DROP PROCEDURE IF EXISTS channelconnector.getOrderDetailsTracking;

DELIMITER $$
CREATE  PROCEDURE channelconnector.getOrderDetailsTracking(
	IN `fby_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256)
)
BEGIN
	/*
		CALL `channelconnector`.`getOrderDetailsTracking`(8, '*************');
    */
	
	SELECT 
		od.`id`,
		od.`channel`,
		od.`channel_code`,
		od.`owner_code`,
		od.`fby_user_id`,
		od.`account_id`,
		od.`order_no`,
		od.`location_id`,
		od.`seller_order_id`,
		od.`purchase_date`,
		od.`payment_time`,
		od.`order_line_item_id`,
		od.`sku`,
		od.`barcode`,
		od.`order_item_id`,
		od.`transaction_id`,
		od.`product_name`,
		od.`brand`,
		od.`available_stock`,
		od.`quantity_purchased`,
		od.`currency`,
		od.`exchange_rate`,
		od.`item_price`,
		od.`line_item_price`,
		od.`item_tax`,
		od.`item_total_tax`,
		od.`promotion_discount`,
		od.`item_total_price`,
		od.`item_total_ship_price`,
		od.`tracking_courier`,
		od.`tracking_id`,
		od.`tracking_url`,
		od.`is_trackable`,
		od.`payment_status`,
		od.`order_status`,
		od.`cancel_reason`,
		od.`is_canceled_fby`,
		od.`count`,
		od.`fby_error_flag`,
		od.`cron_name`,
		od.`cron_id`,
		od.`status`,
		od.`IsCheckAfter48Hours`,
		od.`checked_at`,
		od.`created_at`,
		od.`updated_at`
	FROM
		`channelconnector`.`order_details` AS od
	WHERE
		od.fby_user_id = fby_id
			AND od.order_no = in_order_no
			AND od.is_trackable = 1
			AND od.status = 0;
END$$
DELIMITER ;
