DROP PROCEDURE IF EXISTS channelconnector.fbyCronErrorManage;

DELIMITER $$
CREATE PROCEDURE channelconnector.fbyCronErrorManage
(
	`in_crn_name` VARCHAR(60), 
	`in_crnid` VARCHAR(100),
	`in_err_type` VARCHAR(100), 
	`in_err_msg` TEXT, 
	`in_fby_id` VARCHAR(128), 
	`in_exist` TINYINT(4) UNSIGNED
)
BEGIN
	/*
	call channelconnector.fbyCronErrorManage
    (
    'in_crn_name',
    'in_crnid',
    'in_err_type',
    'in_err_msg',
    '14',
    '0'
    );
    Select * from bulk_process_error_log;
    */
	SET SQL_SAFE_UPDATES = 0;
    IF in_exist = 1 THEN
		UPDATE bulk_process_error_log AS cl 
		SET 
			cl.count = cl.count + 1,
			cl.type_error = in_err_type,
			cl.error_message = in_err_msg
		WHERE
			cl.cron_id = in_crnid
			AND cl.cron_name = in_crn_name;
    ELSE
		
		INSERT INTO bulk_process_error_log
        (
			fby_user_id,
			cron_name,
			cron_id,
			type_error,
			error_message
        ) 
        VALUES
        (
			in_fby_id,
            in_crn_name,
            in_crnid,
            in_err_type,
            in_err_msg
		);
        
    END IF;
END$$
DELIMITER ;
