DROP  PROCEDURE IF EXISTS channelconnector.getEbayOrderDetailTracking;

DELIMITER $$
CREATE PROCEDURE channelconnector.`getEbayOrderDetailTracking`
(
	IN `in_fby_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256),
	IN `in_channel` VARCHAR(128),
)
BEGIN
SELECT * FROM order_details 
    WHERE fby_user_id = in_fby_id 
    AND order_no = in_order_no 
    AND is_trackable = 1
    AND channel = in_channel
    AND order_details.status = 0 
    GROUP BY order_no 
    LIMIT 1;
END$$
DELIMITER ;
