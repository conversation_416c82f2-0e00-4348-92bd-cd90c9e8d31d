-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `tracking_carrier_master_mapping`
--

DROP TABLE IF EXISTS `tracking_carrier_master_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracking_carrier_master_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` int DEFAULT NULL,
  `mirakl_carrier_code` varchar(256) DEFAULT NULL,
  `fby_mapped_tracking_carrier_code` varchar(256) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fby_user_id` (`fby_user_id`,`mirakl_carrier_code`,`fby_mapped_tracking_carrier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tracking_carrier_master_mapping`
--

LOCK TABLES `tracking_carrier_master_mapping` WRITE;
/*!40000 ALTER TABLE `tracking_carrier_master_mapping` DISABLE KEYS */;
INSERT INTO `tracking_carrier_master_mapping` VALUES (1,37,'UPS','UPS','2023-06-15 15:22:09','2023-06-15 15:22:09'),(2,37,'DHL','DHL','2023-06-15 15:22:09','2023-06-15 15:22:09'),(3,37,'TNT IT','TNT','2023-06-15 15:22:09','2023-06-15 15:22:09'),(4,37,'GLS','GLS','2023-06-15 15:22:10','2023-06-15 15:22:10'),(5,37,'FEDEX','FDX','2023-06-15 15:22:10','2023-06-15 15:22:10'),(6,37,'SDA','SDA','2023-06-15 15:22:10','2023-06-15 15:22:10'),(7,37,'BRT','BRT','2023-06-15 15:22:10','2023-06-15 15:22:10'),(8,37,'RHENUS',NULL,'2023-06-15 15:22:10','2023-06-15 15:22:10'),(9,37,'CMO','CLS','2023-06-15 15:22:10','2023-06-15 15:22:10'),(10,37,'CRO','CHR','2023-06-15 15:22:11','2023-06-15 15:22:11'),(11,37,'DPD','DPD','2023-06-15 15:22:11','2023-06-15 15:22:11'),(12,50,'swisspost','SWP','2023-06-15 15:22:11','2023-06-15 15:22:11'),(13,50,'dpd','DPD','2023-06-15 15:22:11','2023-06-15 15:22:11'),(14,50,'dhl','DHL','2023-06-15 15:22:11','2023-06-15 15:22:11'),(15,50,'ups','UPS','2023-06-15 15:22:11','2023-06-15 15:22:11'),(16,50,'tnt','TNT','2023-06-15 15:22:11','2023-06-15 15:22:11'),(17,50,'fiege',NULL,'2023-06-15 15:22:12','2023-06-15 15:22:12'),(18,50,'LaPost_Colissimo','LPS','2023-06-15 15:22:12','2023-06-15 15:22:12'),(19,50,'asendia','ASN','2023-06-15 15:22:12','2023-06-15 15:22:12'),(20,50,'chronopost','CHR','2023-06-15 15:22:12','2023-06-15 15:22:12'),(21,50,'Planzer',NULL,'2023-06-15 15:22:12','2023-06-15 15:22:12'),(22,50,'quickpac',NULL,'2023-06-15 15:22:12','2023-06-15 15:22:12'),(23,50,'fedex','FDX','2023-06-15 15:22:13','2023-06-15 15:22:13'),(24,50,'postnord',NULL,'2023-06-15 15:22:13','2023-06-15 15:22:13');
/*!40000 ALTER TABLE `tracking_carrier_master_mapping` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:28:45
