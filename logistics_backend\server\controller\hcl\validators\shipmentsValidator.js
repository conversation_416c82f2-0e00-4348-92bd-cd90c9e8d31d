const CONSTANT = require("../../../../misc/constants.js");
const Joi = require('joi');

const modes = CONSTANT.SHIPPING.MODE_ARRAY;
//const operations = Object.values(CONSTANT.WALLET.OPERATION);

const shipmentCostSchema = Joi.object({
    sourcePincode: Joi.string().required(),
    destinationPincode: Joi.string().required(),
    weight: Joi.number().positive().required(),
    height: Joi.number().positive().required(),
    width: Joi.number().positive().required(),
    length: Joi.number().positive().required(),
    mode: Joi.string().valid(...modes).required(),
    orderType: Joi.string().allow(''),
    isCod: Joi.boolean().default(false)
});

  // Define the validation schema
const multiPackageShipmentCostSchema = Joi.object({
  sourcePincode: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      "string.pattern.base": "Source pincode must be a 6-digit number",
      "any.required": "Source pincode is required"
    }),

  destinationPincode: Joi.string()
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      "string.pattern.base": "Destination pincode must be a 6-digit number",
      "any.required": "Destination pincode is required"
  }),

  orderType: Joi.string().allow(''),

  mode: Joi.string()
    .valid(...modes)
    .required()
    .messages({
      "any.only": "Mode must be one of 'Air', 'Surface', 'Rail', or 'Express'",
      "any.required": "Mode is required"
    }),

  isCod: Joi.boolean().default(false),

  packageDetails: Joi.array()
    .items(
      Joi.object({
        packageId: Joi.number()
          .integer()
          .positive()
          .required()
          .messages({
            "number.base": "packageId must be a number",
            "number.integer": "packageId must be an integer",
            "number.positive": "packageId must be a positive number",
            "any.required": "packageId is required"
          }),

        dimensions: Joi.object({
          length: Joi.number()
            .positive()
            .required()
            .messages({
              "number.base": "Length must be a number",
              "number.positive": "Length must be a positive number",
              "any.required": "Length is required"
            }),
          width: Joi.number()
            .positive()
            .required()
            .messages({
              "number.base": "Width must be a number",
              "number.positive": "Width must be a positive number",
              "any.required": "Width is required"
            }),
          height: Joi.number()
            .positive()
            .required()
            .messages({
              "number.base": "Height must be a number",
              "number.positive": "Height must be a positive number",
              "any.required": "Height is required"
            }),
          unitMeasurement: Joi.string()
            .valid("cms")
            .required()
            .messages({
              "any.only": "Unit measurement must be either 'cms'",
              "any.required": "Unit measurement is required"
            })
        }).required(),

        weight: Joi.object({
          value: Joi.number()
            .positive()
            .required()
            .messages({
              "number.base": "Weight must be a number",
              "number.positive": "Weight must be a positive number",
              "any.required": "Weight is required"
            }),
          unitMeasurement: Joi.string()
            .valid("KG")
            .required()
            .messages({
              "any.only": "Weight unit must be 'KG'",
              "any.required": "Weight unit is required"
            })
        }).required()
      })
    )
    .min(1)
    .required()
    .messages({
      "array.min": "At least one package must be provided",
      "any.required": "Package details are required"
    })
});
 
module.exports = {shipmentCostSchema, multiPackageShipmentCostSchema};