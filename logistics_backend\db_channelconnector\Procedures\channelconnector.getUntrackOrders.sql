DROP PROCEDURE IF EXISTS channelconnector.getUntrackOrders;

DEL<PERSON>ITER $$
CREATE  PROCEDURE channelconnector.getUntrackOrders(
	`in_fby_id` VARCHAR(128),
    `in_channel` VARCHAR(128)
)
BEGIN
	/*
    
	call channelconnector.getUntrackOrders (23,'shopify');
    
    */
    DECLARE in_channel_name varchar(256);
    SET in_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	SELECT DISTINCTROW
		CASE
			WHEN in_channel_name LIKE '%presta%'
					
			THEN
				TRIM(seller_order_id)
			ELSE TRIM(order_no) END AS 
		order_no,
        owner_code
    FROM order_details 
     
	WHERE 
		fby_user_id = in_fby_id 
		#AND channel = in_channel 
		AND is_trackable = 0  ;
    -- GROUP BY 
		-- order_no,
        -- owner_code;
END$$
DELIMITER ;
