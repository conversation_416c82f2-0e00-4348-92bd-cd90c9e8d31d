DROP PROCEDURE IF EXISTS channelconnector.getProductByDomainForLocationSync;

DELIMITER $$
CREATE PROCEDURE channelconnector.`getProductByDomainForLocationSync`
(
	IN `fby_id` INT, 
    IN `dom` VARCHAR(64)
)
BEGIN
	/*
    call channelconnector.getProductByDomainForLocationSync (1002,'shopping170.myshopify.com');
    */
	SELECT 
		P.* 
	FROM products as P
    WHERE 
		P.fby_user_id = fby_id 
		AND P.domain = dom 
        AND P.location_id = 0;
END$$
DELIMITER ;
