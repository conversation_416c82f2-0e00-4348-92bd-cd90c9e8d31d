const Joi = require('joi');
const CONSTANT = require('../../../../misc/constants.js');

const transactionTypes = Object.values(CONSTANT.WALLET.TRANSACTION_TYPE);
const operations = Object.values(CONSTANT.WALLET.OPERATION);

const rechargeWalletSchema = Joi.object({
    clientId: Joi.number().integer().required(),
    amount: Joi.number().positive().required(),
    transactionType:Joi.string().valid(...transactionTypes).required(),
    transactionReference: Joi.string().max(255).required(),
    operation: Joi.string().valid(...operations).required(),
});

const validateRechargeWallet= (data) => {
    return rechargeWalletSchema.validate(data);
};

const walletTransactionSchema = Joi.object({
    clientId: Joi.number().integer().required(),
    amount: Joi.number().positive().required(),
    transactionType:Joi.string().valid(...transactionTypes).required(),
    awbNumber: Joi.string().allow(''),
    transactionReference: Joi.string().max(255).required(),
    operation: Joi.string().valid(...operations).required(),
    createdBy: Joi.number().integer().min(0).optional(),
});

const validateWalletTransaction= (data) => {
    return walletTransactionSchema.validate(data);
};

const getWalletTransactionsSchema = Joi.object({
    clientId: Joi.number().integer().required(),
    awb:Joi.string().allow(''),
    fromDate:Joi.date().allow(''),
    toDate:Joi.date().allow(''),
    pageNum: Joi.number().integer().allow(''),
    pageSize: Joi.number().integer().allow(''),
});
const validateGetWalletTransaction = (data) => {
    return getWalletTransactionsSchema.validate(data);
};

const getWalletBalancesSchema = Joi.object({
    pageNum: Joi.number().integer().allow(''),
    pageSize: Joi.number().integer().allow(''), 
});
const validateGetWalletBalances = (data) => {
    return getWalletBalancesSchema.validate(data);
};


module.exports = { validateRechargeWallet, validateWalletTransaction, validateGetWalletTransaction, validateGetWalletBalances};
