import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCog, faTrash, faUpload, faPlus, faEdit, faTimes } from '@fortawesome/free-solid-svg-icons';
import { InputText } from 'primereact/inputtext';
import { Card, CardContent, Typography, IconButton, Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'; // Import Material-UI components
import ReactQuill from 'react-quill'; // Import ReactQuill
import 'react-quill/dist/quill.snow.css';

// Media Component
export const MediaComponent = ({ product, handleImageChange }) => {
    const handleFileChange = (e) => {
        const file = e.target.files[0];
        handleImageChange(file);
    };

    return (
        <div className="media">
            <h4>Media</h4>
            <div className="media-box">
                <label htmlFor="imageUpload">
                    <FontAwesomeIcon icon={faUpload} size="3x" />
                    <p>Upload an image or drag & drop it</p>
                    <input type="file" id="imageUpload" accept="image/*" onChange={handleFileChange} />
                </label>
                {product?.newProduct?.photos && product?.newProduct?.photos.map((photo, index) => (
                    <img key={index} src={photo.image} alt={`Product Image ${index + 1}`} width="100" height="100" />
                ))}
            </div>
        </div>
    );
};

// TitleAndDescriptionComponent
export const TitleAndDescriptionComponent = ({ product, handleFieldChange }) => {
    return (
        <div className="title-description">
            <div>
                <h4>Title</h4>
                <input type="text" value={product?.newProduct?.fields?.title || ''} onChange={handleFieldChange} name="title" />
            </div>
            <div style={{ marginTop: '20px' }}>
                <h4>Description</h4>
                <ReactQuill
                    theme="snow"
                    value={product?.newProduct?.fields?.short_description || product?.newProduct?.fields?.description || ''}
                    onChange={(value) => handleFieldChange({ target: { name: 'description', value } })}
                    style={{ minHeight: '100px', height: '100px', marginBottom: '50px' }}
                />
            </div>
        </div>
    );
};

// Inventory Component
export const InventoryComponent = ({ product, handleFieldChange }) => {
    const hasVariants = product?.newProduct?.variants?.length > 0;

    return (
        <div className="inventory">
            <h4>Inventory</h4>
            {hasVariants && (
                <div style={{
                    padding: '12px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '4px',
                    marginBottom: '16px',
                    fontSize: '0.9em',
                    color: '#1565c0'
                }}>
                    <strong>Note:</strong> These values will be used as defaults for new variants.
                    Individual variant prices and quantities can be customized in the Variants section below.
                </div>
            )}
            <div className="inventory-row">
                <div className="inventory-item">
                    <label htmlFor="price">Base Price:</label>
                    <input
                        type="number"
                        step="0.01"
                        value={product?.newProduct?.fields?.price || ''}
                        onChange={handleFieldChange}
                        id="price"
                        name="price"
                        placeholder="0.00"
                    />
                </div>
                <div className="inventory-item">
                    <label htmlFor="quantity">Base Quantity:</label>
                    <input
                        type="number"
                        value={product?.newProduct?.fields?.quantity || ''}
                        onChange={handleFieldChange}
                        id="quantity"
                        name="quantity"
                        placeholder="0"
                    />
                </div>
                <div className="inventory-item">
                    <label htmlFor="sku">Base SKU Code:</label>
                    <input
                        type="text"
                        value={product?.newProduct?.fields?.sku || ''}
                        onChange={handleFieldChange}
                        id="sku"
                        name="sku"
                        placeholder="Required"
                        required
                    />
                </div>
            </div>
            <div className="inventory-row">
                <div className="inventory-item">
                    <label htmlFor="barcode">Barcode:</label>
                    <input
                        type="text"
                        value={product?.newProduct?.fields?.barcode || ''}
                        onChange={handleFieldChange}
                        id="barcode"
                        name="barcode"
                        placeholder="Optional"
                    />
                </div>
                <div className="inventory-item">
                    <label htmlFor="asin">ASIN:</label>
                    <input
                        type="text"
                        value={product?.newProduct?.fields?.asin || ''}
                        onChange={handleFieldChange}
                        id="asin"
                        name="asin"
                        placeholder="Optional"
                    />
                </div>
            </div>
        </div>
    );
};

export const VariantTable = ({ product, handleVariantChange, openDialog }) => {
    const handleVariantFieldChange = (index, field, value) => {
        // Also update inventory_quantity when quantity changes
        if (field === 'quantity') {
            handleVariantChange(index, 'inventory_quantity', value);
        }
        handleVariantChange(index, field, value);
    };

    return (
        <div className="variant-table">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h4>Product Variants ({product.newProduct.variants.length})</h4>
                <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => openDialog(null)}
                    startIcon={<FontAwesomeIcon icon={faCog} />}
                >
                    Manage Options
                </Button>
            </div>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                        <Table stickyHeader>
                            <TableHead>
                                <TableRow>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Variant</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Price ($)</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Quantity</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>SKU Code</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Barcode</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {product.newProduct.variants.map((variant, index) => (
                                    <TableRow key={index} hover>
                                        <TableCell>
                                            <div style={{ fontWeight: 'medium', color: '#333' }}>
                                                {variant?.title || `Variant ${index + 1}`}
                                            </div>
                                            {variant.option1 && (
                                                <div style={{ fontSize: '0.8em', color: '#666', marginTop: '4px' }}>
                                                    {variant.option1}{variant.option2 && ` • ${variant.option2}`}{variant.option3 && ` • ${variant.option3}`}
                                                </div>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <TextField
                                                type="number"
                                                size="small"
                                                value={variant.price || ''}
                                                onChange={(e) => handleVariantFieldChange(index, 'price', e.target.value)}
                                                placeholder="0.00"
                                                inputProps={{
                                                    step: "0.01",
                                                    min: "0",
                                                    style: { textAlign: 'right' }
                                                }}
                                                sx={{ width: '100px' }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TextField
                                                type="number"
                                                size="small"
                                                value={variant.quantity || variant.inventory_quantity || ''}
                                                onChange={(e) => handleVariantFieldChange(index, 'quantity', e.target.value)}
                                                placeholder="0"
                                                inputProps={{
                                                    min: "0",
                                                    style: { textAlign: 'right' }
                                                }}
                                                sx={{ width: '80px' }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TextField
                                                type="text"
                                                size="small"
                                                value={variant.sku || ''}
                                                onChange={(e) => handleVariantFieldChange(index, 'sku', e.target.value)}
                                                placeholder="Auto-generated"
                                                sx={{ width: '140px' }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TextField
                                                type="text"
                                                size="small"
                                                value={variant.barcode || ''}
                                                onChange={(e) => handleVariantFieldChange(index, 'barcode', e.target.value)}
                                                placeholder="Optional"
                                                sx={{ width: '120px' }}
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
                {product.newProduct.variants.length > 0 && (
                    <Grid item xs={12}>
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#f8f9fa',
                            borderRadius: '4px',
                            fontSize: '0.9em',
                            color: '#666'
                        }}>
                            <strong>Tips:</strong>
                            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                <li>SKUs are auto-generated but can be customized</li>
                                <li>Set individual prices and quantities for each variant</li>
                                <li>Barcodes are optional but recommended for inventory tracking</li>
                            </ul>
                        </div>
                    </Grid>
                )}
            </Grid>
        </div>
    );
};

export const VariantsComponent = ({ product, handleVariantChange }) => {
    const [dialogVisible, setDialogVisible] = useState(false);
    const [variantOptions, setVariantOptions] = useState(['']);
    const [variantValues, setVariantValues] = useState(['']);
    const [options, setOptions] = useState([{ name: '', values: [''] }]);

    const [variants, setVariants] = useState([]);
    const [variantsAdded, setVariantsAdded] = useState(false);
    const [currentVariantIndex, setCurrentVariantIndex] = useState(null);
    const [addOptionCount, setAddOptionCount] = useState(0); // Track the number of times "Add Option" button is clicked

    const openDialog = (index = null) => {
        setDialogVisible(true);
    };

    const closeDialog = () => {
        setDialogVisible(false);
    };
    const addVariantOption = () => {
        if (addOptionCount < 2) { // Allow adding up to 3 options total (0, 1, 2)
            setOptions(prevOptions => [...prevOptions, { name: '', values: [''] }]);
            setAddOptionCount(prevCount => prevCount + 1);
        }
    };

    const handleOptionNameChange = (index, value) => {
        setOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
            updatedOptions[index].name = value;
            return updatedOptions;
        });
    };

    const handleValueChange = (optionIndex, valueIndex, value) => {
        setOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
            updatedOptions[optionIndex].values[valueIndex] = value;
            return updatedOptions;
        });
    };
    // const handleVariantChange = (index, field, value) => {
    //     const updatedVariants = [...variants];
    //     updatedVariants[index][field] = value;
    //     setVariants(updatedVariants);
    // };

    const removeVariantOption = (index) => {
        setOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
            updatedOptions.splice(index, 1);
            return updatedOptions;
        });
    };

    // Helper function to generate unique SKU for variant
    const generateVariantSku = (baseSku, variantTitle) => {
        if (!baseSku) return '';

        // Create a short code from variant title
        const variantCode = variantTitle
            .replace(/[^a-zA-Z0-9]/g, '') // Remove special characters
            .substring(0, 6) // Take first 6 characters
            .toUpperCase();

        return `${baseSku}-${variantCode}`;
    };

    // Helper function to generate all variant combinations
    const generateVariantCombinations = (validOptions) => {
        if (validOptions.length === 0) return [];

        if (validOptions.length === 1) {
            return validOptions[0].values.map(value => [value]);
        }

        if (validOptions.length === 2) {
            const combinations = [];
            validOptions[0].values.forEach(value1 => {
                validOptions[1].values.forEach(value2 => {
                    combinations.push([value1, value2]);
                });
            });
            return combinations;
        }

        if (validOptions.length === 3) {
            const combinations = [];
            validOptions[0].values.forEach(value1 => {
                validOptions[1].values.forEach(value2 => {
                    validOptions[2].values.forEach(value3 => {
                        combinations.push([value1, value2, value3]);
                    });
                });
            });
            return combinations;
        }

        return [];
    };

    const handleSave = () => {
        const newVariants = [];

        // Validate that we have at least one option with values
        if (options.length === 0) {
            alert('Please add at least one option before saving.');
            return;
        }

        // Check if we have valid options with values
        const validOptions = options.filter(option =>
            option.name && option.values && option.values.length > 0 && option.values[0] !== ''
        );

        if (validOptions.length === 0) {
            alert('Please add valid option names and values before saving.');
            return;
        }

        // Get base SKU from main product
        const baseSku = product?.newProduct?.fields?.sku || '';
        const basePrice = parseFloat(product?.newProduct?.fields?.price || 0);

        // Generate all possible combinations
        const combinations = generateVariantCombinations(validOptions);

        combinations.forEach((combination, index) => {
            const variantTitle = combination.join(' • ');
            const variantSku = generateVariantSku(baseSku, variantTitle);

            const variant = {
                price: basePrice, // Start with base product price
                barcode: '', // User can fill this later
                sku: variantSku, // Auto-generated unique SKU
                quantity: parseInt(product?.newProduct?.fields?.quantity || 0), // Start with base quantity
                inventory_quantity: parseInt(product?.newProduct?.fields?.quantity || 0),
                title: variantTitle,
                option1: combination[0] || '',
                option2: combination[1] || '',
                option3: combination[2] || '',
                position: index + 1
            };
            newVariants.push(variant);
        });

        handleVariantChange(newVariants, validOptions);
        closeDialog();
        setVariantsAdded(true);
    };

    // Function to add a new value to an option
    const addValue = (optionIndex) => {
        setOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
            updatedOptions[optionIndex].values.push('');
            return updatedOptions;
        });
    };

    // Function to remove a value from an option
    const removeValue = (optionIndex, valueIndex) => {
        setOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
            updatedOptions[optionIndex].values.splice(valueIndex, 1);
            return updatedOptions;
        });
    };

    return (
        <div>
            {(variantsAdded || product.newProduct.variants.length > 0) ? (
                <VariantTable product={product} handleVariantChange={handleVariantChange} openDialog={openDialog} />
            ) : (
                <div className="variant-box">
                    <button className="add-variant-button" onClick={openDialog}>
                        <FontAwesomeIcon icon={faPlus} /> Add your first variant
                    </button>
                </div>
            )}
            <Dialog open={dialogVisible} onClose={closeDialog} fullWidth maxWidth="sm">
                <div className="dialog-content">
                    <div className="title-bar">
                        <Typography variant="h6" className="title">Manage Variants</Typography>
                        <IconButton onClick={closeDialog} className="cancel-button">
                            <FontAwesomeIcon icon={faTimes} />
                        </IconButton>
                    </div>
                    <div className="current-variants" style={{ marginBottom: '16px' }}>
                        <Typography variant="body1" style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                            Configure Product Options
                        </Typography>
                        <Typography variant="body2" style={{ color: '#666' }}>
                            Add options like Size, Color, Material, etc. Each combination will create a unique variant.
                        </Typography>
                    </div>
                    <div className="option-fields">
                        {options.map((option, optionIndex) => (
                            <Card key={optionIndex} className="option-card" style={{ marginBottom: '16px' }}>
                                <CardContent>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                                        <Typography variant="subtitle1" style={{ fontWeight: 'bold' }}>
                                            Option {optionIndex + 1}
                                        </Typography>
                                        {options.length > 1 && (
                                            <IconButton
                                                onClick={() => removeVariantOption(optionIndex)}
                                                size="small"
                                                color="error"
                                            >
                                                <FontAwesomeIcon icon={faTrash} />
                                            </IconButton>
                                        )}
                                    </div>

                                    <TextField
                                        fullWidth
                                        size="small"
                                        label="Option Name"
                                        value={option.name}
                                        onChange={(e) => handleOptionNameChange(optionIndex, e.target.value)}
                                        placeholder="e.g., Size, Color, Material"
                                        style={{ marginBottom: '16px' }}
                                    />

                                    <Typography variant="body2" style={{ marginBottom: '8px', color: '#666' }}>
                                        Option Values:
                                    </Typography>

                                    <Grid container spacing={1}>
                                        {option.values.map((value, valueIndex) => (
                                            <Grid item xs={12} sm={6} key={valueIndex}>
                                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                    <TextField
                                                        size="small"
                                                        fullWidth
                                                        value={value}
                                                        onChange={(e) => handleValueChange(optionIndex, valueIndex, e.target.value)}
                                                        placeholder={`Value ${valueIndex + 1}`}
                                                    />
                                                    {option.values.length > 1 && (
                                                        <IconButton
                                                            onClick={() => removeValue(optionIndex, valueIndex)}
                                                            size="small"
                                                            color="error"
                                                        >
                                                            <FontAwesomeIcon icon={faTrash} size="sm" />
                                                        </IconButton>
                                                    )}
                                                </div>
                                            </Grid>
                                        ))}
                                        <Grid item xs={12}>
                                            <Button
                                                onClick={() => addValue(optionIndex)}
                                                variant="outlined"
                                                size="small"
                                                startIcon={<FontAwesomeIcon icon={faPlus} />}
                                                style={{ marginTop: '8px' }}
                                            >
                                                Add Value
                                            </Button>
                                        </Grid>
                                    </Grid>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Variant Preview Section */}
                    {options.some(option => option.name && option.values.some(value => value)) && (
                        <div style={{
                            marginTop: '20px',
                            padding: '16px',
                            backgroundColor: '#f8f9fa',
                            borderRadius: '8px',
                            border: '1px solid #e9ecef'
                        }}>
                            <Typography variant="subtitle1" style={{ fontWeight: 'bold', marginBottom: '12px' }}>
                                Variant Preview
                            </Typography>
                            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                                {(() => {
                                    const validOptions = options.filter(option =>
                                        option.name && option.values && option.values.length > 0 && option.values[0] !== ''
                                    );
                                    const combinations = generateVariantCombinations(validOptions);
                                    const baseSku = product?.newProduct?.fields?.sku || 'SKU';

                                    return combinations.map((combination, index) => {
                                        const variantTitle = combination.join(' • ');
                                        const variantSku = generateVariantSku(baseSku, variantTitle);

                                        return (
                                            <div key={index} style={{
                                                padding: '8px 12px',
                                                margin: '4px 0',
                                                backgroundColor: 'white',
                                                borderRadius: '4px',
                                                border: '1px solid #dee2e6',
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            }}>
                                                <span style={{ fontWeight: 'medium' }}>{variantTitle}</span>
                                                <span style={{
                                                    fontSize: '0.85em',
                                                    color: '#666',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {variantSku}
                                                </span>
                                            </div>
                                        );
                                    });
                                })()}
                            </div>
                            <Typography variant="caption" style={{ color: '#666', marginTop: '8px', display: 'block' }}>
                                {(() => {
                                    const validOptions = options.filter(option =>
                                        option.name && option.values && option.values.length > 0 && option.values[0] !== ''
                                    );
                                    const combinations = generateVariantCombinations(validOptions);
                                    return `${combinations.length} variant${combinations.length !== 1 ? 's' : ''} will be created`;
                                })()}
                            </Typography>
                        </div>
                    )}

                    <div className="button-group" style={{ marginTop: '20px' }}>
                        <Button
                            onClick={addVariantOption}
                            variant="outlined"
                            color="primary"
                            className="add-option-button"
                            disabled={addOptionCount >= 2}
                            startIcon={<FontAwesomeIcon icon={faPlus} />}
                        >
                            Add Option {addOptionCount >= 2 && '(Max 3)'}
                        </Button>
                        <Button
                            onClick={handleSave}
                            variant="contained"
                            color="primary"
                            className="save-button"
                            disabled={!options.some(option => option.name && option.values.some(value => value))}
                        >
                            Create Variants
                        </Button>
                    </div>
                </div>
            </Dialog>
        </div>
    );
};


// Organize & Classify Component
export const OrganizeAndClassifyComponent = ({ product, handleFieldChange }) => {
    return (
        <div className="organize-and-classify">
            <h4>Organize & Classify</h4>
            <div className="row">
                <div className="column">
                    <label htmlFor="brand">Brand:</label>
                    <input type="text" id="brand" name="brand" value={product.newProduct.fields.brand || ''} onChange={handleFieldChange} />
                </div>
                <div className="column">
                    <label htmlFor="category">Category:</label>
                    <input type="text" id="category" name="categories" value={product.newProduct.fields.categories || ''} onChange={handleFieldChange} />
                </div>
            </div>
            <div className="row">
                <div className="column">
                    <label htmlFor="tags">Tags:</label>
                    <input type="text" id="tags" name="tags" value={product.newProduct.fields.tags || ''} onChange={handleFieldChange} />
                </div>
            </div>
        </div>
    );
};

// Weight & Dimensions Component
export const WeightAndDimensionsComponent = ({ product, handleFieldChange }) => {
    const [selectedDimensionUnit, setSelectedDimensionUnit] = useState(product.newProduct.fields.dimensions.unit || '');
    const [selectedWeightUnit, setWeightSelectedUnit] = useState(product.newProduct.fields.weight.unit || '');

    const handleWeightChange = (e) => {
        const { name, value } = e.target;
        const updatedWeight = {
            ...product.newProduct.fields.weight,
            [name]: value
        };
        handleFieldChange({
            target: {
                name: 'weight',
                value: updatedWeight
            }
        });
    }

    const handleDimensionChange = (e) => {
        const { name, value } = e.target;
        const updatedDimensions = {
            ...product.newProduct.fields.dimensions,
            [name]: value
        };
        handleFieldChange({
            target: {
                name: 'dimensions',
                value: updatedDimensions
            }
        });
    };

    const handleUnitChange = (e) => {
        const selectedUnitValue = e.target.value;
        setSelectedDimensionUnit(selectedUnitValue);
        handleFieldChange({
            target: {
                name: 'dimensions',
                value: {
                    ...product.newProduct.fields.dimensions,
                    unit: selectedUnitValue
                }
            }
        });
    };

    const handleWeightUnitChange = (e) => {
        const selectedUnitValue = e.target.value;
        setWeightSelectedUnit(selectedUnitValue);
        handleFieldChange({
            target: {
                name: 'weight',
                value: {
                    ...product.newProduct.fields.weight,
                    unit: selectedUnitValue
                }
            }
        });
    };

    return (
        <div className="weight-and-dimensions">
            <h4>Weight & Dimensions</h4>
            <div className="row">
                <div className="column">
                    <label>Weight:</label>
                    <div className="dimensions-input">
                        <input type="text" id="value" name="value" value={product.newProduct.fields.weight.value} onChange={handleWeightChange} />
                        <select name="unit" value={selectedWeightUnit} onChange={handleWeightUnitChange}>
                            <option value="">Select Unit</option>
                            <option value="pounds" selected={selectedWeightUnit === "pounds"}>Pounds</option>
                            <option value="grams" selected={selectedWeightUnit === "grams"}>Grams</option>
                            <option value="kilograms" selected={selectedWeightUnit === "kilograms"}>Kilograms</option>
                        </select>
                    </div>
                </div>
                <div className="column">
                    <label>Dimensions:</label>
                    <div className="dimensions-input">
                        <input type="text" id="width" name="width" placeholder="Width" value={product.newProduct.fields.dimensions.width} onChange={handleDimensionChange} />
                        <input type="text" id="height" name="height" placeholder="Height" value={product.newProduct.fields.dimensions.height} onChange={handleDimensionChange} />
                        <input type="text" id="length" name="length" placeholder="Length" value={product.newProduct.fields.dimensions.length} onChange={handleDimensionChange} />
                        <select name="unit" value={selectedDimensionUnit} onChange={handleUnitChange}>
                            <option value="">Select Unit</option>
                            <option value="inches" selected={selectedDimensionUnit === "inches"}>Inches</option>
                            <option value="feet" selected={selectedDimensionUnit === "feet"}>Feet</option>
                            <option value="yards" selected={selectedDimensionUnit === "yards"}>Yards</option>
                            <option value="millimeters" selected={selectedDimensionUnit === "millimeters"}>Millimeters</option>
                            <option value="centimeters" selected={selectedDimensionUnit === "centimeters"}>Centimeters</option>
                            <option value="meters" selected={selectedDimensionUnit === "meters"}>Meters</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    );
};