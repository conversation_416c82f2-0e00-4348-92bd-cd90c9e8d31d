/* jobPage.css */
.job-page {
    display: flex;
    margin-left: 255px;
    margin-top: 60px;
    background: var(--surface-card);
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.button-group {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .form-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .filter-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .search-container {
    display: flex;
    align-items: center;
  }
  
  .search-container svg {
    margin-right: 5px;
  }
  
  @media (max-width: 600px) {
    .form-container {
      flex-direction: column;
    }
  }
  
  @media (max-width: 400px) {
    .form-container input {
      width: 100%;
    }
  }
  

