DROP PROCEDURE IF EXISTS channelconnector.fbyProductErrorManage;

DELIMITER $$
CREATE PROCEDURE `fbyProductErrorManage`
(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_sku` VARCHAR(128), 
	IN `in_exist` TINYINT(4) UNSIGNED, 
	IN `in_cron_name` VARCHAR(60), 
	IN `in_cron_id` VARCHAR(100), 
	IN `in_error_type` VARCHAR(60), 
	IN `in_error_msg` TEXT, 
	IN `in_time` DATETIME
)
BEGIN
    IF in_exist = 1 THEN
    SET SQL_SAFE_UPDATES = 0;
      UPDATE products AS P 
	  SET 
	  	P.count=P.count+1,
		P.updated_at=in_time
	  WHERE 
	  	P.cron_id=in_cron_id 
	  	AND P.sku=in_sku;
      
      UPDATE cron_error_log AS CL 
	  SET 
	  	CL.type_error=in_error_type,
		CL.error_message=in_error_msg 
	  WHERE 
	  	CL.cron_id=in_cron_id;
    SET SQL_SAFE_UPDATES = 1;
    ELSE
    SET SQL_SAFE_UPDATES = 0;
     UPDATE products AS P 
	 SET 
	 	P.fby_error_flag=1,
		P.count=1,
		P.cron_name=in_cron_name,
		P.cron_id=in_cron_id,
		P.updated_at=in_time
	 WHERE 
	 	P.fby_user_id=in_fby_user_id 
		AND P.sku=in_sku;
    SET SQL_SAFE_UPDATES = 1;
    END IF;
END$$
DELIMITER ;