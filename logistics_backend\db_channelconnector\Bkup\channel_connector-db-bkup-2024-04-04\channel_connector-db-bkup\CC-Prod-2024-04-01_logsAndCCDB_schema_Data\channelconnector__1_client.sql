-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `_1_client`
--

DROP TABLE IF EXISTS `_1_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_1_client` (
  `id` int NOT NULL AUTO_INCREMENT,
  `clientId` varchar(512) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ownerCode` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `isActive` tinyint DEFAULT NULL,
  `createdOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modifiedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_1_client`
--

LOCK TABLES `_1_client` WRITE;
/*!40000 ALTER TABLE `_1_client` DISABLE KEYS */;
INSERT INTO `_1_client` VALUES (3,'57','demo_fby','FDM',1,'2022-02-24 20:59:01','2022-03-01 17:32:26'),(6,'61','Isbag','FIS',1,'2022-02-25 11:14:39','2022-02-25 11:14:39'),(7,'63','Persian Gourmet','FPR',1,'2022-03-04 14:20:02','2022-03-04 14:20:02'),(8,'67','Marsia Skincare','FMS',1,'2022-03-31 10:10:06','2022-10-27 08:45:36'),(9,'68','Golden Days Milano','FGL',1,'2022-04-01 10:17:22','2022-04-01 10:17:22'),(11,'70','Abm Top','FAB',1,'2022-04-19 10:17:22','2022-04-19 10:17:22'),(12,'72','Main Padel','FMP',1,'2022-05-22 13:03:17','2022-11-15 13:22:42'),(13,'73','Adsera MB','FAD',1,'2022-05-30 11:01:03','2022-05-30 11:01:03'),(14,'69','Digitalnow International','FDG',1,'2022-05-31 16:01:03','2022-05-31 16:01:03'),(15,'74','D2C - FBY','YT',1,'2022-07-05 08:50:15','2022-11-10 11:58:32'),(16,'76','Sirt500','FSR',1,'2022-07-19 16:09:51','2022-07-19 16:09:51'),(17,'77','demo_reseller','RDM',1,'2022-08-19 15:51:45','2022-12-06 13:27:09'),(18,'46','demo_fby','FDM',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(19,'47','Isbag','FIS',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(20,'49','Persian Gourmet','FPR',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(21,'53','DocHq','FDC',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(22,'54','Golden Days Milano','FGL',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(23,'55','Digitalnow International','FDG',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(24,'56','Abm Top','FAB',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(25,'58','Cirkolo Roma','FCR',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(26,'59','Adsera MB','FAD',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(27,'60','Peachday','FPC',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(28,'62','Sirt500','FSR',1,'2022-09-19 15:15:01','2022-09-19 15:15:01'),(29,'66','Tostini caffè','FTC',1,'2022-09-29 11:45:59','2022-09-29 11:46:07'),(30,'67','Marsia Skincare','FMS',1,'2022-10-27 08:46:22','2022-10-27 08:46:22'),(31,'78','swimyourstyle','FSW',1,'2023-01-17 13:49:46','2023-01-17 13:49:53'),(32,'84','Gassa D\'Amante','FGD',1,'2023-01-27 08:36:58','2023-01-27 08:36:58'),(33,'87','Poste Italiane','YT',1,'2023-03-23 14:29:22','2023-03-23 14:29:22'),(34,'93','Translated','FTR',1,'2023-05-16 17:44:59','2023-05-16 17:44:59'),(35,'92','Folyawomen','FFW',1,'2023-05-17 07:40:53','2023-05-17 07:40:59'),(36,'94','Intimo Artù','FIA',1,'2023-05-19 10:38:41','2023-05-19 10:38:41'),(37,'95','Ecosh','FEC',1,'2023-06-07 09:50:37','2023-06-07 09:50:37'),(38,'96','Trilussa','FTI',1,'2023-07-20 12:24:45','2023-07-20 12:24:49'),(39,'103','Nakluea','FNK',1,'2023-10-09 11:05:31','2023-10-09 11:05:35');
/*!40000 ALTER TABLE `_1_client` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:26:40
