const dbpool = require('../../startup/db.js');
const miscConstants = require("../../misc/constants.js");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers.js');
const OrderService = require('./orderService.js');
const StatusService = require('./statusService.js');
const ShippingProviderService = require('./shippingProviderService.js');
const PincodeService = require('./pincodeService.js');
const WeightSlabService = require('./weightSlabService.js');
const ClientsRatesService = require('./clientsRatesService.js');
const WalletService = require('./walletService.js');
const { SHIPMENT_TYPES, ORDER_TYPES } = require('../../misc/enums/orderStatusEnum');
const { getShippingService } = require('../shippingPartners/partnerFactory.js');

const hcldb = process.env.INITIAL_CATALOG || "hcl";

class ShipmentService {

    /**
     * 
     * @param {*} orderId 
     * @param {*} users 
     * @returns 
     */
    static async createOrderShipment(orderId, user) 
    {
        try {
            let awb;
            
            //validate order and status
            const { order, statuses, walletBalance } = await this.validateOrderAndGetStatuses(orderId, user.clientId);

            // get shipping provider for the order
            //const shippingProvider = await this.getValidShippingProvider(order);      
            const shippingProvider = {
                id: order?.shippingInfo?.shippingProvoider?.id,
                name: order?.shippingInfo?.shippingProvoider?.name
            };
   
            // create shipment with provider and get the awb
            const awbDetails = await this.createShipmentWithProvider(
                shippingProvider.id,
                shippingProvider.name,
                order
            );
            
            // update shipping details on shipping infor table
            const addShippingDetails = await OrderService.updateShippingDetails({
                orderId,
                shippingProvider,
                awbDetails,
                statuses,
                userId: user.id
            });    
        
             // final response
             return this.prepareOrderShipmentResponse(order, shippingProvider, awbDetails);
          
        } catch (error) {
            throw error;
        }
    }

    /**
     * 
     * @param {*} orderId 
     * @returns 
     */
    static async validateOrderAndGetStatuses(orderId, clientId) {
        try {
            const [order, statuses, walletBalance] = await Promise.all([
                OrderService.getOrder(orderId),
                StatusService.statusesMaster('forward'),
                WalletService.getWalletBalancebyClientId(clientId)
            ]);
    
            if (!order) {
                throw new Error('Order not found');
            }
    
            if (!statuses?.new?.length || !statuses.new[0]?.state) {
                throw new Error('Invalid order status configuration.');
            }
    
            if (order.orderState !== statuses.new[0].state) {
                throw new Error('Order is not ready for shipping');
            }
    
             // Convert wallet balance to a number for proper comparison
            const walletAmount = parseFloat(walletBalance.balance);
            if (isNaN(walletAmount)) {
                throw new Error('Invalid wallet balance received.');
            }

            if (order.shippingInfo.totalShipmentCharges > walletAmount) {
                throw new Error('Wallet balance is insufficient. Please get wallet recharged.');
            }
    
            return { order, statuses, walletBalance };
    
        } catch (error) {
            throw new Error(`Order validation failed: ${error.message}`);
        }
    }


    /**
     * 
     * @param {*} order 
     * @param {*} shippingProvider 
     * @param {*} awbNumber 
     * @returns 
     */
    static prepareOrderShipmentResponse(order, shippingProvider, awbDetails) {
        const enrichedOrder = {
            ...order,
            shippingInfo: {
                ...order.shippingInfo,
                awb: awbDetails.awbNumber,
                tokenNumber: awbDetails?.tokenNumber || '',
                waybillInfo: awbDetails?.waybillInfo || null
            }
        };

        return {
            shippingProvider,
            awb: awbDetails.awbNumber,
            order: enrichedOrder
        };
    }
    
    /**
     * 
     * @param {*} order 
     * @returns 
     */
    static async getValidShippingProvider(order) {
        try {
            const providers = await this.getPriorityShippingProviders(order);
            
            if (!providers?.length) {
                throw new Error('No eligible shipping providers found');
            }

            const providerId = providers[0];
            const providerName = await ShippingProviderService.getShippingProviderName(providerId);

            if (!providerName) {
                throw new Error('Invalid shipping provider');
            }
            return {
                id: providerId,
                name: providerName
            };

        } catch (error) {
            throw new Error(
                `Failed to get shipping provider: ${error.message}`
            );
        }
    }
    /**
     * get priority shipping providers 
     * for the order
     * @param {*} order 
     * @returns 
     */
    static async getPriorityShippingProviders(order) {
        try {       

            const clientId = order.clientId;
            const mode = order.shipmentDetails.express.toLowerCase();
            const orderType = "B2C"; 
            const providerId = 1;
            
            // get effective weight slabs
            const effectiveWeight = await this.getEffectiveWeight(order);
            // get weight slabs
            const weightSlab = WeightSlabService.getWeightSlabFromCache(clientId, mode, effectiveWeight);
         
            //get address details
            const addressDetails = await OrderService.getAddressFromOrder(order);
            if(!addressDetails?.pickUpAddress || !addressDetails?.deliveryAddress) {
                throw new Error('Invalid pickup or destination address');
            }

            const sourcePincode = addressDetails.pickUpAddress.address.zip || '';
            const destinationPincode = addressDetails.deliveryAddress.address.zip || '';

            // get zone of destination pincode
            // get pincode details and zones
            const pincodeDetails = await PincodeService.checkDeliveryServiceability(sourcePincode, destinationPincode, mode, 1);
            if (!pincodeDetails?.isServiceable) {
                const errorMessage = !pincodeDetails?.source?.serviceable ? pincodeDetail?.source?.message 
                                                : !pincodeDetails?.destination?.serviceable ? pincodeDetails?.destination?.message : 'Undefined Error';
                throw new Error(errorMessage);
            }
            //get shipping provider based on the client and weight slab
            const shippingProvoiders = await ShippingProviderService.getShippingProviderByPriority(
                clientId, 
                weightSlab.weightSlabId, 
                pincodeDetails?.destination?.data?.zone, 
                mode, 
                orderType
            );
            return shippingProvoiders;

        } catch (error) {
            throw new Error(`Error in getting shipping priority: ${error.message}`);
        }    
    }


    static async createShipmentWithProvider(providerId, providerName, order) {
        try {
            const providerDetails = await ShippingProviderService.getShippingProviderDeatils(providerId);
            console.log(providerDetails);

            const shippingService = getShippingService(providerDetails, SHIPMENT_TYPES.FORWARD);
            const awbDetails = await shippingService.createShipment(order);

            console.log(awbDetails);

            if (!awbDetails.success) {
                throw new Error(
                    awbDetails.message || 'Failed to create shipment'
                );
            }
            return awbDetails;

        } catch (error) {
            throw new Error(`Shipment creation failed: ${error.message}` );
        }
    }

    /**
     * calculate effective weight
     * @param {*} order 
     * @returns effectiveWeight
     */

    static getEffectiveWeight = async( order ) => {
        try {
            // get weight detail from order
            const { weight, height, width, length } = await OrderService.getWeights(order);
            // Calculate the effective weight
            const effectiveWeight = await this.calculateEffectiveWeight(weight, height, width, length);
            if(!effectiveWeight) {
                throw new Error('No weight found in the order');
            }
            return effectiveWeight;
        } catch (error) {
            throw new Error(`Error in weight calculation: ${error.message}`);
        }       
    }

    /**
     * 
     * @param {*} weight 
     * @param {*} height 
     * @param {*} width 
     * @param {*} length 
     * @returns 
     */
    static async calculateEffectiveWeight(weight, height, width, length) {
        try {
            return await helpers.calculateWeight(weight, height, width, length);
        } catch (error) {
            throw new Error(`Failed to calculate effective weight: ${error.message}`);
        }
    }

    /**
     * calculate shipment cost
     * @param {*} param0 ( clientId, sourcePincode, destinationPincode, weight, eight, width, length, mode, orderType, isCod)
     * @returns ost, weight: effectiveWeight, sourceZone, destinationZone
     */
    static async calculateShipmentCost({
        clientId,
        sourcePincode,
        destinationPincode,
        weight,
        height,
        width,
        length,
        mode = miscConstants.SHIPPING.MODE.SURFACE,
        orderType = ORDER_TYPES.B2C,
        isCod = false
    }) {
        // Business logic validation
        if (!clientId) {
            throw new Error('Client ID is required');
        }
    
        // Additional business rules validation
        if (orderType === ORDER_TYPES.B2C) {          
            if (weight > miscConstants.SHIPPING.MAX_WEIGHT.DEFAULT) {
                throw new Error(`Weight cannot exceed ${miscConstants.SHIPPING.MAX_WEIGHT.DEFAULT} kg`);
            }
    
            if (mode.toLowerCase() === miscConstants.SHIPPING.MAX_WEIGHT.AIR && weight > miscConstants.SHIPPING.MAX_WEIGHT.AIR) {
                throw new Error(`Air shipments cannot exceed ${miscConstants.SHIPPING.MAX_WEIGHT.AIR} kg`);
            }
        } else {
            if (weight < miscConstants.SHIPPING.MIN_WEIGHT.DEFAULT) {
                throw new Error(`Weight cannot be less than ${miscConstants.SHIPPING.MIN_WEIGHT.DEFAULT} kg`);
            }
        }
    
        try {
            // Get client's enabled shipping providers
            const clientProviders = await ClientsRatesService.getEnabledProviders(clientId);
            //console.log(clientProviders);
            if (!clientProviders || clientProviders.length === 0) {
                return { isServiceable: false, shippingRates: [], pincodeDetails: null };
            }
    
            // Get effective weight based on volumetric calculations
            const effectiveWeight = await this.calculateEffectiveWeight(weight, height, width, length);
            const shippingRates = [];
    
            // Iterate through enabled providers and fetch their rates
            for (const provider of clientProviders) {
                const { providerId, providerName } = provider;
    
                // Get pincode serviceability details
                // console.log(sourcePincode);
                // console.log(destinationPincode);
                const pincodeDetails = await PincodeService.checkDeliveryServiceability(
                    sourcePincode, 
                    destinationPincode, 
                    mode,
                    orderType,
                    providerId
                );
    
               // console.log(pincodeDetails);

                if (!pincodeDetails?.isServiceable) {
                    continue; // Skip non-serviceable providers
                }
    
              //  console.log(destinationPincode);

                const zoneId = pincodeDetails?.destination?.data?.zone;
    
                // Get base shipping rates from client_zone_rates
                const shippingDetails = await this.calculateShippingCharges({
                    clientId,
                    providerId,
                    effectiveWeight,
                    zoneId,
                    mode,
                    orderType,
                    isCod
                });
    
                if (!shippingDetails || !shippingDetails.shippingCharges) {
                    console.log(`No rates found for provider: ${providerName}`);
                    continue;
                }
    
                // Calculate additional rates (excluding COD in additional rates)
                const { additionalCharge, codCharge } = await this.calculateAdditionalRates(
                    clientId, 
                    providerId, 
                    orderType, 
                    shippingDetails.shippingCharges
                );
    
                // Final cost including shipping, additional, and optional COD charges
                const shippingCharge = shippingDetails.shippingCharges + additionalCharge;
                const finalCost = shippingCharge + (isCod ? codCharge : 0);
    
                // Store all possible rates
                shippingRates.push({
                    providerId,
                    porviderOption: `Option ${shippingRates.length + 1}`, // Dynamically naming options
                    providerName,
                    cost: finalCost,
                    shippingCharges: shippingCharge,
                    codCharges: isCod ? codCharge : 0,
                    weight: effectiveWeight,
                    weightSlabId: shippingDetails.weightSlabId,
                    weightSlab: shippingDetails.weightSlab,
                    zone: zoneId,
                    isServiceable: true,
                    pincodeDetails: pincodeDetails
                });
            }
    
            if (shippingRates.length === 0) {
                return { isServiceable: false, shippingRates: [] };
            }
    
            shippingRates.sort((a, b) => a.cost - b.cost);

            return {
                isServiceable: true,
                shippingRates
            };
    
        } catch (error) {
            console.error("Error calculating shipment cost:", error);
            throw error;
        }
    }
    
    /**
     * 
     * @param {*} param0 
     * @returns 
     */

    static async calculateShippingCharges({
        clientId, providerId, effectiveWeight, zoneId, mode, orderType, isCod
    }) {
        try {
            const rates = await ClientsRatesService.getRatesBasedOnWeightZone(
                clientId, providerId, effectiveWeight, zoneId, mode, orderType, isCod
            );
    
            if (!rates) {
                throw new Error('No rate card found for the given parameters');
            }
    
            const shippingCharges = await helpers.calculateShippingCost(
                effectiveWeight,
                rates.weightSlab,
                rates.rate,
                rates.additionalWeight,
                rates.additionalRate,
                rates.additionalWeightExtra,
                rates.additionalRateExtra
            );
    
            return {
                shippingCharges,
                weightSlabId: rates.weightSlabId,
                weightSlab: rates.weightSlab
            };
        } catch (error) {
            throw new Error(`Failed to calculate shipping charges: ${error.message}`);
        }
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} shippingCost 
     * @returns 
     */
    // static async calculateCodCharges(clientId, shippingCost) {
    //     try {
    //         const codRate = await ClientsRatesService.getCodRateByClient(clientId);
            
    //         if (!codRate) {
    //             throw new Error(
    //                 'COD rates not available for this client'
    //             );
    //         }

    //         return helpers.calculateCodCost(
    //             codRate.codAmount,
    //             codRate.codPercentage,
    //             shippingCost
    //         );
    //     } catch (error) {
    //         throw new ShipmentCalculationError(
    //             `Failed to calculate COD charges: ${error.message}`
    //         );
    //     }
    // }

    static async calculateAdditionalRates(clientId, providerId, orderType, baseShippingCharge) {
        try {
            const additionalRates = await ClientsRatesService.getClientAdditionalRates(clientId, providerId, orderType);
            if (!additionalRates || additionalRates.length === 0) return { additionalCharge: 0, codCharge: 0 };
    
            let totalAdditionalCharge = 0;
            let codFixedCharge = null;
            let codPercentage = null;
    
            additionalRates.forEach(rate => {
                const rateValue = parseFloat(rate.rateValue);
                const rateName = rate.rateName.toLowerCase();
    
                if (rateName.includes("reverse")) return; // Skip reverse charges
    
                if (rateName.includes("cod")) {
                    if (rate.rateType === "FIXED") {
                        codFixedCharge = rateValue;
                    } else if (rate.rateType === "PERCENTAGE") {
                        codPercentage = rateValue;
                    }
                } else {
                    // General additional charges (non-COD, non-reverse)
                    if (rate.rateType === "FIXED") {
                        totalAdditionalCharge += rateValue;
                    } else if (rate.rateType === "PERCENTAGE") {
                        totalAdditionalCharge += (rateValue / 100) * baseShippingCharge;
                    }
                }
            });
    
            // Use the calculateCodCost function
            const totalCodCharge = helpers.calculateCodCost(codFixedCharge || 0, codPercentage || 0, baseShippingCharge);
    
            return { additionalCharge: totalAdditionalCharge, codCharge: totalCodCharge };
        } catch (error) {
            console.error("Error calculating additional rates:", error);
            return { additionalCharge: 0, codCharge: 0 };
        }
    }
    
    /**
     * 
     * @param {*} orderId 
     * @param {*} format 
     * @returns 
     */
    static async trackShipment(orderId, format = 'json', awb = null, shippingProviderId = null, shipmentType = 'forward') {
        try {
            let order = '';
            let isOrder = false;

            if(awb) {
                const shippingDetails = await OrderService.getShippingDetailsByAWB(awb);
                console.log(shippingDetails);
                if(shippingDetails) {
                    isOrder = true;  
                    orderId = shippingDetails?.orderId;
                    shippingProviderId = shippingDetails?.shippingProviderId;
                }
            } else if(orderId) {
                order = await OrderService.getOrder(orderId);        
                if(!order) {
                    response = {
                        success: false,
                        isOrder,
                        message: 'Order not found'
                    };
                }else if(helpers.isEmpty(order.shippingInfo)) {
                    response = {
                        success: false,
                        isOrder: true,
                        message: 'Order has not been shipped yet'
                    };
                } else {
                    //if(order.orderState != statuses.new[0].state) throw new Error('Order can not be shipped');  
                    isOrder = true;   
                    shippingProviderId = order.shippingInfo.shippingProvoider.id;
                    awb = order.shippingInfo.awb;
                    shipmentType = order.shipmentType;       
                } 
            }   
            console.log(shippingProviderId);
            // console.log(awb);
            const providerDetails = await ShippingProviderService.getShippingProviderDeatils(shippingProviderId);
            const shippingService = getShippingService(providerDetails, shipmentType);
            const response = await shippingService.trackShipment(awb, format);  
            console.log(response);
            
            if(response.success === true && orderId) {
                if(providerDetails?.providerType?.toUpperCase() === miscConstants.SHIPPING.PROVIDER_TYPE.EXTERNAL) {
                    const orderTrackingUpdated = await OrderService.addTrackingEvents(orderId, response.data.trackingDetails);  
                } 
                order = await OrderService.getOrder(orderId);          
                return {
                    success: response.success,
                    isOrder,
                    ...order,
                    trackHeader: response?.data?.trackHeader || null,
                    trackingDetails: response?.data?.trackingDetails?.length > 0 
                                    ? response.data.trackingDetails  // Get last item
                                    : order?.trackingDetails || null
                };
            } 

            return response;

        } catch(error) {
            throw error;
        }
    }

    static async cancelAwb(orderId) {
        try {
            let response;
            const order = await OrderService.getOrder(orderId);         
            if(!order) {
                response = {
                    success: false,
                    message: 'Order not found'
                };
            }else if(helpers.isEmpty(order.shippingInfo)) {
                response = {
                    success: false,
                    message: 'Order has not been shipped yet'
                };
            } else {
                //if(order.orderState != statuses.new[0].state) throw new Error('Order can not be shipped');    
                const providerId = order.shippingInfo.shippingProvoider.id;
                const awbs = [order.shippingInfo.awb];
                const tokenNumber = order?.shippingInfo?.tokenNumber || '';
                const shipmentType = order?.shipmentType;
                const providerDetails = ShippingProviderService.getShippingProviderDeatils(providerId);
                const shippingService = getShippingService(providerDetails, shipmentType);
                response = await shippingService.cancelAwb(awbs, tokenNumber);
            }    
            return response;
        } catch(error) {
            throw error;
        }        
    }
}    


module.exports = ShipmentService;