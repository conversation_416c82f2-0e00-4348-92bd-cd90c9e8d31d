DROP PROCEDURE IF EXISTS channelconnector.getProductByDomain;

DELIMITER $$
CREATE PROCEDURE channelconnector.getProductByDomain
(
`in_fby_id` INT, 
`in_dom` VARCHAR(64)
)
BEGIN
	/*
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductByDomain (27,'storeden');
    */
	SELECT 
		P.*
	FROM
		channelconnector.products AS P
	INNER JOIN
		channelconnector.temp_master_inventory AS TI ON P.sku = TI.skucode
		AND P.fby_user_id = TI.fby_user_id
        -- AND P.barcode = TI.barcode
	WHERE
		P.inventory_quantity <> P.previous_inventory_quantity
        AND P.fby_user_id = in_fby_id
        
   UNION
   
   SELECT 
		P1.*
	FROM
		channelconnector.products AS P1
		
	WHERE
		LOWER(P1.channel) like 'woo%comm%'
        AND P1.fby_user_id = in_fby_id
        and sku is not null and sku <> ''
        AND P1.inventory_quantity <> P1.previous_inventory_quantity;
END$$
DELIMITER ;
