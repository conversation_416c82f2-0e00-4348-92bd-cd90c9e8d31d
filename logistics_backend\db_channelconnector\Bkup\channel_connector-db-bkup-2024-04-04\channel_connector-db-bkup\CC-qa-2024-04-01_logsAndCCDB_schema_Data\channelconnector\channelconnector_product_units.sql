-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `product_units`
--

DROP TABLE IF EXISTS `product_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_units` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) DEFAULT NULL,
  `sku` varchar(128) DEFAULT NULL,
  `product_type` varchar(128) DEFAULT NULL,
  `brand` varchar(128) DEFAULT NULL,
  `weight_unit` varchar(128) DEFAULT NULL,
  `weight_value` varchar(128) DEFAULT NULL,
  `dimensions_unit` varchar(128) DEFAULT NULL,
  `dimensions_width` varchar(128) DEFAULT NULL,
  `dimensions_height` varchar(128) DEFAULT NULL,
  `dimensions_length` varchar(128) DEFAULT NULL,
  `tags` varchar(128) DEFAULT NULL,
  `category` varchar(128) DEFAULT NULL,
  `asin` varchar(128) DEFAULT NULL,
  `product_status` varchar(128) DEFAULT 'active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_units`
--

LOCK TABLES `product_units` WRITE;
/*!40000 ALTER TABLE `product_units` DISABLE KEYS */;
INSERT INTO `product_units` VALUES (1,'8','redmi','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'active'),(2,'8','realmi','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'active'),(3,'8','swift','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'active'),(11,'8','wood','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'active'),(18,'8','iphone14-pro-max','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'draft'),(19,'8','iphone14-pro','test prod',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'archived'),(33,'8','Test SKU','Test Product','Test Brand','','10','millimeters','10','10','10','Test Tag','Test Categories','TestAsin','active'),(34,'8','New sku','Test Product','New Brand','grams','','meters','10','10','10','New Tags','New categories','asin11','active'),(35,'8','Bulk-1','Test Product','bulk brand','kg','5','m','5','5','5','bulk tags','bulk categ','','active');
/*!40000 ALTER TABLE `product_units` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:52:29
