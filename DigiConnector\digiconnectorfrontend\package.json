{"name": "digiconnectorfrontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@ionic/react": "^7.7.4", "@mui/icons-material": "^5.15.13", "@mui/material": "^5.15.13", "@mui/styles": "^5.15.14", "@reduxjs/toolkit": "^2.2.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.15.3", "axios": "^1.6.7", "bootstrap": "^5.3.3", "chart.js": "^4.4.2", "ionicons": "^7.2.2", "moment": "^2.30.1", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "^10.5.1", "quagga": "^0.12.1", "react": "^18.2.0", "react-barcode": "^1.5.0", "react-bootstrap": "^2.10.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-slick": "^0.30.2", "react-spinners": "^0.13.8", "react-table": "^7.8.0", "react-to-print": "^2.15.1", "react-toastify": "^10.0.4", "react-transition-group": "^4.4.5", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}