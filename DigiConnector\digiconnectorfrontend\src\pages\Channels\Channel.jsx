import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Grid, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';
import { Sidebar } from '../../components/SidePanel/Sidebar';
import { NavBar } from '../../components/Navbar/Navbar';
import ShopifyLogo from '../../Images/shopify.png';
import AmazonLogo from '../../Images/amazon.png';
import WooCommerceLogo from '../../Images/woocommerce.png';
import StordenLogo from '../../Images/amazon.png';
import PrestaShopLogo from '../../Images/prestashop.png';
import eBayLogo from '../../Images/ebay.png';
import MagentoLogo from '../../Images/magento.png';
import EtsyLogo from '../../Images/etsy.png';
import MiraklLogo from '../../Images/mirakl.png';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import './channelPage.css';

const channelDetails = {
    shopify: ShopifyLogo,
    amazon: AmazonLogo,
    woocommerce: WooCommerceLogo,
    storden: StordenLogo,
    prestashop: PrestaShopLogo,
    ebay: eBayLogo,
    magento: MagentoLogo,
    etsy: EtsyLogo,
    mirakl: MiraklLogo
};

export const Channel = () => {
    const navigate = useNavigate();
    const [dialogOpen, setDialogOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [currentChannel, setCurrentChannel] = useState('');

    const handleConnectClick = (channelName) => {
        // Set current channel and open dialog
        setCurrentChannel(channelName);
        setDialogOpen(true);
    };

    const handleClose = () => {
        // Reset form data and close dialog
        setFormData({});
        setDialogOpen(false);
    };

    const handleBack = () => {
        navigate(-1); // Go back to previous page
    };

    const handleConnect = async () => {
        try {
            const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/channel`, {
                action: 'connect',
                channel: currentChannel,
                formData
            },{
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`
                }
            });

            console.log('API response:', response.data);

            // Handle response data as needed
            handleClose(); // Close dialog after connecting
        } catch (error) {
            console.error('Error connecting to channel:', error);
        }
    };

    const handleChange = (e) => {
        // Update form data
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const getDialogContent = () => {
        switch (currentChannel) {
            case 'shopify':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="URL of Store" name="storeUrl" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Token" name="token" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'amazon':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store Name" name="storeName" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="AWS Access key ID" name="accessKeyId" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Client Secret" name="clientSecret" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Market Place Id" name="marketPlaceId" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="AWS Authorization Token" name="awsAuthToken" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Seller/Merchant Id" name="merchantId" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'woocommerce':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store Url" name="storeUrl" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Consumer Key" name="consumerKey" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Consumer Secret" name="consumerSecret" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'storden':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store Name" name="storeName" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Access Token" name="accessToken" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'prestashop':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store URL" name="storeUrl" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="API Key" name="apiKey" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'ebay':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store Name" name="storeName" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Access Token" name="accessToken" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'magento':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store URL" name="storeUrl" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Access Token" name="accessToken" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'etsy':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Store Name" name="storeName" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="API Key" name="apiKey" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            case 'mirakl':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Channel Number" name="fby_user_id" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="Mirakl API URL" name="apiUrl" onChange={handleChange} />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField fullWidth label="API Key" name="apiKey" onChange={handleChange} />
                        </Grid>
                    </Grid>
                );
            default:
                return null;
        }
    };

    const channels = ['shopify', 'amazon', 'woocommerce', 'storden', 'prestashop', 'ebay', 'magento', 'etsy', 'mirakl'];

    return (
        <div className="channel-page">
            <NavBar selectedSidebarItem="channel" />
            <Sidebar />
            <div className="channel-content">
                <Button onClick={handleBack} startIcon={<ArrowBackIcon />}>
                    Back
                </Button>
                <h2>Channels</h2>
                <Grid container spacing={2}>
                    {channels.map((channelName) => (
                        <Grid item xs={12} sm={6} md={4} lg={4} key={channelName} className="channel-item">
                            <img src={channelDetails[channelName]} alt={channelName} className="channel-image" />
                            <div className="button-container">
                                <Button variant="contained" color="primary" onClick={() => handleConnectClick(channelName)}>Connect</Button>
                            </div>
                        </Grid>
                    ))}
                </Grid>
            </div>
            <Dialog open={dialogOpen} onClose={handleClose}>
                <DialogTitle sx={{ fontSize: '26px' }}>Connect to {currentChannel}</DialogTitle>
                <DialogContent>
                    {getDialogContent()}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Cancel</Button>
                    <Button onClick={handleConnect} variant="contained" color="primary">Connect</Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};