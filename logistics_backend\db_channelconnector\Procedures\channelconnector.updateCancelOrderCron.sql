DROP PROCEDURE IF EXISTS channelconnector.updateCancelOrderCron;

DELIMITER $$ 
CREATE PROCEDURE channelconnector.updateCancelOrderCron(
	`in_ordr_no` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	 SET SQL_SAFE_UPDATES = 0;
     
		UPDATE order_details AS od 
		SET 
			od.cron_name = in_crn_name,
			od.cron_id = in_crnid,
			od.updated_at = in_time
		WHERE
			od.order_no = in_ordr_no;


	 SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;