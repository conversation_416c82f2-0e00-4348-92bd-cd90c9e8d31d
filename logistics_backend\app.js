require("./startup/env").check_env();
require("dotenv/config");
var http = require('http');
const moment = require("moment");
const express = require("express");
const swaggerUI = require("swagger-ui-express");
const path = require('path');
const cors = require('cors');
const env = require("./startup/env");
const logger = require('./misc/logger');
const helpers = require('./misc/helpers');
const YAML = require('yamljs');
const fs = require('fs');
const common = require('./server/constants/common');
const CONSTANTS = require("./misc/constants.js");
const fbyService = require('./services/fby_service');
const tests = require('./startup/test');
const swaggerAutogen = require('swagger-autogen')();

// const ShippingProviderService = require('./services/hcl/shippingProviderService');
// const StatusService = require('./services/hcl/statusService.js');
const app = express();

const corsOpts = {
    origin: (origin, callback) => {
        const allowedOrigins = [
            'http://logistics-dev.centralindia.cloudapp.azure.com',
            'http://logistics-uat.centralindia.cloudapp.azure.com',
            'http://*************',
	    'http://*************:5000',
            'http://************',
            'http://localhost',
            'http://localhost:3000',
            'http://localhost:5173',
	    'http://localhost:5173/',
            'http://127.0.0.1'
        ];

        if (allowedOrigins.indexOf(origin) !== -1 || !origin) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    preflightContinue: false,
};

app.use(cors(corsOpts));

app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        return res.sendStatus(200);
    }

    next();
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));


//const parquet = require("parquetjs-lite");

/**
 * This is required to handle api routes from router.js
 * All routers are imported as 'route' from router.js
 */
app.use("/", require("./server/routes/router.js"));
app.use("/client", require("./routes/_1_client/client_routes"));
app.use("/channel", require("./routes/_2_channel/channel_routes"));
app.use("/prestashop", require("./routes/_3_prestashop/prestashop_routes"));
app.use("/ebay", require("./routes/_4_ebay/ebay_routes"));

app.use("/uploads", express.static(CONSTANTS.MEDIA_PATH.UPLOADS_DIR));

const BASE_URL = process.env.BASE_URL;
const ENV = process.env.ENV;
const PORT = process.env.PORT;
global.APP_ROOT = path.resolve(__dirname) + "\\";

var swagger_path = "";
if (ENV == "PROD") {
    swagger_path = path.resolve(__dirname, './swagger-prod.yaml');
}
else {
    swagger_path = path.resolve(__dirname, './swagger.yaml');
}

fs.readFile(swagger_path, 'utf8', function (err, data) {
    if (err) {
        return console.log(err);
    }
    let urls = [
        'http://localhost:3000/',
        'http://localhost:5000/',
        'http://************/',
        'https://************/',
        'http://channelsconnector.yocabe.com/',
        'http://*************/',
        'http://ccposte.yocabe.com/',
    ];

    var result = data;
    urls.forEach(element => {

        element = element.trim();
        if (result.includes(element) == true);
        {
            result = result.replace(element, BASE_URL);
            console.log('Swagger server dropdown set to : ', BASE_URL);

        }
    });


    fs.writeFile(swagger_path, result, 'utf8', function (err) {
        if (err) return console.log(err);
    });

});


setTimeout(async () => {
    console.clear();
    //console.log("swagger_path", swagger_path);
    const swaggerDocument = YAML.load(swagger_path);
    app.use("/swagger", swaggerUI.serve, swaggerUI.setup(swaggerDocument));
    app.listen(PORT);
    console.log(`listening at Port ${PORT}, And swagger url = ${BASE_URL}swagger`);
    await tests.init();
    if (process.env.ENV == "PROD" || process.env.ENV == "LOCAL") {
        console.log("Sheduling Jobs : false , FOR environment : ", ENV);
        const runner = require("./services/cron_jobs/indexDispatcher");
        await runner.initialize_Jobs();
    }
    else {
        //console.log("Sheduling Jobs : false , FOR environment: ", ENV);
    }


}, 2000);

/*
process.on('uncaughtException', function (err) {
    //console.log(err);
    process.exit(0);
});
*/
//
// (async () => {
//     try {
//         await ShippingProviderService.initializeShippingProvidersConfig();
//         await StatusService.initializeDataMasterConfig();
//         console.log('Shipping providers config initialized successfully.');
//     } catch (error) {
//         console.error('Failed to initialize shipping providers config:', error);
//         process.exit(1); // Exit the application if initialization fails
//     }
// })();


const qs = require('qs');
const fetch = require('node-fetch');

process.on('uncaughtException', err => {
    console.error(err && err.stack);
});
