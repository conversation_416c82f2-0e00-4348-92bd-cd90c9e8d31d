Drop Procedure `channelconnector`.`getCreateProductByDomain`;

DELIMITER $$
CREATE PROCEDURE `getCreateProductByDomain`(`in_fby_id` INT, `in_dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductByDomain (1000,'storeden');
    */
	SELECT 
		* 
	FROM CreatProducts 
    WHERE 
		fby_user_id = in_fby_id 
		AND domain = in_dom;
END$$
DELIMITER ;
