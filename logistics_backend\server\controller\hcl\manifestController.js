const helpers = require('../../../misc/helpers');
const logger = require('../../../misc/logger.js');
const miscConstants = require("../../../misc/constants");
const ManifestService = require('../../../services/hcl/manifestService');

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.createManifest = async(req, res) => {
    try {
        const { shippingProviderId, orderIds } = req.body;
        const userId = req.user.id; 
        const clientId = req.user.clientId;
        const organizationId = req.user.organizationId;

        const manifestId = await ManifestService.createManifest(
            shippingProviderId,
            orderIds,
            userId,
            clientId,
            organizationId
        );

        return helpers.sendSuccess(
                res, 
                miscConstants.HTTPSTATUSCODES.CREATED, 
                miscConstants.SUCESSSMESSAGES.INSERT, 
                { manifestId });

    } catch (error) {
       // console.error('Create manifest error:', error);
        logger.logError('Create manifest error:', error);
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
              miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
              error.message, req.query);
    }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getManifestDetails = async(req, res) => {
    try {
        const { manifestId } = req.params;
        const manifestData = await ManifestService.getManifestDetails(manifestId);

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            manifestData);
    } catch (error) {
        //console.error('Get manifest error:', error);
        logger.logError('Get manifest  error:', error);
        return helpers.sendError(
            res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            `Failed to get manifest details : ${error.message}`, req.params
        );
    }
};

exports.getManifestList = async(req, res) => {
    try {
        const { fromDate, toDate } = req.query;
        const clientId = req.user.clientId;
        let organizationId = req.userRoles.isRoleSuperAdmin ? null : req.user.organizationId;

        if (!fromDate || !toDate) {
            return helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORMESSAGES.BAD_REQUEST, 
                req.query);
        }

        const manifests = await ManifestService.getManifests(fromDate, toDate, clientId, organizationId);
        
        if (!manifests) {
            return helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
                miscConstants.ERRORMESSAGES.NOT_FOUND, 
                req.query);
        }

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            manifests);
    } catch (error) {
        logger.logError('Get manifest list error:', error);
        return helpers.sendError(
            res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            `Failed to get manifest list: ${error.message}`, req.query
        );
    }        
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.updateManifestStatus = async(req, res) => {
    try {

        const { manifestId } = req.params;
        const { status } = req.body;
        const userId = req.user.id;

        await ManifestService.updateManifestStatus(manifestId, status, userId);

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.UPDATE, 
            req.body);
    } catch (error) {
        // console.error('Update manifest status error:', error);
        logger.logError('Update manifest status error:', error);
        return helpers.sendError(
            res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            `Failed to update manifest status: ${error.message}`, req.params
        );
        
    }
};
