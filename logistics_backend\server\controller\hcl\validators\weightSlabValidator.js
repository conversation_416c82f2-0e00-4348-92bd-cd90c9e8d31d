const Joi = require('joi');

// Validation schema for upsert client weight slab
const upsertWeightSlabSchema = Joi.object({
    id: Joi.number().integer().allow(null).optional().messages({
        'number.base': 'ID must be a valid number.',
        'number.integer': 'ID must be an integer.',
    }),
    clientId: Joi.number().integer().required().messages({
        'number.base': 'Client ID is required and must be a valid number.',
        'number.integer': 'Client ID must be an integer.',
        'any.required': 'Client ID is a required field.',
    }),
    shippingProviderId: Joi.number().integer().required().messages({
        'number.base': 'Client ID is required and must be a valid number.',
        'number.integer': 'Client ID must be an integer.',
        'any.required': 'Client ID is a required field.',
    }),
    mode: Joi.string().required().messages({
        'string.base': 'Mode must be a string.',
        'any.required': 'Mode is a required field.',
    }),
    baseWeight: Joi.number().precision(2).required().messages({
        'number.base': 'Base weight must be a valid number.',
        'any.required': 'Base weight is a required field.',
    }),
    additionalWeight: Joi.number().precision(2).allow(null, '').messages({
        'number.base': 'Additional weight must be a valid number.',
        'any.required': 'Additional weight is a required field.',
    }),
    additionalWeightExtra: Joi.number().precision(2).allow(null, '').messages({
        'number.base': 'Additional weight must be a valid number.',
        'any.required': 'Additional weight is a required field.',
    }),
    isActive: Joi.boolean().optional().default(true).messages({
        'boolean.base': 'Is active must be a boolean value.',
    }),
});

const getWeightSlabsSchema = Joi.object({
    clientId: Joi.number().integer().allow(''),
    shippingProviderId: Joi.number().integer().allow(''),
    id: Joi.number().integer().allow(''),
    mode: Joi.string().allow(''),
});


// Validation schema for upsert provider weight slab
const upsertProviderWeightSlabSchema = Joi.object({
    id: Joi.number().integer().allow(null).optional().messages({
        'number.base': 'ID must be a valid number.',
        'number.integer': 'ID must be an integer.',
    }),
    shippingProviderId: Joi.number().integer().required().messages({
        'number.base': 'Client ID is required and must be a valid number.',
        'number.integer': 'Client ID must be an integer.',
        'any.required': 'Client ID is a required field.',
    }),
    mode: Joi.string().required().messages({
        'string.base': 'Mode must be a string.',
        'any.required': 'Mode is a required field.',
    }),
    orderType: Joi.string().required().messages({
        'string.base': 'Mode must be a string.',
        'any.required': 'orderType is a required field.',
    }),
    baseWeight: Joi.number().precision(2).required().messages({
        'number.base': 'Base weight must be a valid number.',
        'any.required': 'Base weight is a required field.',
    }),
    additionalWeight: Joi.number().precision(2).allow(null, '').messages({
        'number.base': 'Additional weight must be a valid number.',
        'any.required': 'Additional weight is a required field.',
    }),
    additionalWeightExtra: Joi.number().precision(2).allow(null, '').messages({
        'number.base': 'Additional weight must be a valid number.',
        'any.required': 'Additional weight is a required field.',
    }),
    isActive: Joi.boolean().optional().default(true).messages({
        'boolean.base': 'Is active must be a boolean value.',
    }),
});

const getProviderWeightSlabsSchema = Joi.object({
    shippingProviderId: Joi.number().integer().allow(null, ''),
    mode: Joi.string().allow(null, ''),
    orderType: Joi.string().allow(null, ''),
});

module.exports = { upsertWeightSlabSchema, getWeightSlabsSchema, getProviderWeightSlabsSchema, upsertProviderWeightSlabSchema };
