const dbpool = require('../../startup/db.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants.js");
const cache = require('../../misc/cache.js');
const moment = require('moment');


class CodPayoutService {

    constructor() {
        this.codRemittanceDays = miscConstants.COD_REMITTANCE_DAYS;
    }

    /**
     * Get COD Payout by Order ID
     * @param {number} orderId - The order ID
     * @returns - Returns COD payout record if found, otherwise null
     */
    async getCodPayoutByOrderId(orderId) {
        try {
            const query = `${hcldb}.GetCodPayoutByOrderId`;
            const [rows] = await dbpool.executeProcedure(query, [orderId]);

            return rows.length ? rows[0] : null;
        } catch (error) {
            console.error(`Error fetching COD payout for orderId: ${orderId}`, error);
            throw error;
        }
    }

    async createCodPayoutEntry (clientId, orderId, awb, invoiceNum, codAmount, deliveredAT, organizationId = null) {
        try {
            const query = `${hcldb}.AddCodPayoutEntry`;
            const result = await dbpool.executeProcedure(query, [clientId, orderId, awb, invoiceNum, codAmount, deliveredAT, organizationId]);
            return result;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async listCodPayouts (clientId, filter, page, pageSize) {
        try {
            const cutoffDate = moment().subtract(this.codRemittanceDays, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'); // Default to 3 days
            const query = `${hcldb}.ListCodPayouts`;
            const [totalCount, payouts] = await dbpool.executeProcedure(query, [clientId, filter, cutoffDate, page, pageSize]);
            return {
                payouts,
                totalRecords: totalCount[0].totalRecords
            }; // Extract the result set
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // Log COD Payout
    async logCodPayout (clientId, orderIds, totalPayoutAmount, payoutDate) {
        try {
            const query = `${hcldb}.LogCodPayout`;
            const result = await dbpool.executeProcedure(query, [clientId, orderIds, totalPayoutAmount, payoutDate]);
            return result;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // List COD Payout History
    async listCodPayoutHistory (clientId, page, pageSize) {
        try {
            const query = `${hcldb}.ListCodPayoutHistory`;
            const [totalCount, payoutsHistory] = await dbpool.executeProcedure(query, [clientId, page, pageSize]);
            return {
                payoutsHistory,
                totalRecords: totalCount[0].totalRecords
            }; // Extract the result set
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async getCodPayoutSums(clientId) {
        try {
            console.log(this.codRemittanceDays);
            const cutoffDate = moment().subtract(this.codRemittanceDays, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'); // Default to 3 days
            const query = `${hcldb}.GetCODPayoutSums`;
            // Call stored procedure for COD Payout Sums
            const [results] = await dbpool.executeProcedure(
                query,
                [clientId, cutoffDate]
            );

            console.log(results);
            // Assuming the stored procedure returns an object with sums
            return {
                currentCodSum: results[0]?.currentCodSum || 0,
                futureCodSum: results[0]?.futureCodSum || 0,
                totalCodSum: results[0]?.totalCodSum || 0
            };
        } catch (error) {
            console.error('Error in getCodPayoutSums service:', error);
            throw error;
        }
    }

}

module.exports = new CodPayoutService();