-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `temp_master_price`
--

DROP TABLE IF EXISTS `temp_master_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temp_master_price` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sku_id` varchar(128) DEFAULT NULL,
  `skucode` varchar(128) DEFAULT NULL,
  `barcode` varchar(128) DEFAULT NULL,
  `fullPrice` int DEFAULT NULL,
  `specialPrice` int DEFAULT NULL,
  `priority` int DEFAULT NULL,
  `source` varchar(128) DEFAULT NULL,
  `currency` varchar(128) DEFAULT NULL,
  `itemId` varchar(128) DEFAULT NULL,
  `cron_id` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fby_user_id` int DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `temp_master_price`
--

LOCK TABLES `temp_master_price` WRITE;
/*!40000 ALTER TABLE `temp_master_price` DISABLE KEYS */;
INSERT INTO `temp_master_price` VALUES (1,'7','BN_BRL_WFQ77777_S777_F7','',10,7,10,'nation','EUR','string','5dc6422e-c5cf-4674-b38a-78129a407f1a','2023-05-08 04:41:11',1010,'2023-05-08 04:41:11'),(2,'7','BN_BRL_WFQ14479_S398_F9','',10,7,10,'nation','EUR','SHIT','691fab25-e016-42c4-b3ab-a2b79cc0e63d','2023-06-19 10:27:27',1002,'2023-06-19 10:27:27');
/*!40000 ALTER TABLE `temp_master_price` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:50:59
