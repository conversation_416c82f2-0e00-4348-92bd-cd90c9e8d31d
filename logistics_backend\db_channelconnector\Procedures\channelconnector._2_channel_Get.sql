DROP PROCEDURE IF EXISTS channelconnector._2_channel_Get;
DEL<PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector._2_channel_Get(
	`in_channelId` VARCHAR(20)
)
BEGIN
	
	/*
		# 1st owner must be present in _1_Client table for the channel
        
        #Get by channed id
		call channelconnector._2_channel_Get(1011);
        
        call channelconnector.`getShopifyUser`('1010');
        
        #Get all
		call channelconnector._2_channel_Get('');
        
        call channelconnector._2_channel_Get('dummy-owner-code-001');
        
    */
   
	IF (in_channelId IS NOT NULL AND in_channelId <> '')
	THEN
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId,
                    T1.stockUpdate,
					T1.priceUpdate,
					T1.orderSync,
					T1.productPublish,
                    T1.`amazon_Role`,
					T1.`amazon_MarketPlaceID`,
					T1.`amazon_SellerID`,
					T1.amazon_region
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				T1.`channelId` = `in_channelId` 
				AND T1.`isActive` = 1
			ORDER BY T1.`channelId`;
		ELSE
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId,
					T1.stockUpdate,
					T1.priceUpdate,
					T1.orderSync,
					T1.productPublish,
                    T1.`amazon_Role`,
					T1.`amazon_MarketPlaceID`,
					T1.`amazon_SellerID`,
					T1.amazon_region
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				 T1.`isActive` = 1
			ORDER BY T1.`channelId`;
               
		END IF;
	
END$$
DELIMITER ;
