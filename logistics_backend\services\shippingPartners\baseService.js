// baseService.js
const axios = require('axios');
const xml2js = require('xml2js');

class BaseService {

    constructor(config, type = 'forward', providerDetails) {
      this.config = config;
      this.type = type;
      this.isProduction = 
          process.env.ENV == "PROD"
            ? true
            : false;
      
      this.environment = 
        process.env.ENV == "PROD"
              ? 'production'
              : 'development';

      this.baseUrl = 
        this.isProduction
          ? config.prodUrl
          : config.devUrl;
          
      this.baseTrackUrl = 
        this.isProduction
          ? config.prodUrl
          : config.devUrl;
    }

    async generateJWTToken() {
      throw new Error("Generate JWT Token not implemented.");
    }

    async checkServiceability(pincode) {
      throw new Error("checkServiceability not implemented.");
    }
    
    async createShipment(order) {
      throw new Error("createShipment not implemented.");
    }
    
    async trackShipment(numbers, format = 'xml', queryType = 'awb') {
      throw new Error("trackShipment not implemented.");
    }

    async cancelAwb(awb, awbId, type = '') {
      throw new Error("getAwbStatus not implemented.");
    }

    // Common methods for all shipping partners
    async makeRequest( endpoint, headers, method = 'POST', data = '' ) {
      try {
          const response = await axios({
              method,
              url: endpoint,
              data,
              headers: headers
          });     
          return response.data;
      } catch (error) {
          throw error;
      }
    }

    /**
     * 
     * @returns 
     */
    getHeaders(token) {
      return {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
      };
    }
    /**
     * 
     * @param {*} address 
     * @returns 
     */
    cleanAddress(addressLine) {
      return addressLine ? addressLine.replace(/[^\w\s]/gi, '').trim() : '';
    }

    // Common address formatting
    formatAddress(address) {
      return this.cleanAddress(
          `${address.addressLine1} 
          ${address.addressLine2} 
          ${address.city} 
          ${address.state}-${address.zip}, 
          ${address.country}`
      );
    }

    // Common contact number formatting
    formatContactNumber(number) {
      return number ? number.replace('+91', '') : '';
    }

    /**
     * 
     * @param {*} date 
     * @returns 
     */
    formatDate(date) {
          // Convert input to a Date object if it's not already one
      const dateObj = new Date(date);
      
      // Check if the conversion resulted in a valid date
      if (isNaN(dateObj.getTime())) {
          throw new Error('Invalid date input');
      }
      
      return dateObj.toISOString().split('T')[0];
    }
    /**
     * 
     * @param {*} xmlResponse 
     * @returns 
     */
    async parseXMLResponse(xmlResponse) {
        return new Promise((resolve, reject) => {
            xml2js.parseString(xmlResponse, { explicitArray: false }, (err, result) => {
                if (err) reject(err);
                else resolve(result);
            });
        });
    }
  }
  
  module.exports = BaseService;
  