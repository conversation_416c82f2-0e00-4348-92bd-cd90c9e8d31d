-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 17, 2022 at 01:37 PM
-- Server version: 10.4.21-MariaDB
-- PHP Version: 8.0.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `channelconnector`
--
CREATE DATABASE IF NOT EXISTS `channelconnector` DEFAULT CHARACTER SET utf8 COLLATE utf8_bin;
USE `channelconnector`;

DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS `addAlertCodes`$$
CREATE  PROCEDURE `addAlertCodes` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_alert_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addAlertDomains`$$
CREATE  PROCEDURE `addAlertDomains` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_alert_domains(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addCancelReason`$$
CREATE  PROCEDURE `addCancelReason` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_cancel_reason(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addChannelCodes`$$
CREATE  PROCEDURE `addChannelCodes` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_channel_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addCurrencyCodes`$$
CREATE  PROCEDURE `addCurrencyCodes` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_currency_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addOrderDetails`$$
CREATE  PROCEDURE `addOrderDetails` (`in_chanl` VARCHAR(128), `in_channel_code` VARCHAR(128), `in_ownr_code` VARCHAR(128), `in_fby_id` VARCHAR(128), `in_account_id` INT(11), `in_order_no` VARCHAR(256), `in_location_id` VARCHAR(256), `in_selr_ordr_id` VARCHAR(100), `in_prchse_dt` DATETIME, `in_payment_time` DATETIME, `in_ordr_line_id` VARCHAR(256), `in_sku` VARCHAR(256), `in_barcode` VARCHAR(128), `in_ordr_itm_id` VARCHAR(128), `in_trnsactn_id` VARCHAR(128), `in_prod_nm` VARCHAR(256), `in_qntity_prchsed` INT(11), `in_curncy` VARCHAR(10), `in_exchng_rt` FLOAT, `in_itm_price` DECIMAL(10,2), `in_line_itm_price` DECIMAL(10,2), `in_itm_tx` DECIMAL(10,2), `in_itm_totl_tx` DECIMAL(10,2), `in_promotion_discount` DECIMAL(10,2), `in_item_total_price` DECIMAL(10,2), `in_item_total_ship_price` DECIMAL(10,2), `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100), `in_financial_status` VARCHAR(128), `in_order_status` VARCHAR(128))  BEGIN

DECLARE var_channel_name varchar(200);

SET var_channel_name  = (
	select LOWER(ch.channelName) from _2_channel as ch 
    Where  ch.channelId = in_fby_id
    AND ch.isActive = 1 
    limit 1
);


INSERT INTO
	order_details(
		`channel`,
		channel_code,
		owner_code,
		fby_user_id,
		account_id,
		order_no,
		location_id,
		seller_order_id,
		purchase_date,
		payment_time,
		order_line_item_id,
		sku,
		barcode,
		order_item_id,
		transaction_id,
		product_name,
		quantity_purchased,
		currency,
		exchange_rate,
		item_price,
		line_item_price,
		item_tax,
		item_total_tax,
		promotion_discount,
		item_total_price,
		item_total_ship_price,
		payment_status,
		order_status,
		cron_name,
		cron_id
	)
VALUES
(
		in_chanl,
		in_channel_code,
		in_ownr_code,
		in_fby_id,
		in_account_id,
		in_order_no,
		in_location_id,
		in_selr_ordr_id,
		in_prchse_dt,
		in_payment_time,
		in_ordr_line_id,
		in_sku,
		in_barcode,
		in_ordr_itm_id,
		in_trnsactn_id,
		in_prod_nm,
		in_qntity_prchsed,
		in_curncy,
		in_exchng_rt,
		in_itm_price,
		in_line_itm_price,
		in_itm_tx,
		in_itm_totl_tx,
		in_promotion_discount,
		in_item_total_price,
		in_item_total_ship_price,
		in_financial_status,
		in_order_status,
		in_crn_name,
		in_crn_id
	) 
    ON DUPLICATE KEY
	UPDATE
		`channel` = 	in_chanl,
		channel_code = 	in_channel_code,
		owner_code = 	in_ownr_code,
		fby_user_id = 	in_fby_id,
		account_id = 	in_account_id,
		location_id = 	in_location_id,
		seller_order_id = 	in_selr_ordr_id,
		purchase_date = 	in_prchse_dt,
		payment_time = 	in_payment_time,
		order_line_item_id = 	in_ordr_line_id,
		sku = 	in_sku,
		barcode = 	in_barcode,
		order_item_id = 	in_ordr_itm_id,
		transaction_id = 	in_trnsactn_id,
		product_name = 	in_prod_nm,
		quantity_purchased = 	in_qntity_prchsed,
		currency = 	in_curncy,
		exchange_rate = 	in_exchng_rt,
		item_price = 	in_itm_price,
		line_item_price = 	in_line_itm_price,
		item_tax = 	in_itm_tx,
		item_total_tax = 	in_itm_totl_tx,
		promotion_discount = 	in_promotion_discount,
		item_total_price = 	in_item_total_price,
		item_total_ship_price = 	in_item_total_ship_price,
		payment_status = 	in_financial_status,
		order_status = 	in_order_status,
		cron_name = 	in_crn_name,
		cron_id	= in_crn_id;

	SELECT * from channelconnector.order_details t
    Where t.fby_user_id = in_fby_id
    AND t.order_no = in_order_no
    AND t.seller_order_id = in_selr_ordr_id
    AND t.order_line_item_id = 	in_ordr_line_id
    AND t.order_item_id = 	in_ordr_itm_id
    AND t.sku = 	in_sku
	AND t.barcode = 	in_barcode    ;
    
    SET SQL_SAFE_UPDATES = 0;
    
    IF( var_channel_name like '%storeden%')
    THEN
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.sku = p.sku 
			AND o.fby_user_id = p.fby_user_id
			AND o.order_item_id = p.item_id
			;
    ELSE
		
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.sku = p.sku 
			AND o.fby_user_id = p.fby_user_id
			AND o.order_item_id = p.item_id
			AND p.location_id <> '0'
            AND p.location_id <> ''
            
			AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0');
		END IF;    
	SET SQL_SAFE_UPDATES = 1;

END$$

DROP PROCEDURE IF EXISTS `addOrderMaster`$$
CREATE  PROCEDURE `addOrderMaster` (`in_channel` VARCHAR(256), `in_channel_code` VARCHAR(20), `in_ownr_code` VARCHAR(20), `in_fby_id` VARCHAR(128), `in_account_id` INT(11), `in_order_no` VARCHAR(256), `in_selr_ordr_id` VARCHAR(100), `in_prchse_dt` DATETIME, `in_payment_time` DATETIME, `in_recipient_nm` VARCHAR(256), `in_company` VARCHAR(256), `in_shiper_adr1` VARCHAR(256), `in_shiper_adr2` VARCHAR(256), `in_shiper_city` VARCHAR(256), `in_shiper_state` VARCHAR(64), `in_shiper_state_code` VARCHAR(4), `in_shiper_zip` VARCHAR(256), `in_shiper_country` VARCHAR(256), `in_shiper_country_code` VARCHAR(20), `in_shiper_phone` VARCHAR(256), `in_total_order` DECIMAL(10,2), `in_total_items` INT(11), `in_total_item_price` DECIMAL(10,2), `in_total_ship_price` DECIMAL(10,2), `in_total_tax` DECIMAL(10,2), `in_total_discount` DECIMAL(10,2), `in_payment_id` VARCHAR(256), `in_payment_method` VARCHAR(256), `in_currency_code` VARCHAR(20), `in_buyer_id` VARCHAR(128), `in_buyer_email` VARCHAR(256), `in_buyer_name` VARCHAR(256), `in_sales_record_no` VARCHAR(128), `in_payment_status` VARCHAR(20), `in_order_status` VARCHAR(20), `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100))  BEGIN
	/*
    
		call  channelconnector.`addOrderMaster`(
        
			'in_channel',			#	in_channel,
			'in_channel_code',		#	in_channel_code,
			'in_ownr_code',			#	in_ownr_code,
			'in_fby_id',			#	in_fby_id,
			'123456',				#	in_account_id,
			'in_order_no',			#	in_order_no,
			'in_selr_ordr_id',		#	in_selr_ordr_id,
			 NOW(),					#	in_prchse_dt,
			 NOW(),					#	in_payment_time,
			'in_recipient_nm',		#	in_recipient_nm,
			'in_company',			#	in_company,
			'in_shiper_adr1',		#	in_shiper_adr1,
			'in_shiper_adr2',		#	in_shiper_adr2,
			'in_shiper_city',		#	in_shiper_city,
			'in_shiper_state',		#	in_shiper_state,
			'1',					#	in_shiper_state_code,
			'in_shiper_zip',		#	in_shiper_zip,
			'in_shiper_country',	#	in_shiper_country,
			'1',					#	in_shiper_country_code,
			'in_shiper_phone',		#	in_shiper_phone,
			'1',					#	in_total_order,
			'1',					#	in_total_items,
			'10',					#	in_total_item_price,
			'10',					#	in_total_ship_price,
			'10',					#	in_total_tax,
			'0',					#	in_total_discount,
			'1',					#	in_payment_id,
			'cash',					#	in_payment_method,
			'EUR',					#	in_currency_code,
			'in_buyer_id',			#	in_buyer_id,
			'in_buyer_email',		#	in_buyer_email,
			'in_buyer_name',		#	in_buyer_name,
			'in_sales_record_no',	#	in_sales_record_no,
			'in_payment_status',	#	in_payment_status,
			'in_order_status',		#	in_order_status,
			'in_crn_name',			#	in_crn_name,
			'in_crn_id'				#	in_crn_id

		);
        
        Select * from channelconnector.order_masters
        order by 1 DESC
        limit 10;
        
        
			SET SQL_SAFE_UPDATES = 0;    
			Delete from channelconnector.order_masters
			Where id = 700;
			
			SET SQL_SAFE_UPDATES = 1;    
        
        
    */
	
	INSERT IGNORE INTO order_masters(
		`channel`,	
		channel_code,	
		owner_code	,
		fby_user_id,	
		account_id,	
		order_no,	
		seller_order_id,	
		purchase_date,	
		payment_date,	
		recipient_name,	
		ship_company,	
		ship_address_1,	
		ship_address_2,	
		ship_city,	
		ship_state,	
		ship_state_code,	
		ship_postal_code,	
		ship_country,	
		ship_country_code,	
		ship_phone_number,	
		total_order,	
		total_items,	
		total_items_price,	
		total_shipping_price,	
		total_tax,	
		total_discount,	
		payment_transaction_id,	
		payment_method,	
		currency_code,	
		buyer_id,	
		buyer_email,	
		buyer_name,	
		sales_record_no,	
		payment_status,	
		order_status,	
		cron_name,	
		cron_id
)
VALUES(
	in_channel,	
	in_channel_code,	
	in_ownr_code,	
	in_fby_id,	
	in_account_id,	
	in_order_no,	
	in_selr_ordr_id,	
	in_prchse_dt,	
	in_payment_time,	
	in_recipient_nm,	
	in_company,	
	in_shiper_adr1,	
	in_shiper_adr2,	
	in_shiper_city,	
	in_shiper_state,	
	in_shiper_state_code,	
	in_shiper_zip,	
	in_shiper_country,	
	in_shiper_country_code,	
	in_shiper_phone,	
	in_total_order,	
	in_total_items,	
	in_total_item_price,	
	in_total_ship_price,	
	in_total_tax,	
	in_total_discount,	
	in_payment_id,	
	in_payment_method,	
	in_currency_code,	
	in_buyer_id,	
	in_buyer_email,	
	in_buyer_name,	
	in_sales_record_no,	
	in_payment_status,	
	in_order_status,	
	in_crn_name,	
	in_crn_id	
)
ON DUPLICATE KEY
	UPDATE
	
	purchase_date       =       			in_prchse_dt,
	payment_date       =       			in_payment_time,
	recipient_name       =       			in_recipient_nm,
	ship_company       =       			in_company,
	ship_address_1       =       			in_shiper_adr1,
	ship_address_2       =       			in_shiper_adr2,
	ship_city       =       			in_shiper_city,
	ship_state       =       			in_shiper_state,
	ship_state_code       =       			in_shiper_state_code,
	ship_postal_code       =       			in_shiper_zip,
	ship_country       =       			in_shiper_country,
	ship_country_code       =       			in_shiper_country_code,
	ship_phone_number       =       			in_shiper_phone,
	total_order       =       			in_total_order,
	total_items       =       			in_total_items,
	total_items_price       =       			in_total_item_price,
	total_shipping_price       =       			in_total_ship_price,
	total_tax       =       			in_total_tax,
	total_discount       =       			in_total_discount,
	payment_transaction_id       =       			in_payment_id,
	payment_method       =       			in_payment_method,
	currency_code       =       			in_currency_code,
	buyer_id       =       			in_buyer_id,
	buyer_email       =       			in_buyer_email,
	buyer_name       =       			in_buyer_name,
	sales_record_no       =       			in_sales_record_no,
	payment_status       =       			in_payment_status,
	order_status       =       			in_order_status,
	cron_name       =       			in_crn_name,
	cron_id			= in_crn_id;

    
	SELECT 
		*
	FROM
		order_masters
	WHERE
		order_no = in_order_no
			AND fby_user_id = in_fby_id
			AND `channel` = in_channel
			AND channel_code = in_channel_code
			AND owner_code = in_ownr_code
			AND account_id = in_account_id
			AND seller_order_id = in_selr_ordr_id;
    
END$$

DROP PROCEDURE IF EXISTS `addOrderTracking`$$
CREATE  PROCEDURE `addOrderTracking` (IN `channel_order_id` VARCHAR(256), IN `channel_line_order_id` VARCHAR(256), IN `owner_code` VARCHAR(25), IN `channel_code` VARCHAR(20), IN `provider_order_id` VARCHAR(256), IN `provider_line_order_id` VARCHAR(256), IN `order_date` DATETIME, IN `sku` VARCHAR(256), IN `barcode` VARCHAR(256), IN `tracking_id` VARCHAR(256), IN `ship_date` DATETIME, IN `carrier` VARCHAR(20), IN `url` TEXT, IN `is_return` VARCHAR(20), IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100))  BEGIN
	INSERT IGNORE INTO temp_order_inventory(channel_order_id,channel_line_order_id,owner_code,channel_code,provider_order_id,provider_line_order_id,order_date,sku,barcode,tracking_id,ship_date,carrier,url,is_return,cron_name,cron_id) VALUES(channel_order_id,channel_line_order_id,owner_code,channel_code,provider_order_id,provider_line_order_id,order_date,sku,barcode,tracking_id,ship_date,carrier,url,is_return,crn_name,crn_id);
END$$

DROP PROCEDURE IF EXISTS `addPaymentMethod`$$
CREATE  PROCEDURE `addPaymentMethod` (IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))  BEGIN
INSERT INTO fby_payment_method(name,payment_code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE payment_code=method_code;
END$$

DROP PROCEDURE IF EXISTS `addProduct`$$
CREATE  PROCEDURE `addProduct` (`in_fby_user_id` VARCHAR(128), `in_chanel` VARCHAR(20), `in_domain` VARCHAR(64), `in_ownr_code` VARCHAR(20), `in_sku` VARCHAR(128), `in_barcode` VARCHAR(128), `in_item_id` VARCHAR(127), `in_title` VARCHAR(128), `in_item_product_id` VARCHAR(127), `in_inventory_item_id` VARCHAR(127), `in_old_quantity` INT(11), `in_new_quantity` INT(11), `in_image` TEXT, `in_price` DECIMAL(10,2), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_location_Id` VARCHAR(100))  BEGIN
	 
	SET SQL_SAFE_UPDATES = 0;
	-- SET in_old_quantity = -1;
    SET in_chanel = (
		Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );
    IF(in_inventory_item_id is null) 
    then
		SET in_inventory_item_id = 0;
    END IF;
    
    IF(in_old_quantity is null) 
    then
		SET in_old_quantity = 0;
    END IF;
    
     IF(in_new_quantity is null) 
    then
		SET in_new_quantity = 0;
    END IF;
    
	INSERT INTO
		products(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			price = in_price,
			location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
			previous_inventory_quantity =	in_old_quantity,
			inventory_quantity = in_new_quantity,
            image = in_image
		;
END$$

DROP PROCEDURE IF EXISTS `addStock`$$
CREATE  PROCEDURE `addStock` (IN `in_skuid` VARCHAR(128), IN `in_skucode` VARCHAR(128), IN `in_ean` VARCHAR(128), IN `in_qntity` INT(11), IN `in_priority` INT(11), IN `in_crnid` VARCHAR(100), IN `in_fby_id` VARCHAR(100))  BEGIN
	IF (
        SELECT 1 FROM temp_master_inventory AS T 
		WHERE 
			T.sku_id = `in_skuid` 
		LIMIT 1 
    ) = 1 
    THEN
    SET SQL_SAFE_UPDATES = 0;
		DELETE T FROM temp_master_inventory AS T 
        WHERE 
			T.sku_id = `in_skuid`; 
	SET SQL_SAFE_UPDATES = 1;
    END IF;
    
	INSERT IGNORE INTO temp_master_inventory(
		sku_id,skucode,barcode,quantity,priority,cron_id, 
        fby_user_id
	) 
    VALUES(
		in_skuid,in_skucode,in_ean,in_qntity,in_priority,in_crnid,
        in_fby_id
	);
    
END$$

DROP PROCEDURE IF EXISTS `cronErrorLog`$$
CREATE  PROCEDURE `cronErrorLog` (IN `crn_name` VARCHAR(60), IN `crnid` VARCHAR(100), IN `err_type` VARCHAR(100), IN `err_msg` TEXT, IN `fby_id` VARCHAR(128))  BEGIN

	IF(CHAR_LENGTH(err_msg) > 2048) THEN
		set err_msg = SUBSTRING(err_msg,2048);
    END IF;
    
	INSERT INTO cron_error_log(fby_user_id,cron_name,cron_id,type_error,error_message) VALUES(fby_id,crn_name,crnid,err_type,err_msg);
    
END$$

DROP PROCEDURE IF EXISTS `fbyCanclOrderErrorManage`$$
CREATE  PROCEDURE `fbyCanclOrderErrorManage` (IN `fby_id` VARCHAR(128), IN `ordr_number` VARCHAR(256), IN `skus` VARCHAR(256), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
    IF exist = 1 THEN
      UPDATE order_details AS od SET od.count=od.count+1,od.updated_at=time WHERE od.cron_id=crn_id AND od.order_no=ordr_number AND od.sku=skus;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE order_details AS od SET od.fby_error_flag=1,od.count=1,od.cron_name=crn_name,od.cron_id=crn_id,od.updated_at=time WHERE od.fby_user_id=fby_id AND od.order_no=ordr_number AND od.sku=skus;
    END IF;
END$$

DROP PROCEDURE IF EXISTS `fbyCronErrorManage`$$
CREATE  PROCEDURE `fbyCronErrorManage` (`in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_err_type` VARCHAR(100), `in_err_msg` TEXT, `in_fby_id` VARCHAR(128), `in_exist` TINYINT(4) UNSIGNED)  BEGIN
	/*
	call channelconnector.fbyCronErrorManage
    (
    'in_crn_name',
    'in_crnid',
    'in_err_type',
    'in_err_msg',
    '14',
    '0'
    );
    Select * from bulk_process_error_log;
    */
	SET SQL_SAFE_UPDATES = 0;
    IF in_exist = 1 THEN
		UPDATE bulk_process_error_log AS cl 
		SET 
			cl.count = cl.count + 1,
			cl.type_error = in_err_type,
			cl.error_message = in_err_msg
		WHERE
			cl.cron_id = in_crnid
			AND cl.cron_name = in_crn_name;
    ELSE
		
		INSERT INTO bulk_process_error_log
        (
			fby_user_id,
			cron_name,
			cron_id,
			type_error,
			error_message
        ) 
        VALUES
        (
			in_fby_id,
            in_crn_name,
            in_crnid,
            in_err_type,
            in_err_msg
		);
        
    END IF;
END$$

DROP PROCEDURE IF EXISTS `fbyOrderErrorManage`$$
CREATE  PROCEDURE `fbyOrderErrorManage` (IN `fby_id` VARCHAR(128), IN `ordr_no` VARCHAR(256), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)  BEGIN
    SET SQL_SAFE_UPDATES = 0;
	IF exist = 1 THEN
      UPDATE order_masters AS om SET om.count=om.count+1,om.updated_at=time WHERE om.cron_id=crn_id AND om.order_no=ordr_no;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE order_masters AS om SET om.fby_error_flag=1,om.count=1,om.cron_name=crn_name,om.cron_id=crn_id,om.updated_at=time WHERE om.fby_user_id=fby_id AND om.order_no=ordr_no;
    END IF;
END$$

DROP PROCEDURE IF EXISTS `fbyOrderTrackErrorManage`$$
CREATE  PROCEDURE `fbyOrderTrackErrorManage` (`in_fby_id` VARCHAR(128), `in_ordr_number` VARCHAR(256), `in_exist` TINYINT(4) UNSIGNED, `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100), `in_error_type` VARCHAR(60), `in_error_msg` TEXT, `in_time` DATETIME)  BEGIN
	/*
		
   CALL fbyOrderErrorManage('8','4667998896386',1,'send_Orders_Fby','809d8c84-1ea4-43bc-9a80-72221a89adc5','catch error','{\"4667998896386\":{\"ownerCode\":\"lowner con codice \\\"FDM\\\" non esiste.\"}}','2022-03-03 12:22:02');

	*/
    SET SQL_SAFE_UPDATES = 0;
    
    IF in_exist = 1 
    THEN
   
		UPDATE order_details AS od 
		SET 
			od.count = od.count + 1,
			od.updated_at = NOW()
		WHERE
			od.cron_id = in_crn_id
				AND od.order_no = in_ordr_number
				AND od.is_trackable = 1;
     
		UPDATE cron_error_log AS cl 
		SET 
			cl.type_error = in_error_type,
			cl.error_message = in_error_msg
		WHERE
			cl.cron_id = in_crn_id;
		
	ELSE
    
		UPDATE order_details AS od 
		SET 
			od.fby_error_flag = 1,
			od.count = 1,
			od.cron_name = in_crn_name,
			od.cron_id = in_crn_id,
			od.updated_at = NOW()
		WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_trackable = 1;
	END IF;
    
   
END$$

DROP PROCEDURE IF EXISTS `fbyProductErrorManage`$$
CREATE  PROCEDURE `fbyProductErrorManage` (IN `fby_id` VARCHAR(128), IN `sku` VARCHAR(128), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)  BEGIN
SET SQL_SAFE_UPDATES = 0;
    IF exist = 1 THEN
      UPDATE products AS p SET p.count=p.count+1,p.updated_at=time WHERE p.cron_id=crn_id AND p.sku=sku;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE products AS p SET p.fby_error_flag=1,p.count=1,p.cron_name=crn_name,p.cron_id=crn_id,p.updated_at=time WHERE p.fby_user_id=fby_id AND p.sku=sku;
    END IF;
END$$

DROP PROCEDURE IF EXISTS `getAlertCode`$$
CREATE  PROCEDURE `getAlertCode` (IN `alert` VARCHAR(100), IN `chanel` VARCHAR(50))  BEGIN
SELECT * FROM map_channel_alert_code AS mca WHERE mca.ch_alert_code=alert and mca.channel=chanel;
END$$

DROP PROCEDURE IF EXISTS `getBlukCronByFlag`$$
CREATE  PROCEDURE `getBlukCronByFlag` (IN `casename` VARCHAR(60), IN `dt` DATETIME)  BEGIN
SELECT * FROM bulk_process_error_log WHERE cron_name=casename AND CAST(created_at AS DATE) = CAST(dt AS DATE) AND flag=1 AND count=1 ORDER BY id DESC LIMIT 1;
END$$

DROP PROCEDURE IF EXISTS `getBulkCronLog`$$
CREATE  PROCEDURE `getBulkCronLog` (IN `fby_id` VARCHAR(128), IN `crn_name` VARCHAR(60), IN `dt` DATETIME)  BEGIN
SELECT * FROM bulk_process_error_log WHERE fby_user_id=fby_id AND cron_name=crn_name AND CAST(created_at AS DATE) = CAST(dt AS DATE) ORDER BY id DESC LIMIT 1;
END$$

DROP PROCEDURE IF EXISTS `getCanceledOrderDetails`$$
CREATE  PROCEDURE `getCanceledOrderDetails` (`in_fby_id` VARCHAR(128), `in_acount_id` INT(11), `in_chanel_code` VARCHAR(50), `in_chanel` VARCHAR(100))  BEGIN
	SELECT 
		*
	FROM
		order_details AS od
	WHERE
		(od.payment_status = 'partially_refunded'
			OR od.payment_status = 'refunded'
            OR od.payment_status = '7'
			OR od.payment_status like '%cancel%'
		)
		AND (od.is_canceled_fby = 0 OR od.is_canceled_fby IS NULL)
		-- AND od.channel = in_chanel
		-- AND od.channel_code = in_chanel_code
		-- AND od.account_id = in_acount_id
		AND od.fby_user_id = in_fby_id;
END$$

DROP PROCEDURE IF EXISTS `getCancelOrderByFlag`$$
CREATE  PROCEDURE `getCancelOrderByFlag` (IN `in_casename` VARCHAR(60))  BEGIN
	/*
    call channelconnector.getCancelOrderByFlag('send_Orders_Fby');
    */
    
	 /*SELECT * FROM order_details;# WHERE fby_error_flag=1 AND count=1 AND lower(cron_name) = lower(casename);*/
     SELECT * FROM order_details as OD 
     WHERE OD.fby_error_flag = 1 
     AND OD.count = 1
     AND lower(OD.cron_name) = lower(in_casename);
END$$

DROP PROCEDURE IF EXISTS `getCancelReason`$$
CREATE  PROCEDURE `getCancelReason` (IN `cancel_reason` VARCHAR(100), IN `chanel` VARCHAR(50))  BEGIN
SELECT * FROM map_channel_cancel_reason AS mcc WHERE mcc.ch_cancel_reason=cancel_reason and mcc.channel=chanel;
END$$

DROP PROCEDURE IF EXISTS `getLocationId`$$
CREATE  PROCEDURE `getLocationId` (IN `fby_id` VARCHAR(128), IN `skuid` VARCHAR(256), IN `in_order_item_id` VARCHAR(256))  BEGIN

	/*
		call channelconnector.getLocationId('OSM21A008' ,9,'**************');
    */
	SELECT 
		*
	FROM
		products
	WHERE
		sku = skuid 
		AND fby_user_id = fby_id
		and item_id = in_order_item_id;
END$$

DROP PROCEDURE IF EXISTS `getOrderByAccount`$$
CREATE  PROCEDURE `getOrderByAccount` (IN `in_fby_user_id` VARCHAR(128), IN `in_account_id` VARCHAR(256))  BEGIN
/*,
call channelconnector.getOrderByAccount(8,17);
*/
SET SQL_SAFE_UPDATES = 0;
	SELECT * FROM order_masters AS OM 
	WHERE 
		OM.fby_user_id = in_fby_user_id; 
		-- AND OM.account_id = in_account_id 
		-- AND OM.channel_code = in_channel_code;
		
SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `getOrderByFlag`$$
CREATE  PROCEDURE `getOrderByFlag` (IN `casename` VARCHAR(60))  BEGIN
	 SELECT * FROM order_masters WHERE fby_error_flag=1 AND count=1 AND cron_name=casename;
END$$

DROP PROCEDURE IF EXISTS `getOrderByStatus`$$
CREATE DEFINER=`sa`@`%` PROCEDURE `getOrderByStatus` (IN `in_fby_user_id` VARCHAR(128))  BEGIN
	/*
		call channelconnector.getOrderByStatus(3);
    */
	SELECT * FROM order_masters 
    WHERE 
		(recipient_name IS NOT NULL AND  recipient_name <> '')
        /*
        AND payment_date IS NOT NULL
        */
		AND fby_user_id = in_fby_user_id 
        AND order_masters.fby_send_status = 0;
END$$

DROP PROCEDURE IF EXISTS `getOrderDetails`$$
CREATE  PROCEDURE `getOrderDetails` (IN `fby_id` VARCHAR(128), IN `odr_no` VARCHAR(256))  BEGIN
 SELECT * FROM order_details WHERE fby_user_id=fby_id AND order_no=odr_no;
END$$

DROP PROCEDURE IF EXISTS `getOrderDetailsTracking`$$
CREATE  PROCEDURE `getOrderDetailsTracking` (IN `fby_id` VARCHAR(128), IN `in_order_no` VARCHAR(256))  BEGIN
	/*
		CALL `channelconnector`.`getOrderDetailsTracking`(8, '*************');
    */
	SELECT * FROM order_details 
    WHERE fby_user_id = fby_id 
    AND order_no = in_order_no 
    AND is_trackable = 1 
    AND order_details.status = 0;
END$$

DROP PROCEDURE IF EXISTS `getPaymentMethod`$$
CREATE  PROCEDURE `getPaymentMethod` (IN `in_payment_method` VARCHAR(100), IN `in_chanel` VARCHAR(50))  BEGIN
/*
	CALL getPaymentMethod('NaN','Shopify IT');
	CALL getPaymentMethod('credit_card','Shopify IT');

*/
IF EXISTS (
			SELECT * FROM map_channel_payment_method AS mcp 
            WHERE mcp.ch_payment_method = in_payment_method
				and mcp.channel = in_chanel
) 
THEN 
		SELECT 
			mcp.id,
			mcp.channel,
			mcp.ch_payment_method,
			mcp.fby_payment_method
		FROM
			map_channel_payment_method AS mcp
		WHERE
			mcp.ch_payment_method = in_payment_method
				AND mcp.channel = in_chanel
		ORDER BY id DESC
		LIMIT 1;
 ELSE
	SELECT 
		0 as id,
		in_chanel as channel,
		in_payment_method as ch_payment_method,
		in_payment_method as by_payment_method;
  END IF;          
  
END$$

DROP PROCEDURE IF EXISTS `getProductByDomain`$$
CREATE  PROCEDURE `getProductByDomain` (`in_fby_id` INT, `in_dom` VARCHAR(64))  BEGIN
	/*
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductByDomain (9,'storeden');
    */
	SELECT 
		P.*
	FROM
		channelconnector.products AS P
	INNER JOIN
		channelconnector.temp_master_inventory AS TI ON P.sku = TI.skucode
		AND P.fby_user_id = TI.fby_user_id
        -- AND P.barcode = TI.barcode
	WHERE
		P.inventory_quantity <> P.previous_inventory_quantity
        AND P.fby_user_id = in_fby_id
        ;
END$$

DROP PROCEDURE IF EXISTS `getProductByDomainForLocationSync`$$
CREATE  PROCEDURE `getProductByDomainForLocationSync` (IN `fby_id` INT, IN `dom` VARCHAR(64))  BEGIN
	/*
    call channelconnector.getProductByDomainForLocationSync (8,'shopping170.myshopify.com');
    */
	SELECT 
		* 
	FROM products 
    WHERE 
		fby_user_id = fby_id 
		AND domain = dom 
        AND location_id = 0;
END$$

DROP PROCEDURE IF EXISTS `getProductByFlag`$$
CREATE  PROCEDURE `getProductByFlag` (IN `casename` VARCHAR(60))  BEGIN
	SELECT * FROM products WHERE fby_error_flag=1 AND count=1 AND cron_name=casename;
END$$

DROP PROCEDURE IF EXISTS `getProductBySku`$$
CREATE  PROCEDURE `getProductBySku` (IN `fby_user_id` VARCHAR(128), IN `sku` VARCHAR(128))  BEGIN
	SELECT * FROM products where fby_user_id=fby_user_id AND products.sku=sku;
END$$

DROP PROCEDURE IF EXISTS `getProductByStatus`$$
CREATE  PROCEDURE `getProductByStatus` (`in_fby_user_id` VARCHAR(128))  BEGIN
	/*
    call channelconnector.`getProductByStatus`(1000);
    */
	SELECT * FROM products 
    WHERE 
		lower(fby_user_id) = lower(in_fby_user_id) 
        AND products.`status` = 0;
END$$

DROP PROCEDURE IF EXISTS `getProductForLocation`$$
CREATE  PROCEDURE `getProductForLocation` (IN `fby_id` INT, IN `dom` VARCHAR(64))  BEGIN
	SELECT 
		*
	FROM
		products
	WHERE
		fby_user_id = fby_id 
        AND domain = dom
        AND (location_id = 0 OR location_id IS NULL);
END$$

DROP PROCEDURE IF EXISTS `getShopifyUser`$$
CREATE DEFINER=`sa`@`%` PROCEDURE `getShopifyUser` (IN `in_channelId` VARCHAR(1024))  BEGIN
	/*
    
		call channelconnector.`getShopifyUser`(9);
        
		call channelconnector.`getShopifyUser`(19);
        
        call channelconnector.`getShopifyUser`(1010);
    */
    DECLARE var_orderSyncStartDate datetime;
    DECLARE var_last_orderSyncDateTime datetime;
    DECLARE var_new_orderSyncDateTime datetime;
    DECLARE var_last_ProductSyncDateTime datetime;
    DECLARE var_channel_name varchar(200);

	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_channelId
		AND ch.isActive = 1 
		limit 1
	);

    
    SET var_orderSyncStartDate = (
			SELECT 
				T1.`orderSyncStartDate`
			FROM
				`channelconnector`.`_2_channel` AS T1
			WHERE
				T1.`channelId` = in_channelId
					AND T1.`isActive` = 1
					AND T1.`isEnabled` = 1
			LIMIT 1
	);
    
    SET var_last_orderSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                MAX(T1.`created_at`) 
                #END 
			FROM
				`channelconnector`.`order_details` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    set var_last_ProductSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                date_add(MAX(T1.`created_at`) , INTERVAL -2 day) 
                #END 
			FROM
				`channelconnector`.`products` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    IF(var_last_orderSyncDateTime IS NOT NULL)
    THEN
		/*
		IF(var_channel_name like '%woocom%') THEN
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        ELSE
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -1 day) ;
        END IF;
        */
        IF(date_add(var_last_orderSyncDateTime, INTERVAL -2 day) > var_orderSyncStartDate)
        THEN
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -2 day) ;
        ELSE
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        END IF;
        
    ELSE
		SET var_new_orderSyncDateTime = var_orderSyncStartDate;
    END IF;
        
	SELECT 
			T1.`id` ,
			T1.`channelId` as `fby_user_id`,
			T1.`groupCode` as group_code,
			T1.`currencyCode` as currency_code,
			T1.`ownerCode` as owner_code,
			T1.`channelCode`  as channel_code,
			T1.`channelName` ,
			T1.`domain`  ,
			T1.`username` ,
			T1.`password`  as `api_password`,
            T1.`siteId`  ,
			T1.`compatibilityLevel`  , 
			T1.`ebay_devid`  , 
			T1.`ebay_appid`  , 
			T1.`ebay_certid`  , 
			T1.`ebay_quantity_update_by`  , 
			T1.`apiKey`  as `api_key`,
			T1.`secret` ,
			T1.`token`  ,
			T1.`isActive`  ,
			T1.`isEnabled` ,
			CAST(T1.`createdOn` as CHAR) as created_at,
			CAST(T1.`modifiedOn` as CHAR) as updated_at,
            CAST(var_new_orderSyncDateTime as CHAR) as orderSyncStartDate,
            CAST(var_last_ProductSyncDateTime as CHAR) as productSyncDateTime
    	
	FROM `channelconnector`.`_2_channel` as T1
	WHERE 
		T1.`channelId` = in_channelId
        AND T1.`isActive` = 1
        AND T1.`isEnabled` = 1
        #AND 1 = case when T1.`orderSyncStartDate` is null || (T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()) then 1 else 0 end
        # AND T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()
        LIMIT 1 ;
        
END$$

DROP PROCEDURE IF EXISTS `getUntrackOrders`$$
CREATE  PROCEDURE `getUntrackOrders` (`in_fby_id` VARCHAR(128), `in_channel` VARCHAR(128))  BEGIN
	/*
    
	call channelconnector.getUntrackOrders (23,'shopify');
    
    */
    DECLARE in_channel_name varchar(256);
    SET in_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	SELECT DISTINCTROW
		CASE
			WHEN in_channel_name LIKE '%presta%'
					
			THEN
				TRIM(seller_order_id)
			ELSE TRIM(order_no) END AS 
		order_no,
        owner_code
    FROM order_details 
     
	WHERE 
		fby_user_id = in_fby_id 
		#AND channel = in_channel 
		AND is_trackable = 0  ;
    -- GROUP BY 
		-- order_no,
        -- owner_code;
END$$

DROP PROCEDURE IF EXISTS `get_user`$$
CREATE  PROCEDURE `get_user` (IN `in_user_id` INT)  BEGIN
	/*
		#Retun fby credentials for given user_id by using default credentials 
        call channelconnector.get_user(14);
        
        SELECT * FROM channelconnector._2_channel;
    */
    DECLARE in_channelName VARCHAR(128);
    
    SET in_channelName = (
		SELECT channelName from channelconnector._2_channel
		Where channelId = in_user_id
		order by id desc
		limit 1
    );
    
 	SELECT 
		`id`,
		`name`,
		`email`,
		 in_user_id as `fby_user_id`,
		`auth_username`,
		`auth_password`,
		`created_at`,
		`updated_at`,
        in_channelName as channelName
	FROM 
		`channelconnector`.`users`
	ORDER BY id
	LIMIT 1;
    
END$$

DROP PROCEDURE IF EXISTS `insertCron`$$
CREATE  PROCEDURE `insertCron` (IN `fby_id` VARCHAR(128), IN `crn_name` VARCHAR(60), IN `crnid` VARCHAR(100), IN `stats` TINYINT(4))  BEGIN
	INSERT INTO cron_process_table(fby_user_id,cron_name,cron_id,status) VALUES(fby_id,crn_name,crnid,stats);
END$$

DROP PROCEDURE IF EXISTS `updateCancelOrderCron`$$
CREATE  PROCEDURE `updateCancelOrderCron` (`in_ordr_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	 SET SQL_SAFE_UPDATES = 0;
     
		UPDATE order_details AS od 
		SET 
			od.cron_name = in_crn_name,
			od.cron_id = in_crnid,
			od.updated_at = in_time
		WHERE
			od.order_no = in_ordr_no;


	 SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateCron`$$
CREATE  PROCEDURE `updateCron` (`in_time` DATETIME, `in_crnid` VARCHAR(100))  BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE cron_process_table 
	SET 
		updated_at = in_time,
		status = 0
	WHERE
		cron_id = in_crnid 
        AND status = 1;
        
    SET SQL_SAFE_UPDATES = 1;    
END$$

DROP PROCEDURE IF EXISTS `updateOrder`$$
CREATE  PROCEDURE `updateOrder` (IN `in_crn_name` VARCHAR(60), IN `in_crnid` VARCHAR(100), IN `in_time` DATETIME, IN `in_tracking_id` VARCHAR(256), IN `in_carrier` VARCHAR(256), IN `in_url` TEXT, IN `in_channel_order_id` VARCHAR(256), IN `in_channel_code` VARCHAR(20), IN `in_barcode` VARCHAR(256), IN `in_fby_user_id` VARCHAR(256))  BEGIN
	DECLARE var_channel_name varchar(256);
    SET var_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	/*
    
		call channelconnector.`updateOrder`(
		'get_track_number',
	  '9662f8d6-c031-4f15-bb6e-d0e00c74feab',
	  '2022-02-09 21:22:10',
	  'B12345678904231',
	  'SDA',
	  'https://www.swiship.it/t/D6zCc37CL',
	  'FSHREXMYR',
	  'SHIT',
	  '',
	  23
		)
    
    
    Select * from order_details od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
	 
        
   Select * from order_masters om
    WHERE
		om.order_no = in_channel_order_id;
      #*/ 
    IF(in_barcode = '' OR in_barcode IS NULL)    
    THEN
		SET in_barcode = null;
	ELSE
		SET in_barcode = trim(in_barcode);
    END IF;
    
    SET SQL_SAFE_UPDATES = 0;    
    /*
    SELECT in_channel_code,in_channel_order_id,in_barcode;
    
    SELECT * FROM order_details AS od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
    #*/    
    #SELECT in_tracking_id,in_carrier,in_url,in_barcode,in_channel_order_id as order_no,in_channel_code;
    
	UPDATE order_details AS od 
	SET 
		od.tracking_id = in_tracking_id,
		od.tracking_courier = in_carrier,
		od.tracking_url = in_url,
		od.is_trackable = 1,
		od.cron_name = in_crn_name,
		od.cron_id = in_crnid,
		od.updated_at = NOW(),
		od.barcode = CASE
        WHEN
            in_barcode IS NOT NULL
                AND in_barcode <> ''
        THEN
            TRIM(in_barcode)
        ELSE TRIM(od.barcode)
    END
	WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (
							TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
							OR (TRIM(od.order_no) = TRIM(in_channel_order_id))
						)
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL AND in_barcode<>'' THEN trim(in_barcode) ELSE trim(od.barcode) end;
		
    update order_masters om
    SET om.order_status = "fulfiled",
		om.cron_name = in_crn_name,
		om.cron_id = in_crnid,
		om.updated_at = NOW()
	WHERE
		om.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     
	SELECT * FROM order_details AS od
    WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
					OR (TRIM(od.order_no) = TRIM(in_channel_order_id)))
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
   /*     
	SELECT * FROM order_masters om
    WHERE
		od.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     */
	SET SQL_SAFE_UPDATES = 1;	
END$$

DROP PROCEDURE IF EXISTS `updateOrderCancel`$$
CREATE  PROCEDURE `updateOrderCancel` (`in_fby_user_id` VARCHAR(128), `in_ordr_number` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE order_details AS od 
	SET 
		od.is_canceled_fby = 1,
		od.count = 0,
		od.fby_error_flag = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_user_id
		AND od.order_no = in_ordr_number;
    
	UPDATE order_masters AS om 
	SET 
		om.is_canceled = 1,
		om.fby_send_status = 1,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_user_id
		AND om.order_no = in_ordr_number;
    
    SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateOrderCancelStatus`$$
CREATE  PROCEDURE `updateOrderCancelStatus` (`in_fby_id` VARCHAR(128), `in_ordr_number` VARCHAR(256), `in_financial_status` VARCHAR(20), `in_cancl_reson` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	/*
	CALL updateOrderCancelStatus(8,'4666394706178',"refunded","customer","get_Shopify_Orders","2d404606-c467-4325-98e2-748eca2761f5","2022-03-03 05:12:00");
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details AS od 
	SET 
		od.payment_status = in_financial_status,
		od.order_status = 'canceled',
		od.cancel_reason = in_cancl_reson,
		od.is_canceled_fby = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_canceled_fby IS NULL;
    
	UPDATE order_masters AS om 
	SET 
		om.payment_status = in_financial_status,
		om.order_status = 'canceled',
		om.is_canceled = 0,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_id
			AND om.order_no = in_ordr_number
			AND om.is_canceled <> 1;
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateOrderCron`$$
CREATE  PROCEDURE `updateOrderCron` (`in_ordr_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_masters 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		order_no = in_ordr_no;
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateOrderDetailStatus`$$
CREATE  PROCEDURE `updateOrderDetailStatus` (`in_fby_id` VARCHAR(128), `in_order_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	   SET SQL_SAFE_UPDATES = 0;
       
		UPDATE order_details 
		SET 
			`status` = 1,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = NOW(),
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND is_trackable = 1;
    
		SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateOrderStatus`$$
CREATE  PROCEDURE `updateOrderStatus` (`in_fby_id` VARCHAR(128), `in_order_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME, `in_checktime` DATETIME, `in_isCheckafter48Hr` TINYINT(4))  BEGIN
	/*
    SELECT * FROM 		order_details as o;
    
    call channelconnector.updateOrderStatus(8,'*************','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW());
    
    call channelconnector.updateOrderStatus(8,'4666393428226','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW());
    
    */
	DECLARE is_updated TINYINT;
	DECLARE order_item_count_total INT;
	DECLARE order_item_count_sucess INT;
	DECLARE order_item_count_pending INT;
	
    SET is_updated = 0;
    SET order_item_count_total = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		
	) ;
     SET order_item_count_sucess = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 1
	) ;
     SET order_item_count_pending = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 0
	) ;
    
     
   #IF (order_item_count_pending IS NULL  OR order_item_count_pending = 0 )     
   #THEN  
		SET SQL_SAFE_UPDATES = 0;
		UPDATE order_masters 
		SET 
			fby_send_status = CASE WHEN in_isCheckafter48Hr = 1 THEN 0 ELSE 1 END,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = in_time,
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND fby_send_status = 0;
		 SET is_updated = 1;
		SET SQL_SAFE_UPDATES = 1;
 #END IF;
 
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		checked_at = in_checktime,
		IsCheckAfter48Hours = in_isCheckafter48Hr
	WHERE
		fby_user_id = in_fby_id
		AND order_no = in_order_no;
 
 SELECT 
    is_updated,
    order_item_count_total as order_items_total,
    order_item_count_sucess AS synced,
    order_item_count_pending AS pending;

 
END$$

DROP PROCEDURE IF EXISTS `updateOrderTrackingCron`$$
CREATE  PROCEDURE `updateOrderTrackingCron` (IN `in_ordr_no` VARCHAR(256), IN `in_crn_name` VARCHAR(60), IN `in_crnid` VARCHAR(100), IN `time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = time
	WHERE
		order_no = in_ordr_no AND is_trackable = 1;
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateProdLocation`$$
CREATE  PROCEDURE `updateProdLocation` (`in_fby_id` VARCHAR(128), `in_inventry_id` VARCHAR(127), `in_loc_id` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100), `in_time` DATETIME)  BEGIN
	/*
		CALL channelconnector.updateProdLocation(8,'44437908324610',66372763906,'Get_Shopify_Location','feceac14-421c-411c-9dac-188cb43fb4ba','2022-02-28 14:26:57');
    */
    SET SQL_SAFE_UPDATES = 0;
	UPDATE
		products AS p
	SET
		p.location_id = case when in_loc_id > 0 then in_loc_id else p.location_id  end,
		p.cron_name = in_crn_name,
		p.count = 0,
		p.fby_error_flag = 0,
		p.cron_id = in_crn_id,
		p.updated_at = in_time
	WHERE
		p.inventory_item_id = in_inventry_id
		AND p.fby_user_id = in_fby_id
		AND p.location_id = 0
        AND in_loc_id > 0;
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateProduct`$$
CREATE  PROCEDURE `updateProduct` (`in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME, `in_fby_user_id` VARCHAR(60))  BEGIN
	/*
		call channelconnector.`updateProduct`(
        'send_Products_Fby',
        '22dd4051-1ff6-44d5-b281-fe1ba55e10e7',
        now(),
        1002
        );
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE products AS P,temp_master_inventory AS TI 
	SET 
	
		P.previous_inventory_quantity = P.inventory_quantity,
		P.inventory_quantity = TI.quantity,
		#P.barcode = TI.barcode,
		P.cron_name = in_crn_name,
		P.cron_id = in_crnid,
		P.updated_at = in_time 
	WHERE 
		P.sku = TI.skucode 
        AND 1 = (
        case when P.barcode>0 AND TI.barcode > 0 
			then case when P.barcode = TI.barcode 
				then 1 
                else 0 
			end 
		else 1 end
        
        )
        AND P.fby_user_id = in_fby_user_id
        AND TI.fby_user_id = in_fby_user_id
        AND P.fby_user_id = TI.fby_user_id
		AND (CASE WHEN P.inventory_quantity IS  NULL THEN 0 ELSE P.inventory_quantity  END) <> TI.quantity; 
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateProductAftrSndChanl`$$
CREATE  PROCEDURE `updateProductAftrSndChanl` (`in_fby_id` VARCHAR(128), `in_sku` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE products 
	SET 
		previous_inventory_quantity = inventory_quantity,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = in_time,
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku;
        
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateProductCron`$$
CREATE  PROCEDURE `updateProductCron` (`in_sku` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)  BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE products 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		products.sku = in_sku;
            
	SET SQL_SAFE_UPDATES = 1;
END$$

DROP PROCEDURE IF EXISTS `updateProductStatus`$$
CREATE  PROCEDURE `updateProductStatus` (`in_fby_id` VARCHAR(128), `in_sku` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME, `in_barcode` VARCHAR(128), `in_item_id` VARCHAR(128), `in_item_product_id` VARCHAR(128), `in_inventory_item_id` VARCHAR(128))  BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE products 
	SET 
		status = 1,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = in_time,
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
		AND sku = in_sku
		AND 1 = case when `in_barcode` <> '' AND `in_barcode` <> '0' then case when barcode = `in_barcode` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_id` <> ''  AND `in_item_id` <> '0' then case when item_id = `in_item_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_product_id` <> ''  AND `in_item_product_id` <> '0' then case when item_product_id = `in_item_product_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_inventory_item_id` <> ''  AND `in_inventory_item_id` <> '0' then case when inventory_item_id = `in_inventory_item_id` then 1 else 0 end else 1 end 
		AND status = 0;
END$$

DROP PROCEDURE IF EXISTS `_1_client_Delete`$$
CREATE  PROCEDURE `_1_client_Delete` (`in_clientId` VARCHAR(1024))  BEGIN
	/*
		call channelconnector.`_1_client_Delete`(
			'Id123'
        );
    */
    DECLARE isExists TINYINT; 
    DECLARE in_ownerCode varchar(1024);
    
	SET in_clientId = LOWER(`in_clientId`);
    
    SET in_ownerCode = (
        SELECT LOWER(ownerCode) FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
        LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
         /*   
        UPDATE `channelconnector`.`_2_channel`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(ownerCode) = in_ownerCode 
			AND isActive = 1;    
           */ 
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT   
			clientId,
            `name`,
            ownerCode,
            CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn,
            1 as `isDeleted`
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId 
            ORDER BY `id` DESC
            LIMIT 1;

    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_1_client_Get`$$
CREATE  PROCEDURE `_1_client_Get` (`in_clientId` VARCHAR(1024))  BEGIN
	/*
    
		call channelconnector.`_1_client_Get`(
			''
        );
        
		call channelconnector.`_1_client_Get`(
			'Id123'
        );
        
    */
   SET in_clientId = LOWER(`in_clientId`);
   
   IF(in_clientId IS NULL OR in_clientId ='')
   THEN
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			isActive = 1;
   
   ELSE
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId
			AND isActive = 1;
	END IF;
END$$

DROP PROCEDURE IF EXISTS `_1_client_Post`$$
CREATE  PROCEDURE `_1_client_Post` (`in_clientId` VARCHAR(1024), `in_name` VARCHAR(1024), `in_ownerCode` VARCHAR(1024))  BEGIN
	/*
		call channelconnector.`_1_client_Post`(			'12345',			'Test Name',            'Test Owner Code'        );
        
         call channelconnector.`_1_client_Get`(
			''
        );
        
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
		WHERE 
			LOWER(T.`clientId`) = LOWER(`in_clientId`) 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
        INSERT INTO `channelconnector`.`_1_client`
		(
			`clientId`,
			`name`,
			`ownerCode`,
			`isActive`
		)
		VALUES
		(
			in_clientId,
			in_name,
			in_ownerCode,
			1
		);
        
		call channelconnector.`_1_client_Get`(`in_clientId`);
 
    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_1_client_Put`$$
CREATE  PROCEDURE `_1_client_Put` (`in_clientId` VARCHAR(1024), `in_name` VARCHAR(1024), `in_ownerCode` VARCHAR(1024))  BEGIN
	/*
		call channelconnector.`_1_client_Put`(
			'1234',
			'Test Name1',
            'Test Owner Code2'
        );
        
        call channelconnector.`_1_client_Get`(
			''
        );
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		call channelconnector.`_1_client_Post`(			`in_clientId`,			`in_name`,            `in_ownerCode`        );
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`name` = in_name,
			`ownerCode` = in_ownerCode,
            `modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		call channelconnector.`_1_client_Get`(`in_clientId`);

    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_2_channel_Delete`$$
CREATE  PROCEDURE `_2_channel_Delete` (`in_channelId` INT(11))  BEGIN
	/*
		call channelconnector._2_channel_Get(1002);
        
        call channelconnector.`getShopifyUser`(1002);
    
		call channelconnector.`_2_channel_Delete`(1002);
        
        SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 1,
                `isEnabled` = 1,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = 1002 ;
		SET SQL_SAFE_UPDATES = 1;
        
		
    */
    DECLARE isExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
   
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel  
        WHERE 
			`channelId` = `in_channelId` 
            AND `isActive` = 1 
			LIMIT 1
    );
    
    /*
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    */
    
    IF(isDeletedExists = 1)
    THEN
		DELETE FROM channelconnector._2_channel 
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0;
    END IF;
    
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
		UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 0,
                `isEnabled` = 0,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = `in_channelId` 
				AND `isActive` = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT 
				T1.`channelId` ,
				T1.`groupCode` ,
				T1.`currencyCode` ,
				T1.`ownerCode` ,
				T1.`channelCode`  ,
				T1.`channelName` ,
				T1.`domain`  ,
				T1.`username` ,
				T1.`password`  ,
				T1.`apiKey`  ,
				T1.`secret` ,
				T1.`token`  ,
				T1.`isActive`  ,
				T1.`isEnabled` ,
				CAST(T1.`createdOn` as CHAR) as createdOn,
				CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                1 as Deleted
		FROM
			`channelconnector`.`_2_channel` as T1
		WHERE
			T1.`channelId` = `in_channelId` 
		ORDER BY T1.`id` DESC
		LIMIT 1;
            
    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_2_channel_Get`$$
CREATE DEFINER=`sa`@`%` PROCEDURE `_2_channel_Get` (`in_channelId` VARCHAR(1024))  BEGIN
	
	/*
		# 1st owner must be present in _1_Client table for the channel
        
        #Get by channed id
		call channelconnector._2_channel_Get(1002);
        
        call channelconnector.`getShopifyUser`('1002');
        
        #Get all
		call channelconnector._2_channel_Get('');
        
        call channelconnector._2_channel_Get('dummy-owner-code-001');
        
    */
   
	IF (in_channelId IS NOT NULL AND in_channelId <> '')
	THEN
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				T1.`channelId` = `in_channelId` 
				AND T1.`isActive` = 1
			ORDER BY T1.`channelId`;
		ELSE
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				 T1.`isActive` = 1
			ORDER BY T1.`channelId`;
               
		END IF;
	
END$$

DROP PROCEDURE IF EXISTS `_2_channel_Post`$$
CREATE DEFINER=`sa`@`%` PROCEDURE `_2_channel_Post` (`in_channelId` INT, `in_groupCode` VARCHAR(256), `in_currencyCode` VARCHAR(56), `in_ownerCode` VARCHAR(256), `in_channelCode` VARCHAR(256), `in_channelName` VARCHAR(2048), `in_domain` VARCHAR(2048), `in_username` VARCHAR(2048), `in_password` VARCHAR(2048), `in_apiKey` VARCHAR(2048), `in_secret` VARCHAR(2048), `in_token` VARCHAR(2048), `in_isEnabled` INT, `in_orderSyncStartDate` DATETIME, `in_ebaycompatibilityLevel` VARCHAR(128), `in_ebaydevId` VARCHAR(256), `in_ebayappId` VARCHAR(256), `in_ebaycertId` VARCHAR(256), `in_ebaysiteId` VARCHAR(256))  BEGIN
	/*
		call channelconnector.`_2_channel_Post`(
						1111 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4'
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
    */
    
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    #SET `in_orderSyncStartDate` = NULL;
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel AS T1 
        WHERE 
			T1.`channelId` = `in_channelId` 
			AND T1.isActive = 1 
		LIMIT 1
    );
    
    SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`ownerCode`) = LOWER(`in_ownerCode`) 
            AND T.isActive = 1 
		LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		  
        CALL channelconnector.`_2_channel_put`(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`
        );
       
        
    ELSE
    IF (isClientExists = 0 OR isClientExists IS NULL)
    THEN
		SELECT 1 AS isErrorClientNotFound;
        
    ELSE IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
			   INSERT INTO `channelconnector`.`_2_channel`
				(
					`channelId` ,
					`groupCode` ,
					`currencyCode` ,
					`ownerCode` ,
					`channelCode`  ,
					`channelName` ,
					`domain`  ,
					`username` ,
					`password`  ,
					`apiKey`  ,
					`secret` ,
					`token`  ,
					`isEnabled` ,
                    `isActive`,
					`orderSyncStartDate` ,
                    `compatibilityLevel`,
					ebay_devid,
					ebay_appid,
					ebay_certid,
					siteId
				)
				VALUES
				(
					`in_channelId` ,
					`in_groupCode` ,
					`in_currencyCode` ,
					`in_ownerCode` ,
					`in_channelCode`  ,
					`in_channelName` ,
					`in_domain`  ,
					`in_username` ,
					`in_password`  ,
					`in_apiKey`  ,
					`in_secret` ,
					`in_token`  ,
					`in_isEnabled` ,
                    1 ,
                    `in_orderSyncStartDate`,
                    `in_ebaycompatibilityLevel`,
					`in_ebaydevId`,
					`in_ebayappId`,
					`in_ebaycertId`,
					`in_ebaysiteId`
				);
				
				call channelconnector._2_channel_Get(`in_channelId`);
                
			END IF;
		END IF;
    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_2_channel_Put`$$
CREATE DEFINER=`sa`@`%` PROCEDURE `_2_channel_Put` (`in_channelId` INT, `in_groupCode` VARCHAR(256), `in_currencyCode` VARCHAR(56), `in_ownerCode` VARCHAR(256), `in_channelCode` VARCHAR(256), `in_channelName` VARCHAR(2048), `in_domain` VARCHAR(2048), `in_username` VARCHAR(2048), `in_password` VARCHAR(2048), `in_apiKey` VARCHAR(2048), `in_secret` VARCHAR(2048), `in_token` VARCHAR(2048), `in_isEnabled` INT, `in_orderSyncStartDate` DATETIME, `in_ebaycompatibilityLevel` VARCHAR(128), `in_ebaydevId` VARCHAR(256), `in_ebayappId` VARCHAR(256), `in_ebaycertId` VARCHAR(256), `in_ebaysiteId` VARCHAR(256))  BEGIN
	/*
		call channelconnector.`_2_channel_Put`(
						1030 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4'
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
        
    */
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
		SET 
			`isActive` = 1,
			`isEnabled` = 1,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId`
			AND `isActive` = 0;
		SET SQL_SAFE_UPDATES = 1;
    END IF;
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel
        WHERE 
			`channelId` = `in_channelId`
			AND isActive = 1 
       LIMIT 1     
    );
    
	SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client
        WHERE 
			LOWER(`ownerCode`) = LOWER(`in_ownerCode`) 
            AND isActive = 1 
		LIMIT 1
    );
   
    
	IF (isClientExists = 0 OR isClientExists IS NULL)
	THEN
		SELECT 1 AS isErrorClientNotFound;
        
	ELSE IF (isExists = 0 OR isExists IS NULL)
	THEN
		CALL channelconnector.`_2_channel_post`
		(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`
		);
        
	ELSE
		SET SQL_SAFE_UPDATES = 0;

		UPDATE `channelconnector`.`_2_channel`
		SET
			groupCode = `in_groupCode` ,
			currencyCode = `in_currencyCode` ,
			ownerCode = `in_ownerCode` ,
			channelCode = `in_channelCode`  ,
			channelName = `in_channelName` ,
			domain = `in_domain`  ,
			username = `in_username` ,
			`password` = `in_password`  ,
			apiKey = `in_apiKey`  ,
			secret = `in_secret` ,
			token = `in_token`  ,
			isEnabled = `in_isEnabled` ,
            orderSyncStartDate = `in_orderSyncStartDate`,
            compatibilityLevel = `in_ebaycompatibilityLevel`,
            ebay_devid = `in_ebaydevId`,
            ebay_appid = `in_ebayappId`,
            ebay_certid = `in_ebaycertId`,
            siteId = `in_ebaysiteId`,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId` 
			AND isActive = 1;
		
		SET SQL_SAFE_UPDATES = 1;

		call channelconnector._2_channel_Get(`in_channelId`);
             
	END IF;
    END IF;
    
END$$

DROP PROCEDURE IF EXISTS `_2_getIDforChannelByChannelName`$$
CREATE  PROCEDURE `_2_getIDforChannelByChannelName` (`in_channelname` VARCHAR(200))  BEGIN
	
	
	/*
		call channelconnector.`_2_getIDforChannelByChannelName`('');
        
        call channelconnector.`_2_getIDforChannelByChannelName`('all');
    
		call channelconnector.`_2_getIDforChannelByChannelName`('shopify');
        
		call channelconnector.`_2_getIDforChannelByChannelName`('storeden');
        
    */
    IF(in_channelname = 'all' || in_channelname='')
    THEN
		SELECT 
			T1.*
		FROM
			channelconnector._2_channel T1
		WHERE
			isActive = 1
			AND isEnabled = 1;
    ELSE
		SET in_channelname = lower(concat('%',in_channelname,'%')); 
		SELECT 
			T1.*
		FROM
			channelconnector._2_channel T1
		WHERE
			(
			LOWER(domain) LIKE in_channelname
			OR LOWER(channelName) LIKE in_channelname
			)
			AND isActive = 1
			AND isEnabled = 1;
	END IF; 
END$$

DROP PROCEDURE IF EXISTS `_2_getUserIdForJob`$$
CREATE  PROCEDURE `_2_getUserIdForJob` ()  BEGIN
	/*
    
		call channelconnector.`_2_getUserIdForJob`();
        
		call channelconnector.`_2_getUserIdForJob`();
        
    */
	call channelconnector.`_2_getIDforChannelByChannelName`('shopify');
END$$

DROP PROCEDURE IF EXISTS `_3_winston_logs_clean`$$
CREATE  PROCEDURE `_3_winston_logs_clean` (`in_partition_key` DATE)  BEGIN
	/*
		Select * from `channelconnector`.`winston_logs` order 
        
		Call channelconnector._3_winston_logs_clean('2022-02-28');
                
    */
    SET SQL_SAFE_UPDATES = 0;
	DELETE
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		 CAST(`timestamp` as date)< CAST(`in_partition_key`as date)  ;
    SET SQL_SAFE_UPDATES = 1;    
    
END$$

DROP PROCEDURE IF EXISTS `_3_winston_logs_get`$$
CREATE  PROCEDURE `_3_winston_logs_get` (`in_partition_key` DATE, `in_operation_id` VARCHAR(255))  BEGIN
	/*
		Call channelconnector._3_winston_logs_get('2022-01-27','');
        
        Call channelconnector._3_winston_logs_get('2022-01-27','');
        
    */

	SELECT  
		`partition_key`,
		`operation_id`,
		`level`,
		`message`,
		`meta`,
        CAST(`timestamp` as char) as createdOn
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		`partition_key` = `in_partition_key`
        AND 1 = (case when `in_operation_id` = '' then 1 else case when `operation_id` = `in_operation_id` then 1 else 0 end end);
		


END$$

DROP PROCEDURE IF EXISTS `_3_winston_logs_Post`$$
CREATE  PROCEDURE `_3_winston_logs_Post` (`in_level` VARCHAR(255), `in_message` TEXT, `in_meta` TEXT, `in_operation_id` VARCHAR(255))  BEGIN

	INSERT INTO `channelconnector`.`winston_logs`
	( 
		`level`,
		`message`,
		`meta`,
		`operation_id`
	)
	VALUES
	(
		`in_level` ,
		`in_message`,
		`in_meta`  ,
		`in_operation_id`
	);
    
	SET SQL_SAFE_UPDATES = 0;

	DELETE FROM `channelconnector`.`winston_logs` 
	WHERE
		`timestamp` < DATE_ADD(NOW(), INTERVAL -7 DAY);



END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `bulk_process_error_log`
--

DROP TABLE IF EXISTS `bulk_process_error_log`;
CREATE TABLE IF NOT EXISTS `bulk_process_error_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `type_error` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `error_message` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `count` int(11) NOT NULL DEFAULT 1,
  `flag` tinyint(4) UNSIGNED NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `bulk_process_error_log`
--

TRUNCATE TABLE `bulk_process_error_log`;
-- --------------------------------------------------------

--
-- Table structure for table `cron_error_log`
--

DROP TABLE IF EXISTS `cron_error_log`;
CREATE TABLE IF NOT EXISTS `cron_error_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_name` varchar(60) DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `type_error` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `error_message` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `cron_error_log`
--

TRUNCATE TABLE `cron_error_log`;
-- --------------------------------------------------------

--
-- Table structure for table `cron_process_table`
--

DROP TABLE IF EXISTS `cron_process_table`;
CREATE TABLE IF NOT EXISTS `cron_process_table` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) DEFAULT NULL,
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `cron_process_table`
--

TRUNCATE TABLE `cron_process_table`;
-- --------------------------------------------------------

--
-- Table structure for table `fby_alert_codes`
--

DROP TABLE IF EXISTS `fby_alert_codes`;
CREATE TABLE IF NOT EXISTS `fby_alert_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_alert_codes`
--

TRUNCATE TABLE `fby_alert_codes`;
--
-- Dumping data for table `fby_alert_codes`
--

INSERT INTO `fby_alert_codes` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Stock', 'STOCK', '2022-01-28 17:10:50'),
(2, 'Order', 'ORDER', '2022-01-28 17:10:50'),
(3, 'Missing', 'MISSING', '2022-01-28 17:10:50'),
(4, 'Import', 'IMPORT', '2022-01-28 17:10:50'),
(5, 'Export', 'EXPORT', '2022-01-28 17:10:50'),
(6, 'Update', 'UPDATE', '2022-01-28 17:10:50'),
(7, 'Address', 'ADDRESS', '2022-01-28 17:10:50'),
(8, 'Unknown', 'UNKNOWN', '2022-01-28 17:10:50'),
(9, 'Notify', 'NOTIFY', '2022-01-28 17:10:50'),
(10, 'Order documents', 'ORDER DOCUMENTS', '2022-01-28 17:10:50'),
(11, 'Post Insert Order Actions', 'POST INSERT ORDER ACTIONS', '2022-01-28 17:10:50'),
(12, 'Shipment action', 'SHIPMENT', '2022-01-28 17:10:50'),
(13, 'Returned action', 'RETURNED', '2022-01-28 17:10:50'),
(14, 'Refund', 'REFUND', '2022-01-28 17:10:51'),
(15, 'Iupiter', 'IUPITER', '2022-01-28 17:10:51'),
(16, 'Missing configuration', 'MISSING CONFIGURATION', '2022-01-28 17:10:51');

-- --------------------------------------------------------

--
-- Table structure for table `fby_alert_domains`
--

DROP TABLE IF EXISTS `fby_alert_domains`;
CREATE TABLE IF NOT EXISTS `fby_alert_domains` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_alert_domains`
--

TRUNCATE TABLE `fby_alert_domains`;
--
-- Dumping data for table `fby_alert_domains`
--

INSERT INTO `fby_alert_domains` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Iupiter', 'Iupiter', '2022-01-28 17:18:18'),
(2, 'Satellite', 'Satellite', '2022-01-28 17:18:18'),
(3, 'Neteven', 'Neteven', '2022-01-28 17:18:18'),
(4, 'Ebay', 'Ebay', '2022-01-28 17:18:18'),
(5, 'Catalogo', 'Catalogo', '2022-01-28 17:18:18'),
(6, 'Logistica', 'Logistica', '2022-01-28 17:18:18'),
(7, 'Customer Service', 'Customer Service', '2022-01-28 17:18:18'),
(8, 'Ordini', 'Ordini', '2022-01-28 17:18:18'),
(9, 'Validazioni', 'Validazioni', '2022-01-28 17:18:18'),
(10, 'Validazioni recuperabili', 'Validazioni recuperabili', '2022-01-28 17:18:18'),
(11, 'Giacenze', 'Giacenze', '2022-01-28 17:18:18'),
(12, 'Indirizzi', 'Indirizzi', '2022-01-28 17:18:18'),
(13, 'Notifiche manuali', 'Notifiche manuali', '2022-01-28 17:18:18'),
(14, 'Notifiche incomplete', 'Notifiche incomplete', '2022-01-28 17:18:18'),
(15, 'Configurazioni', 'Configurazioni', '2022-01-28 17:18:18'),
(16, 'Rifornimenti', 'Rifornimenti', '2022-01-28 17:18:18'),
(17, 'File spedizioni', 'File spedizioni', '2022-01-28 17:18:18'),
(18, 'File prezzi', 'File prezzi', '2022-01-28 17:18:18'),
(19, 'File quantità', 'File quantità', '2022-01-28 17:18:18'),
(20, 'Magazzini sospesi', 'Magazzini sospesi', '2022-01-28 17:18:18'),
(21, 'Prodotti', 'Prodotti', '2022-01-28 17:18:18'),
(22, 'Rimborsi', 'Rimborsi', '2022-01-28 17:18:18'),
(23, 'Spedizioni', 'Spedizioni', '2022-01-28 17:18:18'),
(24, 'Sincronizzazione', 'Sincronizzazione', '2022-01-28 17:18:18');

-- --------------------------------------------------------

--
-- Table structure for table `fby_cancel_reason`
--

DROP TABLE IF EXISTS `fby_cancel_reason`;
CREATE TABLE IF NOT EXISTS `fby_cancel_reason` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_cancel_reason`
--

TRUNCATE TABLE `fby_cancel_reason`;
--
-- Dumping data for table `fby_cancel_reason`
--

INSERT INTO `fby_cancel_reason` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Ricevuto prodotto difettato', 'RPRODIF', '2022-01-28 17:03:17'),
(2, 'Ricevuto prodotto errato', 'RPROERR', '2022-01-28 17:03:17'),
(3, 'Taglia errata', 'RTAGERR', '2022-01-28 17:03:17'),
(4, 'Ricevuto colore errato', 'RCOLERR', '2022-01-28 17:03:17'),
(5, 'Ripensamento cliente', 'RRIPCLI', '2022-01-28 17:03:18'),
(6, 'Acquisto grandi quantità prodotto', 'RRIMFDK', '2022-01-28 17:03:18'),
(7, 'Acquisto non autorizzato', 'RACQNOT', '2022-01-28 17:03:18'),
(8, 'Articolo non desiderato', 'RPRONOD', '2022-01-28 17:03:18'),
(9, 'Articolo non richiesto incluso nella spedizione', 'RPRONRS', '2022-01-28 17:03:18'),
(10, 'Cambio colore', 'RCAMCOL', '2022-01-28 17:03:18'),
(11, 'Cambio modello', 'RCAMMOD', '2022-01-28 17:03:18'),
(12, 'Cambio prodotto', 'RCAMPRO', '2022-01-28 17:03:18'),
(13, 'Cambio taglia', 'RCAMTAG', '2022-01-28 17:03:18'),
(14, 'Condizioni diverse prodotto', 'RCONDIV', '2022-01-28 17:03:18'),
(15, 'Doppia spedizione', 'RDOPSPE', '2022-01-28 17:03:18'),
(16, 'Mancano parti/accessori al prodotto', 'RPARMAN', '2022-01-28 17:03:18'),
(17, 'Mancata consegna', 'RMANCON', '2022-01-28 17:03:18'),
(18, 'Ordine sbagliato dal cliente', 'RORDERR', '2022-01-28 17:03:18'),
(19, 'Prestazioni/qualità differenti da quelle attese', 'RPROASP', '2022-01-28 17:03:18'),
(20, 'Prodotto consegnato in ritardo', 'RCONRIT', '2022-01-28 17:03:18'),
(21, 'Prodotto danneggiato dal corriere', 'RDANCOR', '2022-01-28 17:03:18'),
(22, 'Prodotto non completamente compatibile con il mio sistema', 'RPRONOS', '2022-01-28 17:03:18'),
(23, 'Prodotto non conforme alle aspettative', 'RPRONOA', '2022-01-28 17:03:18'),
(24, 'Prodotto non corrisponde alla descrizione', 'RPRONOC', '2022-01-28 17:03:18'),
(25, 'Tempi consegna/spedizione lunghi', 'RTEMLUN', '2022-01-28 17:03:18'),
(26, 'Trovato prezzo migliore altrove', 'RPREMIG', '2022-01-28 17:03:19'),
(53, 'Indirizzo inutilizzabile', 'RIND', '2022-03-01 11:27:25'),
(54, 'Errore inserimento ordine', 'ERR', '2022-03-01 11:27:25'),
(55, 'Destinazione rifiutata dal corriere', 'DESTRIF', '2022-03-01 11:27:25');

-- --------------------------------------------------------

--
-- Table structure for table `fby_channel_codes`
--

DROP TABLE IF EXISTS `fby_channel_codes`;
CREATE TABLE IF NOT EXISTS `fby_channel_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=469 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_channel_codes`
--

TRUNCATE TABLE `fby_channel_codes`;
--
-- Dumping data for table `fby_channel_codes`
--

INSERT INTO `fby_channel_codes` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Pricy IT', 'PRICY', '2022-01-28 17:26:43'),
(2, 'Amazon DE', 'AMDE', '2022-01-28 17:26:43'),
(3, 'Amazon IT', 'AMIT', '2022-01-28 17:26:43'),
(4, 'Amazon ES', 'AMES', '2022-01-28 17:26:43'),
(5, 'Amazon FR', 'AMFR', '2022-01-28 17:26:44'),
(6, 'Amazon UK', 'AMUK', '2022-01-28 17:26:44'),
(7, 'Amazon US', 'AMUS', '2022-01-28 17:26:44'),
(8, 'Ebay IT', 'EBIT', '2022-01-28 17:26:44'),
(9, 'Ebay FR', 'EBFR', '2022-01-28 17:26:44'),
(10, 'Ebay ES', 'EBES', '2022-01-28 17:26:44'),
(11, 'Ebay DE', 'EBDE', '2022-01-28 17:26:44'),
(12, 'Ebay UK', 'EBUK', '2022-01-28 17:26:44'),
(13, 'Fnac FR', 'FNFR', '2022-01-28 17:26:44'),
(14, 'La Redoute FR', 'LRFR', '2022-01-28 17:26:44'),
(15, 'Galleries Lafayette FR', 'GLFR', '2022-01-28 17:26:44'),
(16, 'Zalando IT', 'ZLIT', '2022-01-28 17:26:44'),
(17, 'Cdiscount FR', 'CDFR', '2022-01-28 17:26:44'),
(18, 'Privalia IT', 'PRIT', '2022-01-28 17:26:44'),
(19, 'Spartoo IT', 'STIT', '2022-01-28 17:26:44'),
(20, 'Spartoo FR', 'STFR', '2022-01-28 17:26:44'),
(21, 'BrandAlley FR', 'BAFR', '2022-01-28 17:26:44'),
(22, 'Ebay US', 'EBUS', '2022-01-28 17:26:44'),
(23, 'Amazon CA', 'AMCA', '2022-01-28 17:26:44'),
(24, 'Ebay CA', 'EBCA', '2022-01-28 17:26:44'),
(25, 'ApiDrop', 'ADIT', '2022-01-28 17:26:44'),
(26, 'Zalando DE', 'ZLDE', '2022-01-28 17:26:45'),
(27, 'Amazon NL', 'AMNL', '2022-01-28 17:26:45'),
(28, 'Vendita diretta', 'DIR', '2022-01-28 17:26:45'),
(29, 'Amazon SE', 'AMSE', '2022-01-28 17:26:45'),
(30, 'Veepee IT', 'VPIT', '2022-01-28 17:26:45'),
(31, 'Veepee FR', 'VPFR', '2022-01-28 17:26:45'),
(32, 'Veepee ES', 'VPES', '2022-01-28 17:26:45'),
(33, 'Zalando FR', 'ZLFR', '2022-01-28 17:26:45'),
(34, 'Zalando CH DE', 'ZLCHDE', '2022-01-28 17:26:45'),
(35, 'Zalando CH FR', 'ZLCHFR', '2022-01-28 17:26:45'),
(36, 'Zalando NL', 'ZLNL', '2022-01-28 17:26:45'),
(37, 'Zalando PL', 'ZLPL', '2022-01-28 17:26:45'),
(38, 'Zalando AT', 'ZLAT', '2022-01-28 17:26:45'),
(39, 'Zalando BE FR', 'ZLBEFR', '2022-01-28 17:26:45'),
(40, 'Zalando BE NL', 'ZLBENL', '2022-01-28 17:26:45'),
(41, 'Refrigiwear DE', 'RWDE', '2022-01-28 17:26:45'),
(42, 'Refrigiwear ES', 'RWES', '2022-01-28 17:26:45'),
(43, 'Refrigiwear FR', 'RWFR', '2022-01-28 17:26:45'),
(44, 'Refrigiwear IT', 'RWIT', '2022-01-28 17:26:45'),
(45, 'Refrigiwear UK', 'RWUK', '2022-01-28 17:26:45'),
(46, 'Shopify IT', 'SHIT', '2022-01-28 17:26:45'),
(47, 'Shopify DE', 'SHDE', '2022-01-28 17:26:45'),
(62, 'Galeries Lafayette FR', 'GLFR', '2022-03-01 16:07:47'),
(75, 'Vendita diretta IT', 'VDIT', '2022-03-01 16:07:47'),
(93, 'Zalando ES', 'ZLES', '2022-03-01 16:07:47'),
(96, 'Zalando DK', 'ZLDK', '2022-03-01 16:07:47'),
(97, 'Zalando SE', 'ZLSE', '2022-03-01 16:07:47'),
(98, 'Yoox IT', 'YOIT', '2022-03-01 16:07:47'),
(99, 'Yoox BE', 'YOBE', '2022-03-01 16:07:47'),
(100, 'Yoox DE', 'YODE', '2022-03-01 16:07:47'),
(101, 'Yoox ES', 'YOES', '2022-03-01 16:07:47'),
(102, 'Yoox FR', 'YOFR', '2022-03-01 16:07:47'),
(103, 'Yoox NL', 'YONL', '2022-03-01 16:07:47'),
(104, 'Storeden IT', 'SRIT', '2022-03-01 16:07:47'),
(105, 'Prestashop IT', 'PSIT', '2022-03-01 16:07:47'),
(106, 'Prestashop DE', 'PSDE', '2022-03-01 16:07:47'),
(107, 'Prestashop ES', 'PSES', '2022-03-01 16:07:47'),
(108, 'Prestashop FR', 'PSFR', '2022-03-01 16:07:47'),
(109, 'Prestashop UK', 'PSUK', '2022-03-01 16:07:47'),
(467, 'Canale Prova', 'XXX', '2022-03-02 03:16:03'),
(468, 'werqwerweqr', 'qwerqwerewq', '2022-03-02 03:16:03');

-- --------------------------------------------------------

--
-- Table structure for table `fby_currency_codes`
--

DROP TABLE IF EXISTS `fby_currency_codes`;
CREATE TABLE IF NOT EXISTS `fby_currency_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_currency_codes`
--

TRUNCATE TABLE `fby_currency_codes`;
--
-- Dumping data for table `fby_currency_codes`
--

INSERT INTO `fby_currency_codes` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Euro', 'EUR', '2022-01-28 17:35:28'),
(2, 'Dollaro USA', 'USD', '2022-01-28 17:35:28'),
(3, 'Sterlina', 'GBP', '2022-01-28 17:35:28'),
(4, 'Dollaro Canadese', 'CAD', '2022-01-28 17:35:28'),
(9, 'Franco Svizzero', 'CHF', '2022-03-01 16:07:47'),
(10, 'Corona Danese', 'DKK', '2022-03-01 16:07:47'),
(11, 'Corona Svedese', 'SEK', '2022-03-01 16:07:47'),
(12, 'Zloty Polacco', 'PLN', '2022-03-01 16:07:47');

-- --------------------------------------------------------

--
-- Table structure for table `fby_payment_method`
--

DROP TABLE IF EXISTS `fby_payment_method`;
CREATE TABLE IF NOT EXISTS `fby_payment_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `payment_code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `fby_payment_method`
--

TRUNCATE TABLE `fby_payment_method`;
--
-- Dumping data for table `fby_payment_method`
--

INSERT INTO `fby_payment_method` (`id`, `name`, `payment_code`, `created_at`) VALUES
(1, 'PayPal', 'PP', '2022-01-28 16:31:18'),
(2, 'Sconosciuto', 'NaN', '2022-01-28 16:31:18'),
(3, 'Carta di Credito', 'CC', '2022-01-28 16:31:18'),
(4, 'Bonifico', 'BN', '2022-01-28 16:31:18'),
(5, 'Contrassegno', 'COD', '2022-05-04 14:48:15');

-- --------------------------------------------------------

--
-- Table structure for table `map_channel_alert_code`
--

DROP TABLE IF EXISTS `map_channel_alert_code`;
CREATE TABLE IF NOT EXISTS `map_channel_alert_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ch_alert_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_alert_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `map_channel_alert_code`
--

TRUNCATE TABLE `map_channel_alert_code`;
--
-- Dumping data for table `map_channel_alert_code`
--

INSERT INTO `map_channel_alert_code` (`id`, `channel`, `ch_alert_code`, `fby_alert_code`) VALUES
(1, 'shopify', 'send_Orders_Fby', 'ORDER'),
(2, 'shopify', 'send_Products_Fby', 'STOCK'),
(3, 'shopify', 'catch', 'MISSING CONFIGURATION'),
(4, 'shopify', 'get_Shopify_Orders', 'ORDER'),
(5, 'shopify', 'get_Shopify_Products', 'STOCK'),
(6, 'shopify', 'push_Stock_Shopify', 'STOCK'),
(7, 'shopify', 'send_Canceled_Orders_Fby', 'ORDER');

-- --------------------------------------------------------

--
-- Table structure for table `map_channel_cancel_reason`
--

DROP TABLE IF EXISTS `map_channel_cancel_reason`;
CREATE TABLE IF NOT EXISTS `map_channel_cancel_reason` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ch_cancel_reason` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_cancel_reason` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `map_channel_cancel_reason`
--

TRUNCATE TABLE `map_channel_cancel_reason`;
--
-- Dumping data for table `map_channel_cancel_reason`
--

INSERT INTO `map_channel_cancel_reason` (`id`, `channel`, `ch_cancel_reason`, `fby_cancel_reason`) VALUES
(1, 'shopify', 'customer', 'RRIPCLI'),
(2, 'shopify', 'fraud', 'RPROERR'),
(3, 'shopify', 'inventory', 'RRIMFDK'),
(4, 'shopify', 'other', 'RACQNOT');

-- --------------------------------------------------------

--
-- Table structure for table `map_channel_payment_method`
--

DROP TABLE IF EXISTS `map_channel_payment_method`;
CREATE TABLE IF NOT EXISTS `map_channel_payment_method` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ch_payment_method` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_payment_method` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=22 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `map_channel_payment_method`
--

TRUNCATE TABLE `map_channel_payment_method`;
--
-- Dumping data for table `map_channel_payment_method`
--

INSERT INTO `map_channel_payment_method` (`id`, `channel`, `ch_payment_method`, `fby_payment_method`) VALUES
(1, 'shopify', '', 'NaN'),
(2, 'shopify', 'Cash on Delivery (COD)', 'COD'),
(3, 'shopify', 'Contrassegno (SOLO ITALIA)', 'COD'),
(4, 'shopify', 'credit_card_mollie_', 'CC'),
(5, 'shopify', 'manual', 'NaN'),
(7, 'shopify', 'paypal', 'PP'),
(8, 'Shopify IT', '', 'NaN'),
(9, 'Shopify IT', 'Cash on Delivery (COD)', 'COD'),
(10, 'Shopify IT', 'Contrassegno (SOLO ITALIA)', 'COD'),
(11, 'Shopify IT', 'credit_card_mollie_', 'CC'),
(12, 'Shopify IT', 'manual', 'NaN'),
(13, 'Shopify IT', 'paypal', 'PP'),
(14, 'woocommerce', 'cod', 'COD'),
(15, 'Woocommerce IT', 'cod', 'COD'),
(16, 'shopify', 'credit_card', 'CC'),
(17, 'Shopify IT', 'credit_card', 'CC'),
(18, 'woocommerce', 'credit_card', 'CC'),
(19, 'Woocommerce IT', 'credit_card', 'CC'),
(20, 'Woocommerce IT', 'woocommerce_payments', 'CC'),
(21, 'woocommerce', 'woocommerce_payments', 'CC');

-- --------------------------------------------------------

--
-- Table structure for table `order_details`
--

DROP TABLE IF EXISTS `order_details`;
CREATE TABLE IF NOT EXISTS `order_details` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `channel` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_code` varchar(128) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `owner_code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `account_id` int(11) DEFAULT NULL,
  `order_no` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `seller_order_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `purchase_date` datetime DEFAULT NULL,
  `payment_time` datetime DEFAULT NULL,
  `order_line_item_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `sku` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `order_item_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `transaction_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `product_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `brand` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `available_stock` int(11) DEFAULT 0,
  `quantity_purchased` int(11) DEFAULT NULL,
  `currency` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `exchange_rate` float DEFAULT NULL,
  `item_price` decimal(10,2) DEFAULT NULL,
  `line_item_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `item_tax` decimal(10,2) DEFAULT NULL,
  `item_total_tax` decimal(10,2) DEFAULT NULL,
  `promotion_discount` decimal(10,2) DEFAULT NULL,
  `item_total_price` decimal(10,2) DEFAULT NULL,
  `item_total_ship_price` decimal(10,2) DEFAULT NULL,
  `tracking_courier` varchar(256) DEFAULT NULL,
  `tracking_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `tracking_url` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `is_trackable` tinyint(4) DEFAULT 0,
  `payment_status` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `order_status` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cancel_reason` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `is_canceled_fby` tinyint(4) UNSIGNED DEFAULT NULL,
  `count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `fby_error_flag` tinyint(4) UNSIGNED NOT NULL DEFAULT 0,
  `cron_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `IsCheckAfter48Hours` int(11) DEFAULT NULL,
  `checked_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`,`order_line_item_id`,`sku`,`order_item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `order_details`
--

TRUNCATE TABLE `order_details`;
--
-- Dumping data for table `order_details`
--

INSERT INTO `order_details` (`id`, `channel`, `channel_code`, `owner_code`, `fby_user_id`, `account_id`, `order_no`, `location_id`, `seller_order_id`, `purchase_date`, `payment_time`, `order_line_item_id`, `sku`, `barcode`, `order_item_id`, `transaction_id`, `product_name`, `brand`, `available_stock`, `quantity_purchased`, `currency`, `exchange_rate`, `item_price`, `line_item_price`, `item_tax`, `item_total_tax`, `promotion_discount`, `item_total_price`, `item_total_ship_price`, `tracking_courier`, `tracking_id`, `tracking_url`, `is_trackable`, `payment_status`, `order_status`, `cancel_reason`, `is_canceled_fby`, `count`, `fby_error_flag`, `cron_name`, `cron_id`, `status`, `IsCheckAfter48Hours`, `checked_at`, `created_at`, `updated_at`) VALUES
(2, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', NULL, '1002', '2022-05-06 15:28:30', '2022-05-06 15:28:31', '**************', 'TSTP_TSTB_space156', '', '**************', '**************', 'T-Shirt Space-X unisex', NULL, 0, 1, 'EUR', 0.22, '15.00', '15.00', '2.70', '2.70', '0.00', '15.00', '0.00', NULL, NULL, NULL, 0, 'paid', 'unfulfilled', NULL, NULL, 0, 0, 'get_Shopify_Orders', '02f10c15-1ed9-484a-926a-70f77c70e052', 0, 1, '2022-05-10 13:03:35', '2022-05-10 12:21:03', NULL),
(3, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', NULL, '1001', '2022-05-06 15:12:50', NULL, '12168111816947', 'TSTP_TSTB_space156', '', '**************', '12168111816947', 'T-Shirt Space-X unisex', NULL, 0, 1, 'EUR', 0.22, '15.00', '15.00', '2.70', '2.70', '0.00', '15.00', '9.99', NULL, NULL, NULL, 0, 'pending', 'unfulfilled', NULL, NULL, 0, 0, 'get_Shopify_Orders', '02f10c15-1ed9-484a-926a-70f77c70e052', 0, 0, '2022-05-10 13:12:53', '2022-05-10 12:21:03', NULL),
(4, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', NULL, '1003', '2022-05-06 15:28:30', '2022-05-06 15:28:31', '**************', 'TSTP_TSTB_space156', '', '**************', '**************', 'T-Shirt Space-X unisex', NULL, 0, 1, 'EUR', 0.22, '15.00', '15.00', '2.70', '2.70', '0.00', '15.00', '0.00', NULL, NULL, NULL, 0, 'pending', 'unfulfilled', NULL, NULL, 0, 0, 'get_Shopify_Orders', '02f10c15-1ed9-484a-926a-70f77c70e052', 0, 1, '2022-05-10 13:12:55', '2022-05-10 12:21:03', NULL),
(6, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', NULL, '1003', '2022-05-06 15:28:30', '2022-05-06 15:28:31', '**************', 'TSTP_TSTB_space156', '', '**************', '**************', 'T-Shirt Space-X unisex', NULL, 0, 1, 'EUR', 0.22, '15.00', '15.00', '2.70', '2.70', '0.00', '15.00', '0.00', NULL, NULL, NULL, 0, 'pending', 'unfulfilled', NULL, NULL, 0, 0, 'get_Shopify_Orders', '02f10c15-1ed9-484a-926a-70f77c70e052', 0, 1, '2022-05-10 13:48:05', '2022-05-10 12:21:03', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `order_masters`
--

DROP TABLE IF EXISTS `order_masters`;
CREATE TABLE IF NOT EXISTS `order_masters` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `channel` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_code` varchar(128) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `owner_code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `account_id` int(11) DEFAULT NULL,
  `order_no` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `seller_order_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `purchase_date` datetime DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `local_time` datetime DEFAULT NULL,
  `SalesChannel` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `shipping_method` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `recipient_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_company` varchar(256) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `ship_address_1` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_address_2` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_city` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_state` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_state_code` varchar(128) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `ship_postal_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_country` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_country_code` varchar(128) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `ship_phone_number` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `total_order` decimal(10,2) DEFAULT NULL,
  `total_items` int(11) DEFAULT NULL,
  `total_items_price` decimal(10,2) DEFAULT NULL,
  `total_shipping_price` decimal(10,2) DEFAULT NULL,
  `total_shipping_tax` decimal(10,2) DEFAULT NULL,
  `total_tax` decimal(10,2) DEFAULT NULL,
  `total_discount` decimal(10,2) DEFAULT NULL,
  `payment_transaction_id` varchar(256) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `payment_method` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `currency_code` varchar(20) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `buyer_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `buyer_email` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `buyer_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `product_details` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `sales_record_no` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `payment_status` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `order_status` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `is_canceled` tinyint(4) NOT NULL DEFAULT 0,
  `fby_send_status` tinyint(4) DEFAULT 0,
  `count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `fby_error_flag` tinyint(4) UNSIGNED NOT NULL DEFAULT 0,
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `order_masters`
--

TRUNCATE TABLE `order_masters`;
--
-- Dumping data for table `order_masters`
--

INSERT INTO `order_masters` (`id`, `channel`, `channel_code`, `owner_code`, `fby_user_id`, `account_id`, `order_no`, `seller_order_id`, `purchase_date`, `payment_date`, `local_time`, `SalesChannel`, `shipping_method`, `recipient_name`, `ship_company`, `ship_address_1`, `ship_address_2`, `ship_city`, `ship_state`, `ship_state_code`, `ship_postal_code`, `ship_country`, `ship_country_code`, `ship_phone_number`, `total_order`, `total_items`, `total_items_price`, `total_shipping_price`, `total_shipping_tax`, `total_tax`, `total_discount`, `payment_transaction_id`, `payment_method`, `currency_code`, `buyer_id`, `buyer_email`, `buyer_name`, `product_details`, `sales_record_no`, `payment_status`, `order_status`, `is_canceled`, `fby_send_status`, `count`, `fby_error_flag`, `cron_name`, `cron_id`, `created_at`, `updated_at`) VALUES
(3, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', '1002', '2022-05-06 15:28:30', '2022-05-06 15:28:31', NULL, NULL, NULL, 'Margherita Dicampo', '', 'Piazza Bologna 22', '', 'Roma', 'Roma', 'RM', '00162', 'Italy', 'IT', '', '15.00', 1, '15.00', '0.00', NULL, '2.70', '0.00', '', 'credit_card', 'EUR', '6203346944243', '<EMAIL>', 'Margherita Dicampo', NULL, '*************', 'paid', 'unfulfilled', 0, 0, 0, 0, 'send_Orders_Fby', 'a3cde7dd-aa5f-4e5a-8132-64c378b78dda', '2022-05-10 12:09:27', '2022-05-10 13:03:35'),
(4, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', '1001', '2022-05-06 15:12:50', NULL, NULL, NULL, NULL, 'Margherita Dicampo', '', 'Piazza Bologna 22', '', 'Roma', 'Roma', 'RM', '00162', 'Italy', 'IT', '', '24.99', 1, '15.00', '9.99', NULL, '4.50', '0.00', '', 'Cash on Delivery (COD)', 'EUR', '6203346944243', '<EMAIL>', 'Margherita Dicampo', NULL, '*************', 'pending', 'unfulfilled', 0, 1, 0, 0, 'send_Orders_Fby', '9cd53daa-1f2a-40eb-8d69-af8c58c7d96d', '2022-05-10 12:09:27', '2022-05-10 13:12:53'),
(5, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', '1002', '2022-05-06 15:28:30', NULL, NULL, NULL, NULL, 'Margherita Dicampo', '', 'Piazza Bologna 22', '', 'Roma', 'Roma', 'RM', '00162', 'Italy', 'IT', '', '15.00', 1, '15.00', '0.00', NULL, '2.70', '0.00', '', 'credit_card', 'EUR', '6203346944243', '<EMAIL>', 'Margherita Dicampo', NULL, '*************', 'pending', 'unfulfilled', 0, 0, 0, 0, 'send_Orders_Fby', 'a3cde7dd-aa5f-4e5a-8132-64c378b78dda', '2022-05-10 12:09:27', '2022-05-10 13:12:55'),
(6, 'Shopify IT', 'SHIT', 'FDM', '8', 17, '*************', '1002', '2022-05-06 15:28:30', NULL, NULL, NULL, NULL, 'Margherita Dicampo', '', 'Piazza Bologna 22', '', 'Roma', 'Roma', 'RM', '00162', 'Italy', 'IT', '', '15.00', 1, '15.00', '0.00', NULL, '2.70', '0.00', '', 'NaN', 'EUR', '6203346944243', '<EMAIL>', 'Margherita Dicampo', NULL, '*************', 'pending', 'unfulfilled', 0, 0, 0, 0, 'send_Orders_Fby', 'a3cde7dd-aa5f-4e5a-8132-64c378b78dda', '2022-05-10 12:09:27', '2022-05-10 13:48:05');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
CREATE TABLE IF NOT EXISTS `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `sku` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_product_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int(11) DEFAULT NULL,
  `inventory_quantity` int(11) DEFAULT NULL,
  `image` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `fby_error_flag` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fby_user_id` (`fby_user_id`,`sku`,`item_product_id`,`title`,`inventory_item_id`,`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `products`
--

TRUNCATE TABLE `products`;
--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `fby_user_id`, `channel`, `domain`, `owner_code`, `sku`, `barcode`, `item_id`, `title`, `item_product_id`, `inventory_item_id`, `location_id`, `previous_inventory_quantity`, `inventory_quantity`, `image`, `price`, `count`, `fby_error_flag`, `status`, `cron_name`, `cron_id`, `created_at`, `updated_at`) VALUES
(1, '8', 'Shopify IT', 'shopping170.myshopify.com', 'FDM', 'TSTP_TSTB_space156', '5555555555', '42346220650754', 'Default Title', '7570493473026', '44440980259074', '1', 48, 48, 'https://cdn.shopify.com/s/files/1/0610/7188/4546/products/spacex_blue.jpg?v=**********', '15.00', 0, 0, 0, 'get_Shopify_Products', '04af284b-1b91-4d56-8ce4-7f24e6a462a9', '2022-05-10 11:06:54', NULL),
(2, '8', 'Shopify IT', 'shopping170.myshopify.com', 'FDM', 'TSTP_TSTB_space156', '**********', '**************', 'Default Title', '*************', '**************', '1', 48, 48, 'https://cdn.shopify.com/s/files/1/0610/7188/4546/products/spacex_blue.jpg?v=**********', '15.00', 0, 0, 0, 'get_Shopify_Products', '04af284b-1b91-4d56-8ce4-7f24e6a462a9', '2022-05-10 11:06:54', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `shopify_account`
--

DROP TABLE IF EXISTS `shopify_account`;
CREATE TABLE IF NOT EXISTS `shopify_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `api_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `api_password` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `currency_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `group_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fby_user_id` (`fby_user_id`,`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `shopify_account`
--

TRUNCATE TABLE `shopify_account`;
--
-- Dumping data for table `shopify_account`
--

INSERT INTO `shopify_account` (`id`, `fby_user_id`, `domain`, `api_key`, `api_password`, `channel_code`, `currency_code`, `owner_code`, `group_code`, `created_at`, `updated_at`) VALUES
(1, '1002', 'shopping170.myshopify.com', '2ec972a612088fc392de502d7e4c3887', '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', 'SFIT', 'EUR', 'yt', 'AEU', '2021-12-21 15:22:16', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `temp_master_inventory`
--

DROP TABLE IF EXISTS `temp_master_inventory`;
CREATE TABLE IF NOT EXISTS `temp_master_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sku_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `skucode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `priority` int(11) DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `fby_user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku_id` (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `temp_master_inventory`
--

TRUNCATE TABLE `temp_master_inventory`;
-- --------------------------------------------------------

--
-- Table structure for table `temp_order_inventory`
--

DROP TABLE IF EXISTS `temp_order_inventory`;
CREATE TABLE IF NOT EXISTS `temp_order_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel_order_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_line_order_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `provider_order_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `provider_line_order_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `order_date` datetime NOT NULL,
  `sku` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `tracking_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ship_date` datetime NOT NULL,
  `carrier` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `url` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `is_return` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `temp_order_inventory`
--

TRUNCATE TABLE `temp_order_inventory`;
-- --------------------------------------------------------

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `auth_username` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `auth_password` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fby_user_id` (`fby_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `users`
--

TRUNCATE TABLE `users`;
--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `fby_user_id`, `auth_username`, `auth_password`, `created_at`, `updated_at`) VALUES
(1, 'yocabe', '<EMAIL>', '1002', 'channels_connector', 'kpVWZDcX6FN_DYWD', '2021-12-21 15:20:58', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `winston_logs`
--

DROP TABLE IF EXISTS `winston_logs`;
CREATE TABLE IF NOT EXISTS `winston_logs` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `operation_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `level` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `message` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `meta` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `partition_key` date NOT NULL DEFAULT (curdate()),
  PRIMARY KEY (`id`,`partition_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
PARTITION BY KEY (`PARTITION_KEY`);

--
-- Truncate table before insert `winston_logs`
--

TRUNCATE TABLE `winston_logs`;
-- --------------------------------------------------------

--
-- Table structure for table `_1_client`
--

DROP TABLE IF EXISTS `_1_client`;
CREATE TABLE IF NOT EXISTS `_1_client` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clientId` varchar(512) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ownerCode` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `isActive` tinyint(4) DEFAULT NULL,
  `createdOn` datetime NOT NULL DEFAULT current_timestamp(),
  `modifiedOn` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `_1_client`
--

TRUNCATE TABLE `_1_client`;
--
-- Dumping data for table `_1_client`
--

INSERT INTO `_1_client` (`id`, `clientId`, `name`, `ownerCode`, `isActive`, `createdOn`, `modifiedOn`) VALUES
(1, 'id-000', 'Chicco', 'YT', 1, '2022-01-19 20:59:01', '2022-01-19 20:59:01'),
(2, 'id123', 'Chicco', 'PI', 1, '2022-01-31 02:36:05', '2022-01-31 02:36:05'),
(3, '57', 'FBY — Demo', 'FDM', 1, '2022-02-24 20:59:01', '2022-02-24 20:59:01'),
(6, '61', 'Isbag', 'FIS', 1, '2022-02-25 11:14:39', '2022-02-25 11:14:39'),
(7, '63', 'Persian Gourmet', 'FPR', 1, '2022-03-04 14:20:02', '2022-03-04 14:20:02'),
(8, '67', 'DocHq', 'FDC', 1, '2022-03-31 10:10:06', '2022-03-31 10:10:06'),
(9, '68', 'Golden Days Milano', 'FGL', 1, '2022-04-01 10:17:22', '2022-04-01 10:17:22');

-- --------------------------------------------------------

--
-- Table structure for table `_2_channel`
--

DROP TABLE IF EXISTS `_2_channel`;
CREATE TABLE IF NOT EXISTS `_2_channel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channelId` int(11) NOT NULL,
  `groupCode` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `currencyCode` varchar(56) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ownerCode` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `channelCode` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `channelName` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `orderSyncStartDate` datetime DEFAULT NULL,
  `domain` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `username` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `password` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `siteId` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `compatibilityLevel` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ebay_devid` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ebay_appid` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ebay_certid` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ebay_quantity_update_by` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `apiKey` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `secret` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `token` varchar(2048) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `isActive` tinyint(4) DEFAULT 0,
  `isEnabled` tinyint(4) DEFAULT 0,
  `createdon` datetime NOT NULL DEFAULT current_timestamp(),
  `modifiedOn` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `channelId_UNIQUE` (`channelId`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8;

--
-- Truncate table before insert `_2_channel`
--

TRUNCATE TABLE `_2_channel`;
--
-- Dumping data for table `_2_channel`
--

INSERT INTO `_2_channel` (`id`, `channelId`, `groupCode`, `currencyCode`, `ownerCode`, `channelCode`, `channelName`, `orderSyncStartDate`, `domain`, `username`, `password`, `siteId`, `compatibilityLevel`, `ebay_devid`, `ebay_appid`, `ebay_certid`, `ebay_quantity_update_by`, `apiKey`, `secret`, `token`, `isActive`, `isEnabled`, `createdon`, `modifiedOn`) VALUES
(17, 8, 'SHEU', 'EUR', 'FDM', 'SHIT', 'Shopify IT', '2022-04-01 16:25:31', 'shopping170.myshopify.com', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '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', 0, 0, '2022-02-28 12:12:41', '2022-04-15 14:25:55'),
(18, 9, 'SHEU', 'EUR', 'FIS', 'SHIT', 'Shopify IT', '2022-04-04 00:00:00', 'is-bag-2.myshopify.com', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '197e3ba01b0f7afce72846a8b939a7edviF6gAYfD3oeqVkJk1lmnuGtimjwwNMkKIywRKQ9gswow5Kn3ItBuBx5Rn9gs7Lr', 0, 0, '2022-03-02 08:55:39', '2022-04-10 17:30:44'),
(19, 19, 'SHEU', 'EUR', 'FPR', 'SHIT', 'Shopify IT', '2022-04-04 10:00:00', 'persiancaviar-swiss.myshopify.com', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '1903e6371a2687dbd2aaa17531e1b147qyBe+iLGcXJwFuLl4fyu3WYRaMTNdaQ7nolZ/Y9lON/b8NJ8/P7xMSxcJLw0Jbij', 0, 0, '2022-03-04 17:26:11', '2022-04-04 09:05:32'),
(20, 1004, 'AEU', 'EUR', 'YT', 'SFIT', 'storeden', '2022-02-02 05:18:26', 'https://connect.storeden.com', NULL, '5bb85add137db786f696dffbe90629d5c63a4b6ca7e320a060d5cf31b1def918', NULL, NULL, NULL, NULL, NULL, NULL, '5dee7388e6281f7d88008ae0809162381c3cd7cd2350053061fa1ef83ae9e742031971', NULL, NULL, 1, 1, '2022-02-24 00:00:00', '2022-02-24 00:00:00'),
(21, 1005, 'AEU', 'USD', 'YT', 'SFIT', 'prestashop', '2022-03-15 17:03:10', 'http://prestashop.yocabe.com', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, 'EHQ8PK3NQXWJ5SKXG3DSLYZ62HRRJA27', NULL, NULL, 1, 1, '2022-03-17 16:46:42', '2022-03-17 16:46:42'),
(25, 25, 'WCEU', 'EUR', 'FDM', 'WCIT', 'Woocommerce IT', '2022-04-27 16:45:47', 'shopping180.altervista.org', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '48c2e4420890db3d04abf314e69476c02QYCAdmgv5ECnkI48d9/ch5xnaHj7B/jxsu+bl6NTDS8fTVMugBHXziSLyUVicNu', '06e0f617e1bfa17c68223cec3634de7cixiwvrS9pFjq+1Q5t7wYnuT7BcRJ/9dexE1k+tc5ArNJDk+rKVMVvPmtWzpoi8zS', '', 0, 0, '2022-04-27 14:48:37', '2022-04-27 15:36:42'),
(29, 14, 'SREU', 'EUR', 'FDM', 'SRIT', 'Storeden IT', '2022-03-24 12:00:00', 'connect.storeden.com', '', 'a6f4c9caa9afc4be89177cc105e5f257ptvzJxlHV9AbrVHYiBQc7FOfszq8bF24ig3j0qblSm0199tQi8n3gVTrT82H/QcNmNNZi/F9ZgBXHdcfkkgfQV9Ahi7W2UHoBBIamVrfuaE=', NULL, NULL, NULL, NULL, NULL, NULL, 'b55a192b67070b91f4d0efee16e10575YATnEgSaVMZiJX9s6OFpCK7DBQPnzDLhW5VOO3cebVX4hoRsl4IC1XpqXCbXdCzH6NGgk9Fktn+l/3YGNH8xrv2pmdVRdbiQl2zErYmpAn4=', '', '', 0, 0, '2022-03-24 11:57:53', '2022-03-24 17:31:52'),
(40, 1006, 'AEU', 'EUR', 'YT', 'WCIT', 'woocommerce', '2022-03-01 00:00:00', 'https://shop170.altervista.org/', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, 'ck_c8147aa5d500c288b547e889d0aca7553db43d9e', 'cs_b01afbdc2a825c11d716eba2de0f4c2672ccf1af', NULL, 1, 1, '2022-04-02 11:57:53', '2022-04-02 17:31:52'),
(41, 22, 'WCEU', 'EUR', 'FDM', 'WCIT', 'Woocommerce IT', '2022-04-06 10:17:17', 'https://shop170.altervista.org/', '', '', NULL, NULL, NULL, NULL, NULL, NULL, 'd53860f43ea0a6a1782ab6e07e500602Ou3RWMP6jVRC/wKT11wlyebijWyJifcYenBNuuC4muqRmXG8cMYMfgHyCTiyTFe6', '186df203148746649b271c6a13b18328uOY0qAf9VWKYYk1HAe7M/nESQuhRSgIKQjPRdKcr+a7vebXyGNhzI2tnge5o2mnI', '', 0, 0, '2022-04-06 11:30:20', '2022-04-06 11:30:20'),
(42, 20, 'SHEU', 'EUR', 'FGL', 'SHIT', 'Shopify IT', '2022-04-07 21:00:00', 'www-bloom26-com.myshopify.com', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '41df96a719d8c81b3f7a5303c6945959gJcQ4nCM4IIO4NOHdgnPr7GMps1MW/B+I2MrUvaoh4ThJPxloi8SRteAtvaZ2Ll2', 0, 0, '2022-04-06 16:27:00', '2022-04-06 16:27:00'),
(43, 1007, 'AEU', 'EUR', 'YT', 'EBIT', 'ebay', '2022-04-28 10:06:29', 'https://api.ebay.com/ws/api.dll', NULL, NULL, '3', NULL, '6c65e3a2-6852-431f-ba5d-4ea775c24f00', 'LorenzoC-Yocab-PRD-77a9ffd53-5659cd31', 'PRD-7a9ffd53b4dc-8b75-4907-85d0-2faf', NULL, NULL, NULL, 'v^1.1#i^1#I^3#p^3#r^1#f^0#t^Ul4xMF8xMDo0RkEzN0YyNTJEOUQ5MzY3NzJEMTFGMTk0QTFGM0I2MV8yXzEjRV4yNjA=', 1, 1, '2022-04-13 15:31:00', '2022-04-13 15:31:00'),
(44, 23, 'PSEU', 'EUR', 'FDM', 'PSIT', 'Prestashop IT', '2022-04-22 16:00:00', 'prestashop.yocabe.com', '', '', NULL, NULL, NULL, NULL, NULL, NULL, 'b910d31e765b4e214c55042983410556WjZu8goJvUYSaxPpyRsITZJAExJh34HBihU6eN50nVofxPGnQTZAD1RDrrAZBnSg', '', '', 0, 0, '2022-04-22 14:02:54', '2022-04-22 14:13:37');
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
