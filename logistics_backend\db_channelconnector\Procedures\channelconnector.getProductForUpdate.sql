
DROP PROCEDURE IF EXISTS channelconnector.getProductForUpdate;

DELIMITER $$
CREATE PROCEDURE channelconnector.getProductForUpdate(`in_fby_id` INT, `in_dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductForUpdate (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductForUpdate (1006,'');
    
    SELECT 'Req',
		cp.* 
	FROM createproducts  as cp
    UNION
    SELECT 'Res',
		cp1.* 
	FROM createdproducts  as cp1;
    
    */
	SELECT 
			cp.`id`,
			cp.`fby_user_id`,
			cp.`channel`,
			cp.`domain`,
			cp.`owner_code`,
			cp.`sku`,
			cp.`barcode`,
			ccp.`item_id`,
			cp.`title`,
			ccp.`item_product_id`,
			ccp.`inventory_item_id`,
			cp.`location_id`,
			cp.`previous_inventory_quantity`,
			cp.`inventory_quantity`,
			cp.`image`,
			cp.`price`,
			cp.`count`,
			cp.`fby_error_flag`,
			cp.`status`,
			cp.`cron_name`,
			cp.`cron_id`,
			cp.`created_at`,
			cp.`updated_at`,
			cp.`isChanged`
	FROM
		createproducts AS cp
	INNER JOIN
		createdproducts ccp ON ccp.fby_user_id = cp.fby_user_id
	AND ccp.sku = cp.sku
	AND ccp.fby_user_id = cp.fby_user_id
	AND cp.fby_user_id = in_fby_id
	WHERE
		cp.isChanged = 1; 
		
END$$
DELIMITER ;