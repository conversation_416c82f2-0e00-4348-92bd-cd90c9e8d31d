{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Order\\\\Addorder\\\\AddNewOrder.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, Typography, Grid, TextField, Button, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Menu, MenuItem, Select, FormControl, InputLabel, Chip, Box } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SearchIcon from '@mui/icons-material/Search';\nimport AddIcon from '@mui/icons-material/Add';\nimport RemoveIcon from '@mui/icons-material/Remove';\nimport axios from 'axios';\nimport { NavBar } from '../../../components/Navbar/Navbar';\nimport { Sidebar } from '../../../components/SidePanel/Sidebar';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const AddNewOrder = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    orderNo\n  } = useParams();\n  const [products, setProducts] = useState([]);\n  const [groupedProducts, setGroupedProducts] = useState([]);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [productQuantities, setProductQuantities] = useState({});\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    phone: '',\n    address1: '',\n    city: '',\n    province: '',\n    country: '',\n    zip: ''\n  });\n  const [responseData, setResponseData] = useState([]);\n  const [paymentStatus, setPaymentStatus] = useState('Pending');\n  const [totalPayment, setTotalPayment] = useState(0);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [openCustomerDialog, setOpenCustomerDialog] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [paymentMethod, setPaymentMethod] = useState('');\n  const [customerAndShippingDetails, setCustomerAndShippingDetails] = useState(null);\n  const [orderStatus, setOrderStatus] = useState('fulfilled');\n  const [openTrackingDialog, setOpenTrackingDialog] = useState(false);\n  const [channelOrderId, setChannelOrderId] = useState('');\n  const [originalChannelOrderId, setOriginalChannelOrderId] = useState('');\n  const [channelCode, setChannelCode] = useState('');\n  const [skuEan, setSkuEan] = useState('');\n  const [skuCode, setSkuCode] = useState('');\n  const [tracking, setTracking] = useState('');\n  const [shipmentDate, setShipmentDate] = useState('');\n  const [carrier, setCarrier] = useState('');\n  const [shipUrl, setShipUrl] = useState('');\n  const [isReturn, setIsReturn] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        if (orderNo) {\n          // Fetch order details if orderNo exists\n          var storedGroupCode = localStorage.getItem(\"groupCode\");\n          const resDetails = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_order_detail?fby_user_id=${storedGroupCode}`, {\n            order_no: orderNo\n          }, {\n            headers: {\n              'Content-Type': 'application/json',\n              Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\n            }\n          });\n          if (resDetails.data.success) {\n            setResponseData(resDetails.data.success.data);\n            const responseData = resDetails.data.success.data.map(element => ({\n              id: element.id,\n              title: element.product_name,\n              image: element.image,\n              // Assuming there's an image property in the response\n              price: element.item_total_price,\n              quantity: element.quantity_purchased,\n              total_tax: element.item_tax,\n              line_item_id: element.order_line_item_id,\n              order_status: element.order_status,\n              payment_status: element.payment_status\n            }));\n            if (responseData.length > 0) {\n              setSelectedProducts(responseData.map(item => item)); // Update here\n              setOrderStatus(responseData[0].order_status);\n              setPaymentStatus(responseData[0].payment_status);\n            }\n            const shipData = resDetails.data.success.data.map(element => ({\n              first_name: element.recipient_name,\n              last_name: '',\n              email: element.buyer_email,\n              phone: element.ship_phone_number,\n              address1: element.ship_address_1,\n              city: element.ship_city,\n              province: element.ship_state_code,\n              country: element.ship_country,\n              zip: element.ship_postal_code\n            }));\n            // Set the customer and shipping details\n            setCustomerAndShippingDetails(shipData[0]);\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      }\n    };\n    fetchData();\n  }, [orderNo]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleCheckboxChange = product => {\n    setSelectedProducts(prevState => {\n      const isSelected = prevState.some(item => item.id === product.id);\n      if (!isSelected) {\n        // Initialize quantity when product is selected\n        setProductQuantities(prev => ({\n          ...prev,\n          [product.id]: 1\n        }));\n        return [...prevState, product];\n      } else {\n        // Remove quantity when product is deselected\n        setProductQuantities(prev => {\n          const newQuantities = {\n            ...prev\n          };\n          delete newQuantities[product.id];\n          return newQuantities;\n        });\n        return prevState.filter(item => item.id !== product.id);\n      }\n    });\n  };\n  const handleQuantityChange = (productId, newQuantity) => {\n    setProductQuantities(prev => ({\n      ...prev,\n      [productId]: newQuantity\n    }));\n  };\n  const handleBrowse = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      const res = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`, {}, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\n        }\n      });\n      if (res.data.success && res.data.success.data && res.data.success.data.length > 0) {\n        const responseData = res.data.success.data.map(element => ({\n          id: element.id,\n          title: element.title,\n          body_html: element.description,\n          image: element.image,\n          price: element.price,\n          quantity: element.inventory_quantity,\n          sku: element.sku,\n          option_1_value: element.option_1_value || '',\n          option_2_value: element.option_2_value || ''\n        }));\n\n        // Group products by title\n        const grouped = responseData.reduce((acc, product) => {\n          const existingGroup = acc.find(group => group.title === product.title);\n          if (existingGroup) {\n            existingGroup.variants.push(product);\n          } else {\n            acc.push({\n              title: product.title,\n              image: product.image,\n              body_html: product.body_html,\n              variants: [product]\n            });\n          }\n          return acc;\n        }, []);\n        setProducts(responseData);\n        setGroupedProducts(grouped);\n        setOpenDialog(true);\n      } else {\n        setError('No products found');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      setError('Failed to load products. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to handle tracking submit\n  const handleTrackingSubmit = async () => {\n    try {\n      // Make API call to update tracking\n      const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/push_tracking`, {\n        channelOrderId: channelOrderId,\n        originalChannelOrderId: originalChannelOrderId,\n        channelCode: channelCode,\n        skuEan: skuEan,\n        skuCode: skuCode,\n        tracking: tracking,\n        shipmentDate: shipmentDate,\n        carrier: carrier,\n        shipUrl: shipUrl,\n        isReturn: isReturn\n      });\n      // Handle success response\n      console.log(response.data);\n    } catch (error) {\n      // Handle error\n      console.error('Error updating tracking:', error);\n    }\n  };\n  const handleSaveSelectedProducts = () => {\n    setSelectedProducts(selectedProducts);\n\n    // You can save the selected products to a state or perform any other required action here\n    // For example, you can update a state with the selected products\n    const selectedProductsData = selectedProducts.map(productId => {\n      return products.find(product => product.id === productId);\n    });\n    // Do something with the selected products data, for example:\n    console.log('Selected products:', selectedProductsData);\n    setOpenDialog(false);\n  };\n  const handleSaveCustomerDetails = () => {\n    // Save the entered customer and shipping details\n    const newCustomerAndShippingDetails = {\n      first_name: formData.first_name,\n      last_name: formData.last_name,\n      email: formData.email,\n      phone: formData.phone,\n      address1: formData.address1,\n      city: formData.city,\n      province: formData.province,\n      country: formData.country,\n      zip: formData.zip\n    };\n\n    // Set the customer and shipping details\n    setCustomerAndShippingDetails(newCustomerAndShippingDetails);\n\n    // Close the customer dialog\n    setOpenCustomerDialog(false);\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Validate that we have customer details\n      if (!customerAndShippingDetails) {\n        setError('Please add customer and shipping details before saving the order.');\n        return;\n      }\n\n      // Validate that we have selected products\n      if (selectedProducts.length === 0) {\n        setError('Please select at least one product before saving the order.');\n        return;\n      }\n\n      // Check inventory availability\n      const inventoryCheck = selectedProducts.every(product => {\n        const requestedQuantity = productQuantities[product.id] || 1;\n        const availableQuantity = product.quantity || 0;\n        return requestedQuantity <= availableQuantity;\n      });\n      if (!inventoryCheck) {\n        setError('Some products do not have sufficient inventory. Please adjust quantities.');\n        return;\n      }\n\n      // Prepare the data for the request\n      const requestData = {\n        line_items: selectedProducts.map(product => {\n          const quantity = productQuantities[product.id] || 1;\n          return {\n            variant_id: product.id,\n            quantity: quantity,\n            price: product.price || 0,\n            sku: product.sku,\n            title: product.title\n          };\n        }),\n        customer: {\n          first_name: customerAndShippingDetails.first_name,\n          last_name: customerAndShippingDetails.last_name,\n          email: customerAndShippingDetails.email\n        },\n        billing_address: {\n          first_name: customerAndShippingDetails.first_name,\n          last_name: customerAndShippingDetails.last_name,\n          address1: customerAndShippingDetails.address1,\n          city: customerAndShippingDetails.city,\n          province: customerAndShippingDetails.province,\n          country: customerAndShippingDetails.country,\n          zip: customerAndShippingDetails.zip,\n          phone: customerAndShippingDetails.phone\n        },\n        shipping_address: {\n          first_name: customerAndShippingDetails.first_name,\n          last_name: customerAndShippingDetails.last_name,\n          address1: customerAndShippingDetails.address1,\n          city: customerAndShippingDetails.city,\n          province: customerAndShippingDetails.province,\n          country: customerAndShippingDetails.country,\n          zip: customerAndShippingDetails.zip,\n          phone: customerAndShippingDetails.phone\n        },\n        email: customerAndShippingDetails.email,\n        transactions: [{\n          kind: \"sale\",\n          status: \"success\",\n          amount: totalPayment\n        }],\n        financial_status: paymentStatus,\n        total_price: totalPayment\n      };\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n\n      // Make the POST request\n      const res = await axios({\n        url: `${process.env.REACT_APP_BASE_URL}/common/api/create_shopify_order/?fby_user_id=${storedGroupCode}`,\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        },\n        data: requestData\n      });\n      console.log(res);\n      if (res.data.success) {\n        // Update local inventory quantities\n        const updatedProducts = products.map(product => {\n          const selectedProduct = selectedProducts.find(sp => sp.id === product.id);\n          if (selectedProduct) {\n            const usedQuantity = productQuantities[product.id] || 1;\n            return {\n              ...product,\n              quantity: Math.max(0, product.quantity - usedQuantity)\n            };\n          }\n          return product;\n        });\n        setProducts(updatedProducts);\n\n        // Show success message and redirect\n        alert('Order created successfully!');\n        navigate('/orders');\n      } else {\n        setError('Failed to create order. Please try again.');\n      }\n    } catch (error) {\n      console.error('Error saving Order:', error);\n      setError('Error creating order. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCustomerAndShipping = () => {\n    setOpenCustomerDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n  };\n  const handleCustomerCloseDialog = () => {\n    setOpenCustomerDialog(false);\n  };\n  const handlePaymentClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handlePaymentClose = () => {\n    setAnchorEl(null);\n  };\n  const handlePaymentMethod = method => {\n    setPaymentMethod(method);\n    setAnchorEl(null);\n    if (method === 'Mark as Paid') {\n      setPaymentStatus('Paid');\n    }\n  };\n  const handleLineItemClick = lineItemId => {\n    navigate(`/orderMaster/${orderNo}/orderDetails/${lineItemId}`);\n  };\n  useEffect(() => {\n    // Calculate total payment with quantities\n    let totalPrice = 0;\n    for (const product of selectedProducts) {\n      const quantity = productQuantities[product.id] || 1;\n      const price = product.item_total_price || product.price || 0;\n      totalPrice += price * quantity;\n    }\n    setTotalPayment(totalPrice);\n  }, [selectedProducts, productQuantities]);\n  const handleBack = () => {\n    navigate(-1);\n  };\n  const handleButtonClick = () => {\n    setOpenTrackingDialog(true);\n  };\n\n  // Function to handle tracking dialog close\n  const handleTrackingDialogClose = () => {\n    setOpenTrackingDialog(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      selectedSidebarItem: \"products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      style: {\n        marginTop: '45px',\n        marginLeft: '255px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n            onClick: handleBack,\n            style: {\n              cursor: 'pointer'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.2rem',\n              marginLeft: 8\n            },\n            children: \"Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            style: {\n              marginLeft: 'auto'\n            },\n            onClick: handleSave,\n            disabled: loading,\n            children: loading ? 'Saving...' : 'Save'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 21\n        }, this), error && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"error\",\n          style: {\n            marginBottom: '16px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [!orderNo && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h4\",\n              style: {\n                marginBottom: 10\n              },\n              children: \"Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex'\n              },\n              children: !orderNo && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Search Product\",\n                  variant: \"outlined\",\n                  fullWidth: true,\n                  size: \"small\",\n                  InputProps: {\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"primary\",\n                      \"aria-label\": \"search\",\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 53\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  style: {\n                    marginLeft: 10,\n                    fontSize: '0.8rem'\n                  },\n                  size: \"small\",\n                  onClick: handleBrowse,\n                  disabled: loading,\n                  children: loading ? 'Loading...' : 'Browse'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 25\n          }, this), selectedProducts.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  sx: {\n                    background: '#f5f5f5'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Title\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 49\n                    }, this), orderNo && /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Line Order Id\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: selectedProducts.map(newproduct => {\n                    const productQuantity = productQuantities[newproduct.id] || 1;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: newproduct.image || '',\n                          alt: newproduct.title,\n                          style: {\n                            maxWidth: '100px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 523,\n                          columnNumber: 68\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 523,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          children: newproduct.title || ''\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 525,\n                          columnNumber: 61\n                        }, this), (newproduct.option_1_value || newproduct.option_2_value) && /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"textSecondary\",\n                          children: [newproduct.option_1_value, \" \", newproduct.option_2_value && `/ ${newproduct.option_2_value}`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          display: \"block\",\n                          color: \"textSecondary\",\n                          children: [\"SKU: \", newproduct.sku]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 524,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleQuantityChange(newproduct.id, Math.max(1, productQuantity - 1)),\n                            disabled: productQuantity <= 1,\n                            children: /*#__PURE__*/_jsxDEV(RemoveIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 542,\n                              columnNumber: 69\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 537,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                            size: \"small\",\n                            type: \"number\",\n                            value: productQuantity,\n                            onChange: e => handleQuantityChange(newproduct.id, Math.max(1, parseInt(e.target.value) || 1)),\n                            inputProps: {\n                              min: 1,\n                              max: newproduct.quantity || 999,\n                              style: {\n                                textAlign: 'center',\n                                width: '60px'\n                              }\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleQuantityChange(newproduct.id, Math.min(newproduct.quantity || 999, productQuantity + 1)),\n                            disabled: productQuantity >= (newproduct.quantity || 999),\n                            children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 556,\n                              columnNumber: 69\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 551,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 536,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"textSecondary\",\n                          children: [\"Available: \", newproduct.quantity || 0]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 559,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [\"$\", (newproduct.price || 0) * productQuantity]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 563,\n                        columnNumber: 57\n                      }, this), orderNo && /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          onClick: () => handleLineItemClick(newproduct.line_item_id),\n                          children: newproduct.line_item_id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 566,\n                          columnNumber: 65\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 62\n                      }, this)]\n                    }, newproduct.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 53\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          style: {\n            marginTop: '20px',\n            marginLeft: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h4\",\n                children: \"Payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n                component: Paper,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  children: /*#__PURE__*/_jsxDEV(TableBody, {\n                    children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Payment Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: paymentStatus\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: orderStatus !== 'unfulfilled' && orderStatus !== 'partially fulfilled' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: [/*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outlined\",\n                            color: \"primary\",\n                            onClick: handlePaymentClick,\n                            children: \"Collect Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 596,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n                            anchorEl: anchorEl,\n                            open: Boolean(anchorEl),\n                            onClose: handlePaymentClose,\n                            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                              onClick: () => handlePaymentMethod('Enter Credit Card'),\n                              children: \"Enter Credit Card\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 604,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                              onClick: () => handlePaymentMethod('Mark as Paid'),\n                              children: \"Mark as Paid\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 605,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 599,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outlined\",\n                            color: \"primary\",\n                            children: \"Send Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 609,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 608,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outlined\",\n                          color: \"primary\",\n                          onClick: handleButtonClick,\n                          children: \"Add Tracking\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        style: {\n          marginTop: '80px'\n        },\n        children: customerAndShippingDetails ? /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h2\",\n              style: {\n                fontSize: '1.2rem'\n              },\n              children: \"Customer and Shipping Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Name: \", customerAndShippingDetails.first_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Email: \", customerAndShippingDetails.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Phone: \", customerAndShippingDetails.phone]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Address: \", customerAndShippingDetails.address1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"City: \", customerAndShippingDetails.city]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Province: \", customerAndShippingDetails.province]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Country: \", customerAndShippingDetails.country]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"ZIP: \", customerAndShippingDetails.zip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h2\",\n              style: {\n                fontSize: '0.8rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Add Customer and Shipping Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 104\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleAddCustomerAndShipping,\n                style: {\n                  fontSize: '0.8rem',\n                  marginLeft: '5px'\n                },\n                children: \"Add\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openCustomerDialog,\n        onClose: handleCustomerCloseDialog,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Customer and Shipping Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Customer Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"First Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.first_name,\n                name: \"first_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Last Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.last_name,\n                name: \"last_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Email\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.email,\n                name: \"email\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Phone\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.phone,\n                name: \"phone\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Shipping Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"First Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_first_name,\n                name: \"shipping_first_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Last Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_last_name,\n                name: \"shipping_last_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Address\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.address1,\n                name: \"address1\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Phone\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_phone,\n                name: \"shipping_phone\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"City\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.city,\n                name: \"city\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Province\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.province,\n                name: \"province\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Country\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.country,\n                name: \"country\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"ZIP\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.zip,\n                name: \"zip\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveCustomerDetails,\n            color: \"primary\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCustomerCloseDialog,\n            color: \"primary\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            fontSize: '26px'\n          },\n          children: \"Select Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                sx: {\n                  background: '#f5f5f5'\n                },\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Variants\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Available Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Price\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: groupedProducts.map((productGroup, groupIndex) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: productGroup.image || '',\n                      alt: productGroup.title,\n                      style: {\n                        maxWidth: '80px',\n                        height: '80px',\n                        objectFit: 'cover'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      fontWeight: \"bold\",\n                      children: productGroup.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: productGroup.body_html\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          label: `${variant.option_1_value || 'Default'} ${variant.option_2_value ? '/ ' + variant.option_2_value : ''}`,\n                          size: \"small\",\n                          variant: \"outlined\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 824,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          children: [\"SKU: \", variant.sku]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 829,\n                          columnNumber: 61\n                        }, this)]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 823,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [variant.quantity || 0, \" units\"]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [\"$\", variant.price || 0]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 844,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        checked: selectedProducts.some(item => item.id === variant.id),\n                        onChange: () => handleCheckboxChange(variant)\n                      }, variant.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 45\n                  }, this)]\n                }, `group-${groupIndex}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveSelectedProducts,\n            color: \"primary\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            color: \"primary\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openTrackingDialog,\n        onClose: handleTrackingDialogClose,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Add Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Channel Order Id\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: channelOrderId,\n            onChange: e => setChannelOrderId(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Original Channel Order Id\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: originalChannelOrderId,\n            onChange: e => setOriginalChannelOrderId(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Channel Code\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: channelCode,\n            onChange: e => setChannelCode(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"SKU EAN\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: skuEan,\n            onChange: e => setSkuEan(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"SKU Code\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: skuCode,\n            onChange: e => setSkuCode(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tracking\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: tracking,\n            onChange: e => setTracking(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Shipment Date\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: shipmentDate,\n            onChange: e => setShipmentDate(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Carrier\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: carrier,\n            onChange: e => setCarrier(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Ship URL\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: shipUrl,\n            onChange: e => setShipUrl(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Is Return\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: isReturn,\n            onChange: e => setIsReturn(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleTrackingSubmit,\n            color: \"primary\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleTrackingDialogClose,\n            color: \"primary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(AddNewOrder, \"cPIFoqnRNqLNQ/drLt0IRl2srsE=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = AddNewOrder;\nvar _c;\n$RefreshReg$(_c, \"AddNewOrder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "TextField", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Box", "ArrowBackIcon", "SearchIcon", "AddIcon", "RemoveIcon", "axios", "NavBar", "Sidebar", "useParams", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddNewOrder", "_s", "navigate", "orderNo", "products", "setProducts", "groupedProducts", "setGroupedProducts", "selectedProducts", "setSelectedProducts", "productQuantities", "setProductQuantities", "formData", "setFormData", "first_name", "last_name", "email", "phone", "address1", "city", "province", "country", "zip", "responseData", "setResponseData", "paymentStatus", "setPaymentStatus", "totalPayment", "setTotalPayment", "openDialog", "setOpenDialog", "openCustomerDialog", "setOpenCustomerDialog", "anchorEl", "setAnchorEl", "paymentMethod", "setPaymentMethod", "customerAndShippingDetails", "setCustomerAndShippingDetails", "orderStatus", "setOrderStatus", "openTrackingDialog", "setOpenTrackingDialog", "channelOrderId", "setChannelOrderId", "originalChannelOrderId", "setOriginalChannelOrderId", "channelCode", "setChannelCode", "sku<PERSON>an", "setSkuEan", "skuCode", "setSkuCode", "tracking", "setTracking", "shipmentDate", "setShipmentDate", "carrier", "<PERSON><PERSON><PERSON><PERSON>", "shipUrl", "setShipUrl", "isReturn", "setIsReturn", "loading", "setLoading", "error", "setError", "fetchData", "storedGroupCode", "localStorage", "getItem", "resDetails", "post", "process", "env", "REACT_APP_BASE_URL", "order_no", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "data", "success", "map", "element", "id", "title", "product_name", "image", "price", "item_total_price", "quantity", "quantity_purchased", "total_tax", "item_tax", "line_item_id", "order_line_item_id", "order_status", "payment_status", "length", "item", "shipData", "recipient_name", "buyer_email", "ship_phone_number", "ship_address_1", "ship_city", "ship_state_code", "ship_country", "ship_postal_code", "console", "handleChange", "e", "name", "value", "target", "handleCheckboxChange", "product", "prevState", "isSelected", "some", "prev", "newQuantities", "filter", "handleQuantityChange", "productId", "newQuantity", "handleBrowse", "res", "body_html", "description", "inventory_quantity", "sku", "option_1_value", "option_2_value", "grouped", "reduce", "acc", "existingGroup", "find", "group", "variants", "push", "handleTrackingSubmit", "response", "log", "handleSaveSelectedProducts", "selectedProductsData", "handleSaveCustomerDetails", "newCustomerAndShippingDetails", "handleSave", "inventoryCheck", "every", "requestedQuantity", "availableQuantity", "requestData", "line_items", "variant_id", "customer", "billing_address", "shipping_address", "transactions", "kind", "status", "amount", "financial_status", "total_price", "url", "method", "updatedProducts", "selectedProduct", "sp", "usedQuantity", "Math", "max", "alert", "handleAddCustomerAndShipping", "handleCloseDialog", "handleCustomerCloseDialog", "handlePaymentClick", "event", "currentTarget", "handlePaymentClose", "handlePaymentMethod", "handleLineItemClick", "lineItemId", "totalPrice", "handleBack", "handleButtonClick", "handleTrackingDialogClose", "children", "selectedSidebarItem", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "style", "marginTop", "marginLeft", "xs", "md", "variant", "gutterBottom", "display", "alignItems", "onClick", "cursor", "fontSize", "color", "disabled", "marginBottom", "component", "label", "fullWidth", "size", "InputProps", "endAdornment", "sx", "background", "newproduct", "productQuantity", "src", "alt", "max<PERSON><PERSON><PERSON>", "gap", "type", "onChange", "parseInt", "inputProps", "min", "textAlign", "width", "open", "Boolean", "onClose", "sm", "shipping_first_name", "shipping_last_name", "shipping_phone", "productGroup", "groupIndex", "height", "objectFit", "fontWeight", "flexDirection", "checked", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Order/Addorder/AddNewOrder.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Card, CardContent, Typography, Grid, TextField, Button, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Menu, MenuItem, Select, FormControl, InputLabel, Chip, Box } from '@mui/material';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport SearchIcon from '@mui/icons-material/Search';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport RemoveIcon from '@mui/icons-material/Remove';\r\nimport axios from 'axios';\r\nimport { NavBar } from '../../../components/Navbar/Navbar';\r\nimport { Sidebar } from '../../../components/SidePanel/Sidebar';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\n\r\nexport const AddNewOrder = () => {\r\n    const navigate = useNavigate();\r\n    const { orderNo } = useParams()\r\n    const [products, setProducts] = useState([]);\r\n    const [groupedProducts, setGroupedProducts] = useState([]);\r\n    const [selectedProducts, setSelectedProducts] = useState([]);\r\n    const [productQuantities, setProductQuantities] = useState({});\r\n    const [formData, setFormData] = useState({\r\n        first_name: '',\r\n        last_name: '',\r\n        email: '',\r\n        phone: '',\r\n        address1: '',\r\n        city: '',\r\n        province: '',\r\n        country: '',\r\n        zip: '',\r\n    })\r\n    const [responseData, setResponseData] = useState([]);\r\n    const [paymentStatus, setPaymentStatus] = useState('Pending');\r\n    const [totalPayment, setTotalPayment] = useState(0);\r\n    const [openDialog, setOpenDialog] = useState(false);\r\n    const [openCustomerDialog, setOpenCustomerDialog] = useState(false);\r\n    const [anchorEl, setAnchorEl] = useState(null);\r\n    const [paymentMethod, setPaymentMethod] = useState('');\r\n    const [customerAndShippingDetails, setCustomerAndShippingDetails] = useState(null);\r\n    const [orderStatus, setOrderStatus] = useState('fulfilled');\r\n    const [openTrackingDialog, setOpenTrackingDialog] = useState(false);\r\n    const [channelOrderId, setChannelOrderId] = useState('');\r\n    const [originalChannelOrderId, setOriginalChannelOrderId] = useState('');\r\n    const [channelCode, setChannelCode] = useState('');\r\n    const [skuEan, setSkuEan] = useState('');\r\n    const [skuCode, setSkuCode] = useState('');\r\n    const [tracking, setTracking] = useState('');\r\n    const [shipmentDate, setShipmentDate] = useState('');\r\n    const [carrier, setCarrier] = useState('');\r\n    const [shipUrl, setShipUrl] = useState('');\r\n    const [isReturn, setIsReturn] = useState('');\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState('');\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            try {\r\n                if (orderNo) {\r\n                    // Fetch order details if orderNo exists\r\n                    var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n                    const resDetails = await axios.post(\r\n                        `${process.env.REACT_APP_BASE_URL}/common/api/get_order_detail?fby_user_id=${storedGroupCode}`,\r\n                        { order_no: orderNo },\r\n                        {\r\n                            headers: {\r\n                            'Content-Type': 'application/json',\r\n                            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\r\n                        }\r\n                        }\r\n                    );\r\n                    if (resDetails.data.success) {\r\n                        setResponseData(resDetails.data.success.data);\r\n                        const responseData = resDetails.data.success.data.map(element => ({\r\n                            id: element.id,\r\n                            title: element.product_name,\r\n                            image: element.image, // Assuming there's an image property in the response\r\n                            price: element.item_total_price,\r\n                            quantity: element.quantity_purchased,\r\n                            total_tax: element.item_tax,\r\n                            line_item_id: element.order_line_item_id,\r\n                            order_status: element.order_status,\r\n                            payment_status: element.payment_status\r\n                        }));\r\n                        if (responseData.length > 0) {\r\n                            setSelectedProducts(responseData.map(item => item)); // Update here\r\n                            setOrderStatus(responseData[0].order_status)\r\n                            setPaymentStatus(responseData[0].payment_status)\r\n                        }\r\n                        const shipData = resDetails.data.success.data.map(element => ({\r\n                            first_name: element.recipient_name,\r\n                            last_name: '',\r\n                            email: element.buyer_email,\r\n                            phone: element.ship_phone_number,\r\n                            address1: element.ship_address_1,\r\n                            city: element.ship_city,\r\n                            province: element.ship_state_code,\r\n                            country: element.ship_country,\r\n                            zip: element.ship_postal_code,\r\n                        }));\r\n                        // Set the customer and shipping details\r\n                        setCustomerAndShippingDetails(shipData[0]);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                console.error('Error fetching data:', error);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [orderNo]);\r\n\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData({ ...formData, [name]: value });\r\n    };\r\n\r\n    const handleCheckboxChange = (product) => {\r\n        setSelectedProducts(prevState => {\r\n            const isSelected = prevState.some(item => item.id === product.id);\r\n            if (!isSelected) {\r\n                // Initialize quantity when product is selected\r\n                setProductQuantities(prev => ({\r\n                    ...prev,\r\n                    [product.id]: 1\r\n                }));\r\n                return [...prevState, product];\r\n            } else {\r\n                // Remove quantity when product is deselected\r\n                setProductQuantities(prev => {\r\n                    const newQuantities = { ...prev };\r\n                    delete newQuantities[product.id];\r\n                    return newQuantities;\r\n                });\r\n                return prevState.filter(item => item.id !== product.id);\r\n            }\r\n        });\r\n    };\r\n\r\n    const handleQuantityChange = (productId, newQuantity) => {\r\n        setProductQuantities(prev => ({\r\n            ...prev,\r\n            [productId]: newQuantity\r\n        }));\r\n    };\r\n\r\n    const handleBrowse = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError('');\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n\r\n            const res = await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`,\r\n                {},\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\r\n                    }\r\n                }\r\n            );\r\n            if (res.data.success && res.data.success.data && res.data.success.data.length > 0) {\r\n                const responseData = res.data.success.data.map(element => ({\r\n                    id: element.id,\r\n                    title: element.title,\r\n                    body_html: element.description,\r\n                    image: element.image,\r\n                    price: element.price,\r\n                    quantity: element.inventory_quantity,\r\n                    sku: element.sku,\r\n                    option_1_value: element.option_1_value || '',\r\n                    option_2_value: element.option_2_value || '',\r\n                }));\r\n\r\n                // Group products by title\r\n                const grouped = responseData.reduce((acc, product) => {\r\n                    const existingGroup = acc.find(group => group.title === product.title);\r\n                    if (existingGroup) {\r\n                        existingGroup.variants.push(product);\r\n                    } else {\r\n                        acc.push({\r\n                            title: product.title,\r\n                            image: product.image,\r\n                            body_html: product.body_html,\r\n                            variants: [product]\r\n                        });\r\n                    }\r\n                    return acc;\r\n                }, []);\r\n\r\n                setProducts(responseData);\r\n                setGroupedProducts(grouped);\r\n                setOpenDialog(true);\r\n            } else {\r\n                setError('No products found');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fetching products:', error);\r\n            setError('Failed to load products. Please try again.');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Function to handle tracking submit\r\n    const handleTrackingSubmit = async () => {\r\n        try {\r\n            // Make API call to update tracking\r\n            const response = await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/push_tracking`,\r\n                {\r\n                    channelOrderId: channelOrderId,\r\n                    originalChannelOrderId: originalChannelOrderId,\r\n                    channelCode: channelCode,\r\n                    skuEan: skuEan,\r\n                    skuCode: skuCode,\r\n                    tracking: tracking,\r\n                    shipmentDate: shipmentDate,\r\n                    carrier: carrier,\r\n                    shipUrl: shipUrl,\r\n                    isReturn: isReturn\r\n                }\r\n            );\r\n            // Handle success response\r\n            console.log(response.data);\r\n        } catch (error) {\r\n            // Handle error\r\n            console.error('Error updating tracking:', error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleSaveSelectedProducts = () => {\r\n        setSelectedProducts(selectedProducts);\r\n\r\n        // You can save the selected products to a state or perform any other required action here\r\n        // For example, you can update a state with the selected products\r\n        const selectedProductsData = selectedProducts.map(productId => {\r\n            return products.find(product => product.id === productId);\r\n        });\r\n        // Do something with the selected products data, for example:\r\n        console.log('Selected products:', selectedProductsData);\r\n\r\n        setOpenDialog(false);\r\n    };\r\n\r\n    const handleSaveCustomerDetails = () => {\r\n        // Save the entered customer and shipping details\r\n        const newCustomerAndShippingDetails = {\r\n            first_name: formData.first_name,\r\n            last_name: formData.last_name,\r\n            email: formData.email,\r\n            phone: formData.phone,\r\n            address1: formData.address1,\r\n            city: formData.city,\r\n            province: formData.province,\r\n            country: formData.country,\r\n            zip: formData.zip,\r\n        };\r\n\r\n        // Set the customer and shipping details\r\n        setCustomerAndShippingDetails(newCustomerAndShippingDetails);\r\n\r\n        // Close the customer dialog\r\n        setOpenCustomerDialog(false);\r\n    };\r\n\r\n    const handleSave = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError('');\r\n\r\n            // Validate that we have customer details\r\n            if (!customerAndShippingDetails) {\r\n                setError('Please add customer and shipping details before saving the order.');\r\n                return;\r\n            }\r\n\r\n            // Validate that we have selected products\r\n            if (selectedProducts.length === 0) {\r\n                setError('Please select at least one product before saving the order.');\r\n                return;\r\n            }\r\n\r\n            // Check inventory availability\r\n            const inventoryCheck = selectedProducts.every(product => {\r\n                const requestedQuantity = productQuantities[product.id] || 1;\r\n                const availableQuantity = product.quantity || 0;\r\n                return requestedQuantity <= availableQuantity;\r\n            });\r\n\r\n            if (!inventoryCheck) {\r\n                setError('Some products do not have sufficient inventory. Please adjust quantities.');\r\n                return;\r\n            }\r\n\r\n            // Prepare the data for the request\r\n            const requestData = {\r\n                line_items: selectedProducts.map(product => {\r\n                    const quantity = productQuantities[product.id] || 1;\r\n                    return {\r\n                        variant_id: product.id,\r\n                        quantity: quantity,\r\n                        price: product.price || 0,\r\n                        sku: product.sku,\r\n                        title: product.title,\r\n                    };\r\n                }),\r\n                customer: {\r\n                    first_name: customerAndShippingDetails.first_name,\r\n                    last_name: customerAndShippingDetails.last_name,\r\n                    email: customerAndShippingDetails.email,\r\n                },\r\n                billing_address: {\r\n                    first_name: customerAndShippingDetails.first_name,\r\n                    last_name: customerAndShippingDetails.last_name,\r\n                    address1: customerAndShippingDetails.address1,\r\n                    city: customerAndShippingDetails.city,\r\n                    province: customerAndShippingDetails.province,\r\n                    country: customerAndShippingDetails.country,\r\n                    zip: customerAndShippingDetails.zip,\r\n                    phone: customerAndShippingDetails.phone,\r\n                },\r\n                shipping_address: {\r\n                    first_name: customerAndShippingDetails.first_name,\r\n                    last_name: customerAndShippingDetails.last_name,\r\n                    address1: customerAndShippingDetails.address1,\r\n                    city: customerAndShippingDetails.city,\r\n                    province: customerAndShippingDetails.province,\r\n                    country: customerAndShippingDetails.country,\r\n                    zip: customerAndShippingDetails.zip,\r\n                    phone: customerAndShippingDetails.phone,\r\n                },\r\n                email: customerAndShippingDetails.email,\r\n                transactions: [\r\n                    {\r\n                        kind: \"sale\",\r\n                        status: \"success\",\r\n                        amount: totalPayment,\r\n                    }\r\n                ],\r\n                financial_status: paymentStatus,\r\n                total_price: totalPayment,\r\n            };\r\n\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n\r\n            // Make the POST request\r\n            const res = await axios({\r\n                url: `${process.env.REACT_APP_BASE_URL}/common/api/create_shopify_order/?fby_user_id=${storedGroupCode}`,\r\n                method: \"POST\",\r\n                headers: {\r\n                    \"Content-Type\": \"application/json\",\r\n                    Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n                },\r\n                data: requestData,\r\n            });\r\n\r\n            console.log(res);\r\n            if (res.data.success) {\r\n                // Update local inventory quantities\r\n                const updatedProducts = products.map(product => {\r\n                    const selectedProduct = selectedProducts.find(sp => sp.id === product.id);\r\n                    if (selectedProduct) {\r\n                        const usedQuantity = productQuantities[product.id] || 1;\r\n                        return {\r\n                            ...product,\r\n                            quantity: Math.max(0, product.quantity - usedQuantity)\r\n                        };\r\n                    }\r\n                    return product;\r\n                });\r\n                setProducts(updatedProducts);\r\n\r\n                // Show success message and redirect\r\n                alert('Order created successfully!');\r\n                navigate('/orders');\r\n            } else {\r\n                setError('Failed to create order. Please try again.');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error saving Order:', error);\r\n            setError('Error creating order. Please try again.');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n\r\n    const handleAddCustomerAndShipping = () => {\r\n        setOpenCustomerDialog(true);\r\n    };\r\n\r\n    const handleCloseDialog = () => {\r\n        setOpenDialog(false);\r\n    };\r\n\r\n    const handleCustomerCloseDialog = () => {\r\n        setOpenCustomerDialog(false);\r\n    };\r\n    const handlePaymentClick = (event) => {\r\n        setAnchorEl(event.currentTarget);\r\n    };\r\n\r\n    const handlePaymentClose = () => {\r\n        setAnchorEl(null);\r\n    };\r\n\r\n    const handlePaymentMethod = (method) => {\r\n        setPaymentMethod(method);\r\n        setAnchorEl(null);\r\n        if (method === 'Mark as Paid') {\r\n            setPaymentStatus('Paid');\r\n        }\r\n    };\r\n\r\n    const handleLineItemClick = (lineItemId) => {\r\n        navigate(`/orderMaster/${orderNo}/orderDetails/${lineItemId}`);\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        // Calculate total payment with quantities\r\n        let totalPrice = 0;\r\n        for (const product of selectedProducts) {\r\n            const quantity = productQuantities[product.id] || 1;\r\n            const price = product.item_total_price || product.price || 0;\r\n            totalPrice += price * quantity;\r\n        }\r\n        setTotalPayment(totalPrice);\r\n    }, [selectedProducts, productQuantities]);\r\n\r\n    const handleBack = () => {\r\n        navigate(-1);\r\n    };\r\n\r\n    const handleButtonClick = () => {\r\n        setOpenTrackingDialog(true);\r\n    };\r\n\r\n    // Function to handle tracking dialog close\r\n    const handleTrackingDialogClose = () => {\r\n        setOpenTrackingDialog(false);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <NavBar selectedSidebarItem=\"products\" />\r\n            <Sidebar />\r\n            <Grid container spacing={3} style={{ marginTop: '45px', marginLeft: '255px' }}>\r\n                <Grid item xs={12} md={6}>\r\n                    <Typography variant=\"h5\" gutterBottom style={{ display: 'flex', alignItems: 'center' }}>\r\n                        <ArrowBackIcon onClick={handleBack} style={{ cursor: 'pointer' }} />\r\n                        <span style={{ fontSize: '1.2rem', marginLeft: 8 }}>Order</span>\r\n                        <Button\r\n                            variant=\"contained\"\r\n                            color=\"primary\"\r\n                            style={{ marginLeft: 'auto' }}\r\n                            onClick={handleSave}\r\n                            disabled={loading}\r\n                        >\r\n                            {loading ? 'Saving...' : 'Save'}\r\n                        </Button>\r\n                    </Typography>\r\n                    {error && (\r\n                        <Typography variant=\"body2\" color=\"error\" style={{ marginBottom: '16px' }}>\r\n                            {error}\r\n                        </Typography>\r\n                    )}\r\n                    <Grid container spacing={3}>\r\n                        <Grid item xs={12}>\r\n                            {(!orderNo) && (\r\n                                <Typography variant=\"h5\" component=\"h4\" style={{ marginBottom: 10 }}>Product</Typography>\r\n                            )}\r\n                            <div style={{ display: 'flex' }}>\r\n                                {(!orderNo) && (\r\n                                    <>\r\n                                        <TextField\r\n                                            label=\"Search Product\"\r\n                                            variant=\"outlined\"\r\n                                            fullWidth\r\n                                            size=\"small\"\r\n                                            InputProps={{\r\n                                                endAdornment: (\r\n                                                    <IconButton color=\"primary\" aria-label=\"search\" size=\"small\">\r\n                                                        <SearchIcon />\r\n                                                    </IconButton>\r\n                                                ),\r\n                                            }}\r\n                                        />\r\n                                        <Button\r\n                                            variant=\"outlined\"\r\n                                            style={{ marginLeft: 10, fontSize: '0.8rem' }}\r\n                                            size=\"small\"\r\n                                            onClick={handleBrowse}\r\n                                            disabled={loading}\r\n                                        >\r\n                                            {loading ? 'Loading...' : 'Browse'}\r\n                                        </Button>\r\n                                    </>\r\n                                )}\r\n                            </div>\r\n                        </Grid>\r\n                        {(selectedProducts.length > 0) && (\r\n                            <Grid item xs={12}>\r\n                                <TableContainer component={Paper}>\r\n                                    <Table>\r\n                                        <TableHead sx={{ background: '#f5f5f5' }}>\r\n                                            <TableRow>\r\n                                                <TableCell>Image</TableCell>\r\n                                                <TableCell>Title</TableCell>\r\n                                                <TableCell>Quantity</TableCell>\r\n                                                <TableCell>Price</TableCell>\r\n                                                {(orderNo) && (\r\n                                                    <TableCell>Line Order Id</TableCell>\r\n                                                )}\r\n                                            </TableRow>\r\n                                        </TableHead>\r\n                                        <TableBody>\r\n                                            {selectedProducts.map(newproduct => {\r\n                                                const productQuantity = productQuantities[newproduct.id] || 1;\r\n                                                return (\r\n                                                    <TableRow key={newproduct.id}>\r\n                                                        <TableCell><img src={(newproduct.image || '')} alt={newproduct.title} style={{ maxWidth: '100px' }} /></TableCell>\r\n                                                        <TableCell>\r\n                                                            <Typography variant=\"subtitle2\">{newproduct.title || ''}</Typography>\r\n                                                            {(newproduct.option_1_value || newproduct.option_2_value) && (\r\n                                                                <Typography variant=\"caption\" color=\"textSecondary\">\r\n                                                                    {newproduct.option_1_value} {newproduct.option_2_value && `/ ${newproduct.option_2_value}`}\r\n                                                                </Typography>\r\n                                                            )}\r\n                                                            <Typography variant=\"caption\" display=\"block\" color=\"textSecondary\">\r\n                                                                SKU: {newproduct.sku}\r\n                                                            </Typography>\r\n                                                        </TableCell>\r\n                                                        <TableCell>\r\n                                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                                                <IconButton\r\n                                                                    size=\"small\"\r\n                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.max(1, productQuantity - 1))}\r\n                                                                    disabled={productQuantity <= 1}\r\n                                                                >\r\n                                                                    <RemoveIcon />\r\n                                                                </IconButton>\r\n                                                                <TextField\r\n                                                                    size=\"small\"\r\n                                                                    type=\"number\"\r\n                                                                    value={productQuantity}\r\n                                                                    onChange={(e) => handleQuantityChange(newproduct.id, Math.max(1, parseInt(e.target.value) || 1))}\r\n                                                                    inputProps={{ min: 1, max: newproduct.quantity || 999, style: { textAlign: 'center', width: '60px' } }}\r\n                                                                />\r\n                                                                <IconButton\r\n                                                                    size=\"small\"\r\n                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.min((newproduct.quantity || 999), productQuantity + 1))}\r\n                                                                    disabled={productQuantity >= (newproduct.quantity || 999)}\r\n                                                                >\r\n                                                                    <AddIcon />\r\n                                                                </IconButton>\r\n                                                            </Box>\r\n                                                            <Typography variant=\"caption\" color=\"textSecondary\">\r\n                                                                Available: {newproduct.quantity || 0}\r\n                                                            </Typography>\r\n                                                        </TableCell>\r\n                                                        <TableCell>${(newproduct.price || 0) * productQuantity}</TableCell>\r\n                                                        {orderNo &&\r\n                                                            (<TableCell>\r\n                                                                <Button onClick={() => handleLineItemClick(newproduct.line_item_id)}>{newproduct.line_item_id}</Button>\r\n                                                            </TableCell>)\r\n                                                        }\r\n                                                    </TableRow>\r\n                                                );\r\n                                            })}\r\n                                        </TableBody>\r\n\r\n                                    </Table>\r\n                                </TableContainer>\r\n                            </Grid>\r\n                        )}\r\n\r\n                    </Grid>\r\n                    {/* Payment Information Card */}\r\n                    <Grid container spacing={3} style={{ marginTop: '20px', marginLeft: '5px' }}>\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h6\" component=\"h4\">Payment</Typography>\r\n                                <TableContainer component={Paper}>\r\n                                    <Table>\r\n                                        <TableBody>\r\n                                            <TableRow>\r\n                                                <TableCell>Payment Status:</TableCell>\r\n                                                <TableCell>{paymentStatus}</TableCell>\r\n                                            </TableRow>\r\n                                            <TableRow>\r\n                                                {orderStatus !== 'unfulfilled' && orderStatus !== 'partially fulfilled' ? (\r\n                                                    <>\r\n                                                        <TableCell>\r\n                                                            <Button variant=\"outlined\" color=\"primary\" onClick={handlePaymentClick}>\r\n                                                                Collect Payment\r\n                                                            </Button>\r\n                                                            <Menu\r\n                                                                anchorEl={anchorEl}\r\n                                                                open={Boolean(anchorEl)}\r\n                                                                onClose={handlePaymentClose}\r\n                                                            >\r\n                                                                <MenuItem onClick={() => handlePaymentMethod('Enter Credit Card')}>Enter Credit Card</MenuItem>\r\n                                                                <MenuItem onClick={() => handlePaymentMethod('Mark as Paid')}>Mark as Paid</MenuItem>\r\n                                                            </Menu>\r\n                                                        </TableCell>\r\n                                                        <TableCell>\r\n                                                            <Button variant=\"outlined\" color=\"primary\">\r\n                                                                Send Invoice\r\n                                                            </Button>\r\n                                                        </TableCell>\r\n                                                    </>\r\n                                                ) : (\r\n                                                    <TableCell>\r\n                                                        <Button variant=\"outlined\" color=\"primary\" onClick={handleButtonClick}>\r\n                                                            Add Tracking\r\n                                                        </Button>\r\n                                                    </TableCell>\r\n                                                )}\r\n                                            </TableRow>\r\n\r\n                                        </TableBody>\r\n                                    </Table>\r\n                                </TableContainer>\r\n                            </CardContent>\r\n                        </Card>\r\n                    </Grid>\r\n                </Grid>\r\n                <Grid item xs={12} md={3} style={{ marginTop: '80px' }}>\r\n                    {customerAndShippingDetails ? (\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h5\" component=\"h2\" style={{ fontSize: '1.2rem' }}>Customer and Shipping Details:</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Name: {customerAndShippingDetails.first_name}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Email: {customerAndShippingDetails.email}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Phone: {customerAndShippingDetails.phone}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Address: {customerAndShippingDetails.address1}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>City: {customerAndShippingDetails.city}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Province: {customerAndShippingDetails.province}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Country: {customerAndShippingDetails.country}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>ZIP: {customerAndShippingDetails.zip}</Typography>\r\n                            </CardContent>\r\n                        </Card>\r\n                    ) : (\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h5\" component=\"h2\" style={{ fontSize: '0.8rem' }}><h5>Add Customer and Shipping Details</h5>\r\n                                    <Button variant=\"contained\" color=\"primary\" onClick={handleAddCustomerAndShipping} style={{ fontSize: '0.8rem', marginLeft: '5px' }}>Add</Button>\r\n                                </Typography>\r\n                            </CardContent>\r\n                        </Card>\r\n                    )}\r\n                </Grid>\r\n                <Dialog open={openCustomerDialog} onClose={handleCustomerCloseDialog}>\r\n                    <DialogTitle>Customer and Shipping Details</DialogTitle>\r\n                    <DialogContent>\r\n                        <Grid container spacing={2}>\r\n                            <Grid item xs={12} sm={6}>\r\n                                <Typography variant=\"h6\" gutterBottom>Customer Data</Typography>\r\n                                <TextField\r\n                                    label=\"First Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.first_name}\r\n                                    name=\"first_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Last Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.last_name}\r\n                                    name=\"last_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Email\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.email}\r\n                                    name=\"email\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Phone\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.phone}\r\n                                    name=\"phone\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                {/* Add more input fields for customer data */}\r\n                            </Grid>\r\n                            <Grid item xs={12} sm={6}>\r\n                                <Typography variant=\"h6\" gutterBottom>Shipping Address</Typography>\r\n                                <TextField\r\n                                    label=\"First Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_first_name}\r\n                                    name=\"shipping_first_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Last Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_last_name}\r\n                                    name=\"shipping_last_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Address\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.address1}\r\n                                    name=\"address1\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Phone\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_phone}\r\n                                    name=\"shipping_phone\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"City\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.city}\r\n                                    name=\"city\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Province\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.province}\r\n                                    name=\"province\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Country\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.country}\r\n                                    name=\"country\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"ZIP\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.zip}\r\n                                    name=\"zip\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                {/* Add more input fields for shipping address */}\r\n                            </Grid>\r\n                        </Grid>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleSaveCustomerDetails} color=\"primary\">Save</Button>\r\n                        <Button onClick={handleCustomerCloseDialog} color=\"primary\">Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n\r\n                <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\r\n                    <DialogTitle sx={{ fontSize: '26px' }}>Select Products</DialogTitle>\r\n                    <DialogContent>\r\n                        <TableContainer component={Paper}>\r\n                            <Table>\r\n                                <TableHead sx={{ background: '#f5f5f5' }}>\r\n                                    <TableRow>\r\n                                        <TableCell>Image</TableCell>\r\n                                        <TableCell>Product</TableCell>\r\n                                        <TableCell>Variants</TableCell>\r\n                                        <TableCell>Available Stock</TableCell>\r\n                                        <TableCell>Price</TableCell>\r\n                                        <TableCell>Select</TableCell>\r\n                                    </TableRow>\r\n                                </TableHead>\r\n                                <TableBody>\r\n                                    {groupedProducts.map((productGroup, groupIndex) => (\r\n                                        <TableRow key={`group-${groupIndex}`}>\r\n                                            <TableCell>\r\n                                                <img src={productGroup.image || ''} alt={productGroup.title} style={{ maxWidth: '80px', height: '80px', objectFit: 'cover' }} />\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Typography variant=\"subtitle1\" fontWeight=\"bold\">{productGroup.title}</Typography>\r\n                                                <Typography variant=\"body2\" color=\"textSecondary\">{productGroup.body_html}</Typography>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <Box key={variant.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                                            <Chip\r\n                                                                label={`${variant.option_1_value || 'Default'} ${variant.option_2_value ? '/ ' + variant.option_2_value : ''}`}\r\n                                                                size=\"small\"\r\n                                                                variant=\"outlined\"\r\n                                                            />\r\n                                                            <Typography variant=\"caption\">SKU: {variant.sku}</Typography>\r\n                                                        </Box>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <Typography key={variant.id} variant=\"body2\">\r\n                                                            {variant.quantity || 0} units\r\n                                                        </Typography>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <Typography key={variant.id} variant=\"body2\">\r\n                                                            ${variant.price || 0}\r\n                                                        </Typography>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <input\r\n                                                            key={variant.id}\r\n                                                            type=\"checkbox\"\r\n                                                            checked={selectedProducts.some(item => item.id === variant.id)}\r\n                                                            onChange={() => handleCheckboxChange(variant)}\r\n                                                        />\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                        </TableRow>\r\n                                    ))}\r\n                                </TableBody>\r\n                            </Table>\r\n                        </TableContainer>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleSaveSelectedProducts} color=\"primary\">Save</Button>\r\n                        <Button onClick={handleCloseDialog} color=\"primary\">Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n                <Dialog open={openTrackingDialog} onClose={handleTrackingDialogClose}>\r\n                    <DialogTitle>Add Tracking</DialogTitle>\r\n                    <DialogContent>\r\n                        <TextField\r\n                            label=\"Channel Order Id\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={channelOrderId}\r\n                            onChange={(e) => setChannelOrderId(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Original Channel Order Id\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={originalChannelOrderId}\r\n                            onChange={(e) => setOriginalChannelOrderId(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Channel Code\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={channelCode}\r\n                            onChange={(e) => setChannelCode(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"SKU EAN\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={skuEan}\r\n                            onChange={(e) => setSkuEan(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"SKU Code\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={skuCode}\r\n                            onChange={(e) => setSkuCode(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Tracking\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={tracking}\r\n                            onChange={(e) => setTracking(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Shipment Date\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={shipmentDate}\r\n                            onChange={(e) => setShipmentDate(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Carrier\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={carrier}\r\n                            onChange={(e) => setCarrier(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Ship URL\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={shipUrl}\r\n                            onChange={(e) => setShipUrl(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Is Return\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={isReturn}\r\n                            onChange={(e) => setIsReturn(e.target.value)}\r\n                        />\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleTrackingSubmit} color=\"primary\">Submit</Button>\r\n                        <Button onClick={handleTrackingDialogClose} color=\"primary\">Cancel</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n            </Grid>\r\n        </>\r\n    );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AAC1R,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,OAAO,QAAQ,uCAAuC;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAQ,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC/B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC;IACrCsD,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,GAAG,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAClF,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,WAAW,CAAC;EAC3D,MAAM,CAACiF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACuF,WAAW,EAAEC,cAAc,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyF,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2F,OAAO,EAAEC,UAAU,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiG,OAAO,EAAEC,UAAU,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmG,OAAO,EAAEC,UAAU,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,OAAO,EAAEC,UAAU,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyG,KAAK,EAAEC,QAAQ,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACZ,MAAM0G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA,IAAIhE,OAAO,EAAE;UACT;UACA,IAAIiE,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;UACvD,MAAMC,UAAU,GAAG,MAAMhF,KAAK,CAACiF,IAAI,CAC/B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,4CAA4CP,eAAe,EAAE,EAC9F;YAAEQ,QAAQ,EAAEzE;UAAQ,CAAC,EACrB;YACI0E,OAAO,EAAE;cACT,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;YAC/D;UACA,CACJ,CAAC;UACD,IAAIR,UAAU,CAACS,IAAI,CAACC,OAAO,EAAE;YACzBzD,eAAe,CAAC+C,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAAC;YAC7C,MAAMzD,YAAY,GAAGgD,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;cAC9DC,EAAE,EAAED,OAAO,CAACC,EAAE;cACdC,KAAK,EAAEF,OAAO,CAACG,YAAY;cAC3BC,KAAK,EAAEJ,OAAO,CAACI,KAAK;cAAE;cACtBC,KAAK,EAAEL,OAAO,CAACM,gBAAgB;cAC/BC,QAAQ,EAAEP,OAAO,CAACQ,kBAAkB;cACpCC,SAAS,EAAET,OAAO,CAACU,QAAQ;cAC3BC,YAAY,EAAEX,OAAO,CAACY,kBAAkB;cACxCC,YAAY,EAAEb,OAAO,CAACa,YAAY;cAClCC,cAAc,EAAEd,OAAO,CAACc;YAC5B,CAAC,CAAC,CAAC;YACH,IAAI1E,YAAY,CAAC2E,MAAM,GAAG,CAAC,EAAE;cACzBzF,mBAAmB,CAACc,YAAY,CAAC2D,GAAG,CAACiB,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC;cACrD3D,cAAc,CAACjB,YAAY,CAAC,CAAC,CAAC,CAACyE,YAAY,CAAC;cAC5CtE,gBAAgB,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC0E,cAAc,CAAC;YACpD;YACA,MAAMG,QAAQ,GAAG7B,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;cAC1DrE,UAAU,EAAEqE,OAAO,CAACkB,cAAc;cAClCtF,SAAS,EAAE,EAAE;cACbC,KAAK,EAAEmE,OAAO,CAACmB,WAAW;cAC1BrF,KAAK,EAAEkE,OAAO,CAACoB,iBAAiB;cAChCrF,QAAQ,EAAEiE,OAAO,CAACqB,cAAc;cAChCrF,IAAI,EAAEgE,OAAO,CAACsB,SAAS;cACvBrF,QAAQ,EAAE+D,OAAO,CAACuB,eAAe;cACjCrF,OAAO,EAAE8D,OAAO,CAACwB,YAAY;cAC7BrF,GAAG,EAAE6D,OAAO,CAACyB;YACjB,CAAC,CAAC,CAAC;YACH;YACAtE,6BAA6B,CAAC8D,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC9C;QACJ;MACJ,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACZ4C,OAAO,CAAC5C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD;IACJ,CAAC;IAEDE,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,CAAChE,OAAO,CAAC,CAAC;EAGb,MAAM2G,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrG,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACoG,IAAI,GAAGC;IAAM,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,oBAAoB,GAAIC,OAAO,IAAK;IACtC3G,mBAAmB,CAAC4G,SAAS,IAAI;MAC7B,MAAMC,UAAU,GAAGD,SAAS,CAACE,IAAI,CAACpB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAKgC,OAAO,CAAChC,EAAE,CAAC;MACjE,IAAI,CAACkC,UAAU,EAAE;QACb;QACA3G,oBAAoB,CAAC6G,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP,CAACJ,OAAO,CAAChC,EAAE,GAAG;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,GAAGiC,SAAS,EAAED,OAAO,CAAC;MAClC,CAAC,MAAM;QACH;QACAzG,oBAAoB,CAAC6G,IAAI,IAAI;UACzB,MAAMC,aAAa,GAAG;YAAE,GAAGD;UAAK,CAAC;UACjC,OAAOC,aAAa,CAACL,OAAO,CAAChC,EAAE,CAAC;UAChC,OAAOqC,aAAa;QACxB,CAAC,CAAC;QACF,OAAOJ,SAAS,CAACK,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAKgC,OAAO,CAAChC,EAAE,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAMuC,oBAAoB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;IACrDlH,oBAAoB,CAAC6G,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACI,SAAS,GAAGC;IACjB,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA9D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MACZ,IAAIE,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAEvD,MAAMyD,GAAG,GAAG,MAAMxI,KAAK,CAACiF,IAAI,CACxB,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,mDAAmDP,eAAe,EAAE,EACrG,CAAC,CAAC,EACF;QACIS,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC/D;MACJ,CACJ,CAAC;MACD,IAAIgD,GAAG,CAAC/C,IAAI,CAACC,OAAO,IAAI8C,GAAG,CAAC/C,IAAI,CAACC,OAAO,CAACD,IAAI,IAAI+C,GAAG,CAAC/C,IAAI,CAACC,OAAO,CAACD,IAAI,CAACkB,MAAM,GAAG,CAAC,EAAE;QAC/E,MAAM3E,YAAY,GAAGwG,GAAG,CAAC/C,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;UACvDC,EAAE,EAAED,OAAO,CAACC,EAAE;UACdC,KAAK,EAAEF,OAAO,CAACE,KAAK;UACpB2C,SAAS,EAAE7C,OAAO,CAAC8C,WAAW;UAC9B1C,KAAK,EAAEJ,OAAO,CAACI,KAAK;UACpBC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBE,QAAQ,EAAEP,OAAO,CAAC+C,kBAAkB;UACpCC,GAAG,EAAEhD,OAAO,CAACgD,GAAG;UAChBC,cAAc,EAAEjD,OAAO,CAACiD,cAAc,IAAI,EAAE;UAC5CC,cAAc,EAAElD,OAAO,CAACkD,cAAc,IAAI;QAC9C,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,OAAO,GAAG/G,YAAY,CAACgH,MAAM,CAAC,CAACC,GAAG,EAAEpB,OAAO,KAAK;UAClD,MAAMqB,aAAa,GAAGD,GAAG,CAACE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACtD,KAAK,KAAK+B,OAAO,CAAC/B,KAAK,CAAC;UACtE,IAAIoD,aAAa,EAAE;YACfA,aAAa,CAACG,QAAQ,CAACC,IAAI,CAACzB,OAAO,CAAC;UACxC,CAAC,MAAM;YACHoB,GAAG,CAACK,IAAI,CAAC;cACLxD,KAAK,EAAE+B,OAAO,CAAC/B,KAAK;cACpBE,KAAK,EAAE6B,OAAO,CAAC7B,KAAK;cACpByC,SAAS,EAAEZ,OAAO,CAACY,SAAS;cAC5BY,QAAQ,EAAE,CAACxB,OAAO;YACtB,CAAC,CAAC;UACN;UACA,OAAOoB,GAAG;QACd,CAAC,EAAE,EAAE,CAAC;QAENnI,WAAW,CAACkB,YAAY,CAAC;QACzBhB,kBAAkB,CAAC+H,OAAO,CAAC;QAC3BxG,aAAa,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACHoC,QAAQ,CAAC,mBAAmB,CAAC;MACjC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZ4C,OAAO,CAAC5C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,4CAA4C,CAAC;IAC1D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM8E,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA;MACA,MAAMC,QAAQ,GAAG,MAAMxJ,KAAK,CAACiF,IAAI,CAC7B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,2BAA2B,EAC5D;QACIhC,cAAc,EAAEA,cAAc;QAC9BE,sBAAsB,EAAEA,sBAAsB;QAC9CE,WAAW,EAAEA,WAAW;QACxBE,MAAM,EAAEA,MAAM;QACdE,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAEA,QAAQ;QAClBE,YAAY,EAAEA,YAAY;QAC1BE,OAAO,EAAEA,OAAO;QAChBE,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAEA;MACd,CACJ,CAAC;MACD;MACAgD,OAAO,CAACmC,GAAG,CAACD,QAAQ,CAAC/D,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZ;MACA4C,OAAO,CAAC5C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;EAGD,MAAMgF,0BAA0B,GAAGA,CAAA,KAAM;IACrCxI,mBAAmB,CAACD,gBAAgB,CAAC;;IAErC;IACA;IACA,MAAM0I,oBAAoB,GAAG1I,gBAAgB,CAAC0E,GAAG,CAAC0C,SAAS,IAAI;MAC3D,OAAOxH,QAAQ,CAACsI,IAAI,CAACtB,OAAO,IAAIA,OAAO,CAAChC,EAAE,KAAKwC,SAAS,CAAC;IAC7D,CAAC,CAAC;IACF;IACAf,OAAO,CAACmC,GAAG,CAAC,oBAAoB,EAAEE,oBAAoB,CAAC;IAEvDpH,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMqH,yBAAyB,GAAGA,CAAA,KAAM;IACpC;IACA,MAAMC,6BAA6B,GAAG;MAClCtI,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;MAC7BC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;MACrBC,KAAK,EAAEL,QAAQ,CAACK,KAAK;MACrBC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;MAC3BC,IAAI,EAAEP,QAAQ,CAACO,IAAI;MACnBC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;MAC3BC,OAAO,EAAET,QAAQ,CAACS,OAAO;MACzBC,GAAG,EAAEV,QAAQ,CAACU;IAClB,CAAC;;IAED;IACAgB,6BAA6B,CAAC8G,6BAA6B,CAAC;;IAE5D;IACApH,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMqH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACArF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,IAAI,CAAC7B,0BAA0B,EAAE;QAC7B6B,QAAQ,CAAC,mEAAmE,CAAC;QAC7E;MACJ;;MAEA;MACA,IAAI1D,gBAAgB,CAAC0F,MAAM,KAAK,CAAC,EAAE;QAC/BhC,QAAQ,CAAC,6DAA6D,CAAC;QACvE;MACJ;;MAEA;MACA,MAAMoF,cAAc,GAAG9I,gBAAgB,CAAC+I,KAAK,CAACnC,OAAO,IAAI;QACrD,MAAMoC,iBAAiB,GAAG9I,iBAAiB,CAAC0G,OAAO,CAAChC,EAAE,CAAC,IAAI,CAAC;QAC5D,MAAMqE,iBAAiB,GAAGrC,OAAO,CAAC1B,QAAQ,IAAI,CAAC;QAC/C,OAAO8D,iBAAiB,IAAIC,iBAAiB;MACjD,CAAC,CAAC;MAEF,IAAI,CAACH,cAAc,EAAE;QACjBpF,QAAQ,CAAC,2EAA2E,CAAC;QACrF;MACJ;;MAEA;MACA,MAAMwF,WAAW,GAAG;QAChBC,UAAU,EAAEnJ,gBAAgB,CAAC0E,GAAG,CAACkC,OAAO,IAAI;UACxC,MAAM1B,QAAQ,GAAGhF,iBAAiB,CAAC0G,OAAO,CAAChC,EAAE,CAAC,IAAI,CAAC;UACnD,OAAO;YACHwE,UAAU,EAAExC,OAAO,CAAChC,EAAE;YACtBM,QAAQ,EAAEA,QAAQ;YAClBF,KAAK,EAAE4B,OAAO,CAAC5B,KAAK,IAAI,CAAC;YACzB2C,GAAG,EAAEf,OAAO,CAACe,GAAG;YAChB9C,KAAK,EAAE+B,OAAO,CAAC/B;UACnB,CAAC;QACL,CAAC,CAAC;QACFwE,QAAQ,EAAE;UACN/I,UAAU,EAAEuB,0BAA0B,CAACvB,UAAU;UACjDC,SAAS,EAAEsB,0BAA0B,CAACtB,SAAS;UAC/CC,KAAK,EAAEqB,0BAA0B,CAACrB;QACtC,CAAC;QACD8I,eAAe,EAAE;UACbhJ,UAAU,EAAEuB,0BAA0B,CAACvB,UAAU;UACjDC,SAAS,EAAEsB,0BAA0B,CAACtB,SAAS;UAC/CG,QAAQ,EAAEmB,0BAA0B,CAACnB,QAAQ;UAC7CC,IAAI,EAAEkB,0BAA0B,CAAClB,IAAI;UACrCC,QAAQ,EAAEiB,0BAA0B,CAACjB,QAAQ;UAC7CC,OAAO,EAAEgB,0BAA0B,CAAChB,OAAO;UAC3CC,GAAG,EAAEe,0BAA0B,CAACf,GAAG;UACnCL,KAAK,EAAEoB,0BAA0B,CAACpB;QACtC,CAAC;QACD8I,gBAAgB,EAAE;UACdjJ,UAAU,EAAEuB,0BAA0B,CAACvB,UAAU;UACjDC,SAAS,EAAEsB,0BAA0B,CAACtB,SAAS;UAC/CG,QAAQ,EAAEmB,0BAA0B,CAACnB,QAAQ;UAC7CC,IAAI,EAAEkB,0BAA0B,CAAClB,IAAI;UACrCC,QAAQ,EAAEiB,0BAA0B,CAACjB,QAAQ;UAC7CC,OAAO,EAAEgB,0BAA0B,CAAChB,OAAO;UAC3CC,GAAG,EAAEe,0BAA0B,CAACf,GAAG;UACnCL,KAAK,EAAEoB,0BAA0B,CAACpB;QACtC,CAAC;QACDD,KAAK,EAAEqB,0BAA0B,CAACrB,KAAK;QACvCgJ,YAAY,EAAE,CACV;UACIC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAExI;QACZ,CAAC,CACJ;QACDyI,gBAAgB,EAAE3I,aAAa;QAC/B4I,WAAW,EAAE1I;MACjB,CAAC;MAED,IAAIyC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;;MAEvD;MACA,MAAMyD,GAAG,GAAG,MAAMxI,KAAK,CAAC;QACpB+K,GAAG,EAAE,GAAG7F,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDP,eAAe,EAAE;QACxGmG,MAAM,EAAE,MAAM;QACd1F,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC/D,CAAC;QACDC,IAAI,EAAE0E;MACV,CAAC,CAAC;MAEF7C,OAAO,CAACmC,GAAG,CAACjB,GAAG,CAAC;MAChB,IAAIA,GAAG,CAAC/C,IAAI,CAACC,OAAO,EAAE;QAClB;QACA,MAAMuF,eAAe,GAAGpK,QAAQ,CAAC8E,GAAG,CAACkC,OAAO,IAAI;UAC5C,MAAMqD,eAAe,GAAGjK,gBAAgB,CAACkI,IAAI,CAACgC,EAAE,IAAIA,EAAE,CAACtF,EAAE,KAAKgC,OAAO,CAAChC,EAAE,CAAC;UACzE,IAAIqF,eAAe,EAAE;YACjB,MAAME,YAAY,GAAGjK,iBAAiB,CAAC0G,OAAO,CAAChC,EAAE,CAAC,IAAI,CAAC;YACvD,OAAO;cACH,GAAGgC,OAAO;cACV1B,QAAQ,EAAEkF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzD,OAAO,CAAC1B,QAAQ,GAAGiF,YAAY;YACzD,CAAC;UACL;UACA,OAAOvD,OAAO;QAClB,CAAC,CAAC;QACF/G,WAAW,CAACmK,eAAe,CAAC;;QAE5B;QACAM,KAAK,CAAC,6BAA6B,CAAC;QACpC5K,QAAQ,CAAC,SAAS,CAAC;MACvB,CAAC,MAAM;QACHgE,QAAQ,CAAC,2CAA2C,CAAC;MACzD;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZ4C,OAAO,CAAC5C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAGD,MAAM+G,4BAA4B,GAAGA,CAAA,KAAM;IACvC/I,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMgJ,iBAAiB,GAAGA,CAAA,KAAM;IAC5BlJ,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMmJ,yBAAyB,GAAGA,CAAA,KAAM;IACpCjJ,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EACD,MAAMkJ,kBAAkB,GAAIC,KAAK,IAAK;IAClCjJ,WAAW,CAACiJ,KAAK,CAACC,aAAa,CAAC;EACpC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7BnJ,WAAW,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoJ,mBAAmB,GAAIf,MAAM,IAAK;IACpCnI,gBAAgB,CAACmI,MAAM,CAAC;IACxBrI,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIqI,MAAM,KAAK,cAAc,EAAE;MAC3B7I,gBAAgB,CAAC,MAAM,CAAC;IAC5B;EACJ,CAAC;EAED,MAAM6J,mBAAmB,GAAIC,UAAU,IAAK;IACxCtL,QAAQ,CAAC,gBAAgBC,OAAO,iBAAiBqL,UAAU,EAAE,CAAC;EAClE,CAAC;EAGD/N,SAAS,CAAC,MAAM;IACZ;IACA,IAAIgO,UAAU,GAAG,CAAC;IAClB,KAAK,MAAMrE,OAAO,IAAI5G,gBAAgB,EAAE;MACpC,MAAMkF,QAAQ,GAAGhF,iBAAiB,CAAC0G,OAAO,CAAChC,EAAE,CAAC,IAAI,CAAC;MACnD,MAAMI,KAAK,GAAG4B,OAAO,CAAC3B,gBAAgB,IAAI2B,OAAO,CAAC5B,KAAK,IAAI,CAAC;MAC5DiG,UAAU,IAAIjG,KAAK,GAAGE,QAAQ;IAClC;IACA9D,eAAe,CAAC6J,UAAU,CAAC;EAC/B,CAAC,EAAE,CAACjL,gBAAgB,EAAEE,iBAAiB,CAAC,CAAC;EAEzC,MAAMgL,UAAU,GAAGA,CAAA,KAAM;IACrBxL,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMyL,iBAAiB,GAAGA,CAAA,KAAM;IAC5BjJ,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMkJ,yBAAyB,GAAGA,CAAA,KAAM;IACpClJ,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,oBACI7C,OAAA,CAAAE,SAAA;IAAA8L,QAAA,gBACIhM,OAAA,CAACL,MAAM;MAACsM,mBAAmB,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCrM,OAAA,CAACJ,OAAO;MAAAsM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXrM,OAAA,CAAChC,IAAI;MAACsO,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAV,QAAA,gBAC1EhM,OAAA,CAAChC,IAAI;QAACsI,IAAI;QAACqG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,gBACrBhM,OAAA,CAACjC,UAAU;UAAC8O,OAAO,EAAC,IAAI;UAACC,YAAY;UAACN,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAhB,QAAA,gBACnFhM,OAAA,CAACV,aAAa;YAAC2N,OAAO,EAAEpB,UAAW;YAACW,KAAK,EAAE;cAAEU,MAAM,EAAE;YAAU;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpErM,OAAA;YAAMwM,KAAK,EAAE;cAAEW,QAAQ,EAAE,QAAQ;cAAET,UAAU,EAAE;YAAE,CAAE;YAAAV,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChErM,OAAA,CAAC9B,MAAM;YACH2O,OAAO,EAAC,WAAW;YACnBO,KAAK,EAAC,SAAS;YACfZ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAC9BO,OAAO,EAAEzD,UAAW;YACpB6D,QAAQ,EAAEnJ,OAAQ;YAAA8H,QAAA,EAEjB9H,OAAO,GAAG,WAAW,GAAG;UAAM;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACZjI,KAAK,iBACFpE,OAAA,CAACjC,UAAU;UAAC8O,OAAO,EAAC,OAAO;UAACO,KAAK,EAAC,OAAO;UAACZ,KAAK,EAAE;YAAEc,YAAY,EAAE;UAAO,CAAE;UAAAtB,QAAA,EACrE5H;QAAK;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACf,eACDrM,OAAA,CAAChC,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACvBhM,OAAA,CAAChC,IAAI;YAACsI,IAAI;YAACqG,EAAE,EAAE,EAAG;YAAAX,QAAA,GACZ,CAAC1L,OAAO,iBACNN,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,IAAI;cAACU,SAAS,EAAC,IAAI;cAACf,KAAK,EAAE;gBAAEc,YAAY,EAAE;cAAG,CAAE;cAAAtB,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC3F,eACDrM,OAAA;cAAKwM,KAAK,EAAE;gBAAEO,OAAO,EAAE;cAAO,CAAE;cAAAf,QAAA,EAC1B,CAAC1L,OAAO,iBACNN,OAAA,CAAAE,SAAA;gBAAA8L,QAAA,gBACIhM,OAAA,CAAC/B,SAAS;kBACNuP,KAAK,EAAC,gBAAgB;kBACtBX,OAAO,EAAC,UAAU;kBAClBY,SAAS;kBACTC,IAAI,EAAC,OAAO;kBACZC,UAAU,EAAE;oBACRC,YAAY,eACR5N,OAAA,CAAC7B,UAAU;sBAACiP,KAAK,EAAC,SAAS;sBAAC,cAAW,QAAQ;sBAACM,IAAI,EAAC,OAAO;sBAAA1B,QAAA,eACxDhM,OAAA,CAACT,UAAU;wBAAA2M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAEpB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFrM,OAAA,CAAC9B,MAAM;kBACH2O,OAAO,EAAC,UAAU;kBAClBL,KAAK,EAAE;oBAAEE,UAAU,EAAE,EAAE;oBAAES,QAAQ,EAAE;kBAAS,CAAE;kBAC9CO,IAAI,EAAC,OAAO;kBACZT,OAAO,EAAEhF,YAAa;kBACtBoF,QAAQ,EAAEnJ,OAAQ;kBAAA8H,QAAA,EAEjB9H,OAAO,GAAG,YAAY,GAAG;gBAAQ;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA,eACX;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL1L,gBAAgB,CAAC0F,MAAM,GAAG,CAAC,iBACzBrG,OAAA,CAAChC,IAAI;YAACsI,IAAI;YAACqG,EAAE,EAAE,EAAG;YAAAX,QAAA,eACdhM,OAAA,CAACzB,cAAc;cAACgP,SAAS,EAAE7O,KAAM;cAAAsN,QAAA,eAC7BhM,OAAA,CAAC5B,KAAK;gBAAA4N,QAAA,gBACFhM,OAAA,CAACxB,SAAS;kBAACqP,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,eACrChM,OAAA,CAACvB,QAAQ;oBAAAuN,QAAA,gBACLhM,OAAA,CAAC1B,SAAS;sBAAA0N,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BrM,OAAA,CAAC1B,SAAS;sBAAA0N,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BrM,OAAA,CAAC1B,SAAS;sBAAA0N,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/BrM,OAAA,CAAC1B,SAAS;sBAAA0N,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,EAC1B/L,OAAO,iBACLN,OAAA,CAAC1B,SAAS;sBAAA0N,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CACtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZrM,OAAA,CAAC3B,SAAS;kBAAA2N,QAAA,EACLrL,gBAAgB,CAAC0E,GAAG,CAAC0I,UAAU,IAAI;oBAChC,MAAMC,eAAe,GAAGnN,iBAAiB,CAACkN,UAAU,CAACxI,EAAE,CAAC,IAAI,CAAC;oBAC7D,oBACIvF,OAAA,CAACvB,QAAQ;sBAAAuN,QAAA,gBACLhM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,eAAChM,OAAA;0BAAKiO,GAAG,EAAGF,UAAU,CAACrI,KAAK,IAAI,EAAI;0BAACwI,GAAG,EAAEH,UAAU,CAACvI,KAAM;0BAACgH,KAAK,EAAE;4BAAE2B,QAAQ,EAAE;0BAAQ;wBAAE;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAClHrM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,gBACNhM,OAAA,CAACjC,UAAU;0BAAC8O,OAAO,EAAC,WAAW;0BAAAb,QAAA,EAAE+B,UAAU,CAACvI,KAAK,IAAI;wBAAE;0BAAA0G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,EACpE,CAAC0B,UAAU,CAACxF,cAAc,IAAIwF,UAAU,CAACvF,cAAc,kBACpDxI,OAAA,CAACjC,UAAU;0BAAC8O,OAAO,EAAC,SAAS;0BAACO,KAAK,EAAC,eAAe;0BAAApB,QAAA,GAC9C+B,UAAU,CAACxF,cAAc,EAAC,GAAC,EAACwF,UAAU,CAACvF,cAAc,IAAI,KAAKuF,UAAU,CAACvF,cAAc,EAAE;wBAAA;0BAAA0D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClF,CACf,eACDrM,OAAA,CAACjC,UAAU;0BAAC8O,OAAO,EAAC,SAAS;0BAACE,OAAO,EAAC,OAAO;0BAACK,KAAK,EAAC,eAAe;0BAAApB,QAAA,GAAC,OAC3D,EAAC+B,UAAU,CAACzF,GAAG;wBAAA;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACZrM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,gBACNhM,OAAA,CAACX,GAAG;0BAACwO,EAAE,EAAE;4BAAEd,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEoB,GAAG,EAAE;0BAAE,CAAE;0BAAApC,QAAA,gBACvDhM,OAAA,CAAC7B,UAAU;4BACPuP,IAAI,EAAC,OAAO;4BACZT,OAAO,EAAEA,CAAA,KAAMnF,oBAAoB,CAACiG,UAAU,CAACxI,EAAE,EAAEwF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEgD,eAAe,GAAG,CAAC,CAAC,CAAE;4BACrFX,QAAQ,EAAEW,eAAe,IAAI,CAAE;4BAAAhC,QAAA,eAE/BhM,OAAA,CAACP,UAAU;8BAAAyM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACbrM,OAAA,CAAC/B,SAAS;4BACNyP,IAAI,EAAC,OAAO;4BACZW,IAAI,EAAC,QAAQ;4BACbjH,KAAK,EAAE4G,eAAgB;4BACvBM,QAAQ,EAAGpH,CAAC,IAAKY,oBAAoB,CAACiG,UAAU,CAACxI,EAAE,EAAEwF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEuD,QAAQ,CAACrH,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAE;4BACjGoH,UAAU,EAAE;8BAAEC,GAAG,EAAE,CAAC;8BAAEzD,GAAG,EAAE+C,UAAU,CAAClI,QAAQ,IAAI,GAAG;8BAAE2G,KAAK,EAAE;gCAAEkC,SAAS,EAAE,QAAQ;gCAAEC,KAAK,EAAE;8BAAO;4BAAE;0BAAE;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1G,CAAC,eACFrM,OAAA,CAAC7B,UAAU;4BACPuP,IAAI,EAAC,OAAO;4BACZT,OAAO,EAAEA,CAAA,KAAMnF,oBAAoB,CAACiG,UAAU,CAACxI,EAAE,EAAEwF,IAAI,CAAC0D,GAAG,CAAEV,UAAU,CAAClI,QAAQ,IAAI,GAAG,EAAGmI,eAAe,GAAG,CAAC,CAAC,CAAE;4BAChHX,QAAQ,EAAEW,eAAe,KAAKD,UAAU,CAAClI,QAAQ,IAAI,GAAG,CAAE;4BAAAmG,QAAA,eAE1DhM,OAAA,CAACR,OAAO;8BAAA0M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNrM,OAAA,CAACjC,UAAU;0BAAC8O,OAAO,EAAC,SAAS;0BAACO,KAAK,EAAC,eAAe;0BAAApB,QAAA,GAAC,aACrC,EAAC+B,UAAU,CAAClI,QAAQ,IAAI,CAAC;wBAAA;0BAAAqG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACZrM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,GAAC,GAAC,EAAC,CAAC+B,UAAU,CAACpI,KAAK,IAAI,CAAC,IAAIqI,eAAe;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,EAClE/L,OAAO,iBACHN,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,eACPhM,OAAA,CAAC9B,MAAM;0BAAC+O,OAAO,EAAEA,CAAA,KAAMvB,mBAAmB,CAACqC,UAAU,CAAC9H,YAAY,CAAE;0BAAA+F,QAAA,EAAE+B,UAAU,CAAC9H;wBAAY;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAE;oBAAA,GA7CN0B,UAAU,CAACxI,EAAE;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+ClB,CAAC;kBAEnB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,eAEPrM,OAAA,CAAChC,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,SAAS,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,eACxEhM,OAAA,CAACnC,IAAI;YAAAmO,QAAA,eACDhM,OAAA,CAAClC,WAAW;cAAAkO,QAAA,gBACRhM,OAAA,CAACjC,UAAU;gBAAC8O,OAAO,EAAC,IAAI;gBAACU,SAAS,EAAC,IAAI;gBAAAvB,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5DrM,OAAA,CAACzB,cAAc;gBAACgP,SAAS,EAAE7O,KAAM;gBAAAsN,QAAA,eAC7BhM,OAAA,CAAC5B,KAAK;kBAAA4N,QAAA,eACFhM,OAAA,CAAC3B,SAAS;oBAAA2N,QAAA,gBACNhM,OAAA,CAACvB,QAAQ;sBAAAuN,QAAA,gBACLhM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtCrM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,EAAEpK;sBAAa;wBAAAsK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACXrM,OAAA,CAACvB,QAAQ;sBAAAuN,QAAA,EACJtJ,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,qBAAqB,gBACnE1C,OAAA,CAAAE,SAAA;wBAAA8L,QAAA,gBACIhM,OAAA,CAAC1B,SAAS;0BAAA0N,QAAA,gBACNhM,OAAA,CAAC9B,MAAM;4BAAC2O,OAAO,EAAC,UAAU;4BAACO,KAAK,EAAC,SAAS;4BAACH,OAAO,EAAE5B,kBAAmB;4BAAAW,QAAA,EAAC;0BAExE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTrM,OAAA,CAACjB,IAAI;4BACDqD,QAAQ,EAAEA,QAAS;4BACnBwM,IAAI,EAAEC,OAAO,CAACzM,QAAQ,CAAE;4BACxB0M,OAAO,EAAEtD,kBAAmB;4BAAAQ,QAAA,gBAE5BhM,OAAA,CAAChB,QAAQ;8BAACiO,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAAC,mBAAmB,CAAE;8BAAAO,QAAA,EAAC;4BAAiB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC,eAC/FrM,OAAA,CAAChB,QAAQ;8BAACiO,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAAC,cAAc,CAAE;8BAAAO,QAAA,EAAC;4BAAY;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACZrM,OAAA,CAAC1B,SAAS;0BAAA0N,QAAA,eACNhM,OAAA,CAAC9B,MAAM;4BAAC2O,OAAO,EAAC,UAAU;4BAACO,KAAK,EAAC,SAAS;4BAAApB,QAAA,EAAC;0BAE3C;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,eACd,CAAC,gBAEHrM,OAAA,CAAC1B,SAAS;wBAAA0N,QAAA,eACNhM,OAAA,CAAC9B,MAAM;0BAAC2O,OAAO,EAAC,UAAU;0BAACO,KAAK,EAAC,SAAS;0BAACH,OAAO,EAAEnB,iBAAkB;0BAAAE,QAAA,EAAC;wBAEvE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBACd;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPrM,OAAA,CAAChC,IAAI;QAACsI,IAAI;QAACqG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACJ,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAT,QAAA,EAClDxJ,0BAA0B,gBACvBxC,OAAA,CAACnC,IAAI;UAAAmO,QAAA,eACDhM,OAAA,CAAClC,WAAW;YAAAkO,QAAA,gBACRhM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,IAAI;cAACU,SAAS,EAAC,IAAI;cAACf,KAAK,EAAE;gBAAEW,QAAQ,EAAE;cAAS,CAAE;cAAAnB,QAAA,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClHrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,QAAM,EAACxJ,0BAA0B,CAACvB,UAAU;YAAA;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnGrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,SAAO,EAACxJ,0BAA0B,CAACrB,KAAK;YAAA;cAAA+K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC/FrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,SAAO,EAACxJ,0BAA0B,CAACpB,KAAK;YAAA;cAAA8K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC/FrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,WAAS,EAACxJ,0BAA0B,CAACnB,QAAQ;YAAA;cAAA6K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACpGrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,QAAM,EAACxJ,0BAA0B,CAAClB,IAAI;YAAA;cAAA4K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7FrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,YAAU,EAACxJ,0BAA0B,CAACjB,QAAQ;YAAA;cAAA2K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrGrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,WAAS,EAACxJ,0BAA0B,CAAChB,OAAO;YAAA;cAAA0K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnGrM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,OAAK,EAACxJ,0BAA0B,CAACf,GAAG;YAAA;cAAAyK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,gBAEPrM,OAAA,CAACnC,IAAI;UAAAmO,QAAA,eACDhM,OAAA,CAAClC,WAAW;YAAAkO,QAAA,eACRhM,OAAA,CAACjC,UAAU;cAAC8O,OAAO,EAAC,IAAI;cAACU,SAAS,EAAC,IAAI;cAACf,KAAK,EAAE;gBAAEW,QAAQ,EAAE;cAAS,CAAE;cAAAnB,QAAA,gBAAChM,OAAA;gBAAAgM,QAAA,EAAI;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GrM,OAAA,CAAC9B,MAAM;gBAAC2O,OAAO,EAAC,WAAW;gBAACO,KAAK,EAAC,SAAS;gBAACH,OAAO,EAAE/B,4BAA6B;gBAACsB,KAAK,EAAE;kBAAEW,QAAQ,EAAE,QAAQ;kBAAET,UAAU,EAAE;gBAAM,CAAE;gBAAAV,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACPrM,OAAA,CAACrB,MAAM;QAACiQ,IAAI,EAAE1M,kBAAmB;QAAC4M,OAAO,EAAE1D,yBAA0B;QAAAY,QAAA,gBACjEhM,OAAA,CAACpB,WAAW;UAAAoN,QAAA,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxDrM,OAAA,CAACnB,aAAa;UAAAmN,QAAA,eACVhM,OAAA,CAAChC,IAAI;YAACsO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAP,QAAA,gBACvBhM,OAAA,CAAChC,IAAI;cAACsI,IAAI;cAACqG,EAAE,EAAE,EAAG;cAACoC,EAAE,EAAE,CAAE;cAAA/C,QAAA,gBACrBhM,OAAA,CAACjC,UAAU;gBAAC8O,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAd,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChErM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,YAAY;gBAClBX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACE,UAAW;gBAC3BkG,IAAI,EAAC,YAAY;gBACjBmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,WAAW;gBACjBX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACG,SAAU;gBAC1BiG,IAAI,EAAC,WAAW;gBAChBmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,OAAO;gBACbX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACI,KAAM;gBACtBgG,IAAI,EAAC,OAAO;gBACZmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,OAAO;gBACbX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACK,KAAM;gBACtB+F,IAAI,EAAC,OAAO;gBACZmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEA,CAAC,eACPrM,OAAA,CAAChC,IAAI;cAACsI,IAAI;cAACqG,EAAE,EAAE,EAAG;cAACoC,EAAE,EAAE,CAAE;cAAA/C,QAAA,gBACrBhM,OAAA,CAACjC,UAAU;gBAAC8O,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAd,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnErM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,YAAY;gBAClBX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACiO,mBAAoB;gBACpC7H,IAAI,EAAC,qBAAqB;gBAC1BmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,WAAW;gBACjBX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACkO,kBAAmB;gBACnC9H,IAAI,EAAC,oBAAoB;gBACzBmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACM,QAAS;gBACzB8F,IAAI,EAAC,UAAU;gBACfmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,OAAO;gBACbX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACmO,cAAe;gBAC/B/H,IAAI,EAAC,gBAAgB;gBACrBmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,MAAM;gBACZX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACO,IAAK;gBACrB6F,IAAI,EAAC,MAAM;gBACXmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,UAAU;gBAChBX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACQ,QAAS;gBACzB4F,IAAI,EAAC,UAAU;gBACfmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACS,OAAQ;gBACxB2F,IAAI,EAAC,SAAS;gBACdmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrM,OAAA,CAAC/B,SAAS;gBACNuP,KAAK,EAAC,KAAK;gBACXX,OAAO,EAAC,UAAU;gBAClBY,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZtG,KAAK,EAAErG,QAAQ,CAACU,GAAI;gBACpB0F,IAAI,EAAC,KAAK;gBACVmH,QAAQ,EAAErH,YAAa;gBACvBuF,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAO;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAChBrM,OAAA,CAAClB,aAAa;UAAAkN,QAAA,gBACVhM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAE3D,yBAA0B;YAAC8D,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzErM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAE7B,yBAA0B;YAACgC,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAETrM,OAAA,CAACrB,MAAM;QAACiQ,IAAI,EAAE5M,UAAW;QAAC8M,OAAO,EAAE3D,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACV,SAAS;QAAAzB,QAAA,gBACzEhM,OAAA,CAACpB,WAAW;UAACiP,EAAE,EAAE;YAAEV,QAAQ,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpErM,OAAA,CAACnB,aAAa;UAAAmN,QAAA,eACVhM,OAAA,CAACzB,cAAc;YAACgP,SAAS,EAAE7O,KAAM;YAAAsN,QAAA,eAC7BhM,OAAA,CAAC5B,KAAK;cAAA4N,QAAA,gBACFhM,OAAA,CAACxB,SAAS;gBAACqP,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,eACrChM,OAAA,CAACvB,QAAQ;kBAAAuN,QAAA,gBACLhM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/BrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACtCrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZrM,OAAA,CAAC3B,SAAS;gBAAA2N,QAAA,EACLvL,eAAe,CAAC4E,GAAG,CAAC,CAAC8J,YAAY,EAAEC,UAAU,kBAC1CpP,OAAA,CAACvB,QAAQ;kBAAAuN,QAAA,gBACLhM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,eACNhM,OAAA;sBAAKiO,GAAG,EAAEkB,YAAY,CAACzJ,KAAK,IAAI,EAAG;sBAACwI,GAAG,EAAEiB,YAAY,CAAC3J,KAAM;sBAACgH,KAAK,EAAE;wBAAE2B,QAAQ,EAAE,MAAM;wBAAEkB,MAAM,EAAE,MAAM;wBAAEC,SAAS,EAAE;sBAAQ;oBAAE;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC,eACZrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,gBACNhM,OAAA,CAACjC,UAAU;sBAAC8O,OAAO,EAAC,WAAW;sBAAC0C,UAAU,EAAC,MAAM;sBAAAvD,QAAA,EAAEmD,YAAY,CAAC3J;oBAAK;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACnFrM,OAAA,CAACjC,UAAU;sBAAC8O,OAAO,EAAC,OAAO;sBAACO,KAAK,EAAC,eAAe;sBAAApB,QAAA,EAAEmD,YAAY,CAAChH;oBAAS;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACZrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,eACNhM,OAAA,CAACX,GAAG;sBAACwO,EAAE,EAAE;wBAAEd,OAAO,EAAE,MAAM;wBAAEyC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAApC,QAAA,EACzDmD,YAAY,CAACpG,QAAQ,CAAC1D,GAAG,CAAEwH,OAAO,iBAC/B7M,OAAA,CAACX,GAAG;wBAAkBwO,EAAE,EAAE;0BAAEd,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEoB,GAAG,EAAE;wBAAE,CAAE;wBAAApC,QAAA,gBACxEhM,OAAA,CAACZ,IAAI;0BACDoO,KAAK,EAAE,GAAGX,OAAO,CAACtE,cAAc,IAAI,SAAS,IAAIsE,OAAO,CAACrE,cAAc,GAAG,IAAI,GAAGqE,OAAO,CAACrE,cAAc,GAAG,EAAE,EAAG;0BAC/GkF,IAAI,EAAC,OAAO;0BACZb,OAAO,EAAC;wBAAU;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACFrM,OAAA,CAACjC,UAAU;0BAAC8O,OAAO,EAAC,SAAS;0BAAAb,QAAA,GAAC,OAAK,EAACa,OAAO,CAACvE,GAAG;wBAAA;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC;sBAAA,GANvDQ,OAAO,CAACtH,EAAE;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOf,CACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,eACNhM,OAAA,CAACX,GAAG;sBAACwO,EAAE,EAAE;wBAAEd,OAAO,EAAE,MAAM;wBAAEyC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAApC,QAAA,EACzDmD,YAAY,CAACpG,QAAQ,CAAC1D,GAAG,CAAEwH,OAAO,iBAC/B7M,OAAA,CAACjC,UAAU;wBAAkB8O,OAAO,EAAC,OAAO;wBAAAb,QAAA,GACvCa,OAAO,CAAChH,QAAQ,IAAI,CAAC,EAAC,QAC3B;sBAAA,GAFiBgH,OAAO,CAACtH,EAAE;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEf,CACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,eACNhM,OAAA,CAACX,GAAG;sBAACwO,EAAE,EAAE;wBAAEd,OAAO,EAAE,MAAM;wBAAEyC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAApC,QAAA,EACzDmD,YAAY,CAACpG,QAAQ,CAAC1D,GAAG,CAAEwH,OAAO,iBAC/B7M,OAAA,CAACjC,UAAU;wBAAkB8O,OAAO,EAAC,OAAO;wBAAAb,QAAA,GAAC,GACxC,EAACa,OAAO,CAAClH,KAAK,IAAI,CAAC;sBAAA,GADPkH,OAAO,CAACtH,EAAE;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEf,CACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZrM,OAAA,CAAC1B,SAAS;oBAAA0N,QAAA,eACNhM,OAAA,CAACX,GAAG;sBAACwO,EAAE,EAAE;wBAAEd,OAAO,EAAE,MAAM;wBAAEyC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAApC,QAAA,EACzDmD,YAAY,CAACpG,QAAQ,CAAC1D,GAAG,CAAEwH,OAAO,iBAC/B7M,OAAA;wBAEIqO,IAAI,EAAC,UAAU;wBACfoB,OAAO,EAAE9O,gBAAgB,CAAC+G,IAAI,CAACpB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAKsH,OAAO,CAACtH,EAAE,CAAE;wBAC/D+I,QAAQ,EAAEA,CAAA,KAAMhH,oBAAoB,CAACuF,OAAO;sBAAE,GAHzCA,OAAO,CAACtH,EAAE;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAIlB,CACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAnDD,SAAS+C,UAAU,EAAE;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoD1B,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAChBrM,OAAA,CAAClB,aAAa;UAAAkN,QAAA,gBACVhM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAE7D,0BAA2B;YAACgE,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ErM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAE9B,iBAAkB;YAACiC,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACTrM,OAAA,CAACrB,MAAM;QAACiQ,IAAI,EAAEhM,kBAAmB;QAACkM,OAAO,EAAE/C,yBAA0B;QAAAC,QAAA,gBACjEhM,OAAA,CAACpB,WAAW;UAAAoN,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvCrM,OAAA,CAACnB,aAAa;UAAAmN,QAAA,gBACVhM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,kBAAkB;YACxBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAEtE,cAAe;YACtBwL,QAAQ,EAAGpH,CAAC,IAAKnE,iBAAiB,CAACmE,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,2BAA2B;YACjCX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAEpE,sBAAuB;YAC9BsL,QAAQ,EAAGpH,CAAC,IAAKjE,yBAAyB,CAACiE,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,cAAc;YACpBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAElE,WAAY;YACnBoL,QAAQ,EAAGpH,CAAC,IAAK/D,cAAc,CAAC+D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,SAAS;YACfX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAEhE,MAAO;YACdkL,QAAQ,EAAGpH,CAAC,IAAK7D,SAAS,CAAC6D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,UAAU;YAChBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAE9D,OAAQ;YACfgL,QAAQ,EAAGpH,CAAC,IAAK3D,UAAU,CAAC2D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,UAAU;YAChBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAE5D,QAAS;YAChB8K,QAAQ,EAAGpH,CAAC,IAAKzD,WAAW,CAACyD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,eAAe;YACrBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAE1D,YAAa;YACpB4K,QAAQ,EAAGpH,CAAC,IAAKvD,eAAe,CAACuD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,SAAS;YACfX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAExD,OAAQ;YACf0K,QAAQ,EAAGpH,CAAC,IAAKrD,UAAU,CAACqD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,UAAU;YAChBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAEtD,OAAQ;YACfwK,QAAQ,EAAGpH,CAAC,IAAKnD,UAAU,CAACmD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFrM,OAAA,CAAC/B,SAAS;YACNuP,KAAK,EAAC,WAAW;YACjBX,OAAO,EAAC,UAAU;YAClBY,SAAS;YACTC,IAAI,EAAC,OAAO;YACZtG,KAAK,EAAEpD,QAAS;YAChBsK,QAAQ,EAAGpH,CAAC,IAAKjD,WAAW,CAACiD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAChBrM,OAAA,CAAClB,aAAa;UAAAkN,QAAA,gBACVhM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAEhE,oBAAqB;YAACmE,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtErM,OAAA,CAAC9B,MAAM;YAAC+O,OAAO,EAAElB,yBAA0B;YAACqB,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eACT,CAAC;AAEX,CAAC;AAACjM,EAAA,CA37BWD,WAAW;EAAA,QACHL,WAAW,EACRD,SAAS;AAAA;AAAA6P,EAAA,GAFpBvP,WAAW;AAAA,IAAAuP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}