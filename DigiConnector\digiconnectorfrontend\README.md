# DigiHub

DigiHub is a powerful Direct-to-Customer (D2C) product management application that empowers businesses to effortlessly expand their reach by listing products on various marketplaces and ecommerce platforms simultaneously. With our automated system, you can bulk list products across channels with ease, saving your time and effort.

In addition to product and order management, DigiHub offers a range of features including channel creation, job creation, and more.
https://docs.google.com/presentation/d/1_V_W7yqCqQyIfKzCls2Z_5Wih8icmWItOS-eIiXLmos/edit#slide=id.g2b0df1fcf84_0_245

## Table of Contents

- [Features](#features)
- [Technologies Used](#technologies-used)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Running the Application](#running-the-application)
- [Usage](#usage)
- [Contributing](#contributing)
- [License](#license)
- [Acknowledgements](#acknowledgements)

## Features

- User authentication for secure login.
- Product listing and management.
- Bulk upload functionality for adding products.
- Downloading product data.
- Product analytics dashboard.
- Order management:
  - Add tracking information.
  - Scan barcode for easy order processing.
- Channel management:
  - Create and manage sales channels.
- Job creation:
  - Create, schedule, and manage automated tasks and processes.

## Technologies Used

- React.js

## Getting Started

### Prerequisites

- Node.js and npm should be installed on your system.

### Installation

1. Clone the repository: `git clone https://<EMAIL>/DigiSpin-Technologies/Yocabe/_git/DigiConnector`
2. Install dependencies: `npm install`

### Running the Application

1. Start the Node.js server: `npm start`
2. Navigate to [http://localhost:3000](http://localhost:3000) in your browser to view the application.

## Usage

- After running the application, users can sign up or log in to their accounts.
- Users can add, delete, update, and manage their digital products.
- Bulk upload feature allows users to upload multiple products at once.
- Download product data for analysis or backup purposes.
- Manage orders:
  - Add tracking information for shipments.
  - Scan barcode for efficient order processing.
- Manage channels:
  - Create and configure sales channels.
- Create and manage jobs:
  - Schedule automated tasks and processes.

## Contributing

- Contributions are welcome! Feel free to submit bug reports, feature requests, or pull requests.

## License

## Acknowledgements

- Thanks to the creators of DigiSpin for inspiration.
- Special thanks to the React.js and Node.js communities for their amazing libraries and resources.
