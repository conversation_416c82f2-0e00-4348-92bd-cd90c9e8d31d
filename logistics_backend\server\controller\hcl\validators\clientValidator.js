
const Joi = require('joi');

const clientRegistrationSchema = Joi.object({
    firstName: Joi.string().max(100).required(),
    lastName: Joi.string().max(100).required(),
    email: Joi.string().email().required(),
    mobile: Joi.alternatives().try(
      Joi.string().pattern(/^[1-9]\d{9}$/), // Validate as a string
      Joi.number().min(1000000000).max(9999999999)// Validate as a number
    ).required(),
    company: Joi.string().allow(null, '').max(255),
    monthlyOrders: Joi.string().max(100).required(),
    businessType: Joi.string().max(100).required(),
});

const validateClientRegistration = (data) => {
    return clientRegistrationSchema.validate(data);
};

const createBillingInfoValidation = (data) => {
    const schema = Joi.object({
      id: Joi.number().optional(),
      clientId: Joi.number().required(),
      billingDate: Joi.date().required(),
      codGap: Joi.number().optional(),
      codDays: Joi.string().max(255).optional(),
    });
    return schema.validate(data);
  };

module.exports = { validateClientRegistration,  createBillingInfoValidation };
