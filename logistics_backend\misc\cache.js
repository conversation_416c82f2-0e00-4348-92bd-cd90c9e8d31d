const NodeCache = require("node-cache");

class Cache {
    constructor() {
        // Create a new NodeCache instance with a default TTL (in seconds)
        this.cache = new NodeCache({
            stdTTL: 24 * 60 * 60, // 24 hours in seconds
            checkperiod: 600 // Check for expired keys every 10 minutes
        });
    }

    /**
     * 
     * @param {*} prefix 
     * @param {*} identifier 
     * @returns 
     */
    generateKey(prefix, identifier) {
        return `${prefix}:${identifier}`;
    }

    /**
     * Get a value from the cache by key
     * @param {string} key - Cache key
     * @returns {any|null} - Returns the cached value or null if not found
     */
    get(key) {
        if (!key || typeof key !== 'string') {
            console.error("Invalid key provided for cache get:", key);
            return null;
        }
        const data = this.cache.get(key);
        return data ? JSON.parse(data) : null;
    }

    /**
     * Set a value in the cache
     * @param {string} key - Cache key
     * @param {any} value - Value to cache
     * @param {number} [ttl] - Time to live in seconds (optional)
     */
    set(key, value, ttl = 3600) {
        if (!key || typeof key !== 'string') {
            console.error("Invalid key provided for cache set:", key);
            return;
        }
        this.cache.set(key, JSON.stringify(value), ttl);  
    }

    /**
     * Set multiple values in the cache
     * @param {object} cacheData - An object where keys are cache keys and values are the values to cache
     */
    mset(cacheData) {
        if (cacheData && typeof cacheData === 'object' && !Array.isArray(cacheData)) {
            const formattedData = Object.entries(cacheData).map(([key, value]) => ({ key, val: value }));
            this.cache.mset(formattedData);
        } else {
            console.error('Invalid cacheData for mset. Expected an object:', cacheData);
        }
    }

    /**
     * Get all keys from the cache
     * @returns {Array<string>} - Returns an array of all existing keys
     */
    async keys() {
        return this.cache.keys();
    }

    /**
     * Delete a value from the cache
     * @param {string} key - Cache key
     */
    delete(key) {
        if (!key || typeof key !== 'string') {
            console.error("Invalid key provided for cache delete:", key);
            return;
        }
        this.cache.del(key);
    }

    /**
     * Clear all entries from the cache
     */
    clearAll() {
        this.cache.flushAll();
    }

    /**
     * Get cache statistics
     * @returns {object} - Returns cache statistics such as hits, misses, keys, and more
     */
    getCacheStats() {
        return this.cache.getStats();
    }
}

module.exports = new Cache();
