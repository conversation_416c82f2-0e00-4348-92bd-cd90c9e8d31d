// Base configuration for different courier partners
const COURIER_CONFIGS = {
    DTDC: {
      eventMap: {
        'SPL': 'OrderAssigned',
        'PCSC': 'Pickup_Scheduled',
        'PCAW': 'Pickup_Awaited',
        'PCRA': 'Pickup_Reassigned',
        'PCUP': 'Picked_Up',
        'BKD': 'Booked',
        'OBMN': 'Departed',
        'CDIN': 'Arrived',
        'CDOUT': 'Departed',
        'INSCAN': 'Arrived',
        'OUTDLV': 'Departed',
        'DLV': 'Delivered',
        'Picked Up': 'Departed',
        'In Transit': 'Departed',
        'Booked': 'Arrived',
        'Pickup Reassigned': 'Arrived'
      },
      hubTypeMap: {
        'SPL': 'StartingHub',
        'PCSC': 'StartingHub',
        'PCAW': 'StartingHub',
        'PCRA': 'StartingHub',
        'PCUP': 'StartingHub',
        'BKD': 'StartingHub',
        'CDIN': 'IntermediateHub',
        'CDOUT': 'IntermediateHub',
        'OBMN': 'IntermediateHub',
        'INSCAN': 'DestinationHub',
        'OUTDLV': 'DestinationHub',
        'DLV': 'DestinationHub',
        'In Transit': 'IntermediateHub',
      }
    },
    BLUEDART: {
    eventMap: {
        'PICKUP': 'Arrived',
        'INTRANSIT': 'Departed',
        'DELIVERED': 'Delivered',
        'EXCEPTION': 'Exception',
        'RETURN': 'Returned'
    },
    hubTypeMap: {
        'PICKUP': 'StartingHub',
        'INTRANSIT': 'IntermediateHub',
        'DELIVERED': 'FinalHub',
        'RETURN': 'FinalHub'
      }
    },
    DELHIVERY: {
        eventMap: {
        'Pickup Created': 'Arrived',
        'Picked Up': 'Departed',
        'In Transit': 'Departed',
        'Pending': 'Arrived',
        'Delivered': 'Delivered',
        'RTO': 'Returned'
      },
      hubTypeMap: {
        'Pickup Created': 'StartingHub',
        'Picked Up': 'StartingHub',
        'In Transit': 'IntermediateHub',
        'Delivered': 'FinalHub',
        'RTO': 'FinalHub'
      }
    }
};
  

// Utility functions
const formatDateTime = (date, time = null, format = 'DDMMYYYY') => {
    
    if (format === 'DDMMYYYY' && time === null) {
        const day = date.substring(0, 2);
        const month = date.substring(2, 4);
        const year = date.substring(4);
        return `${year}-${month}-${day}`;
    } else if (format === 'DDMMYYYY') {
        const day = date.substring(0, 2);
        const month = date.substring(2, 4);
        const year = date.substring(4);
        const hours = time.substring(0, 2);
        const minutes = time.substring(2);
        return `${year}-${month}-${day} ${hours}:${minutes}:00`;
    } 
    // Add more date format handlers as needed
    return date;
};
  
const generateStatusMessage = (courierType, eventType, action, origin, destination) => {
    const messages = {
      default: {
        'OrderAssigned': `Order soft data has been uploaded at ${origin}.`,
        'Pickup_Scheduled': `Order has been scheduled for pickup at ${origin}.`,
        'Pickup_Reassigned': `Order has been rescheduled for pickup at ${origin}.`,
        'Picked_Up': `Order has been picked up at ${origin}.`,
        'Booked': `Order has been booked at ${origin}.`,
        'Arrived': `Order has arrived at ${origin}.`,
        'Departed': `Order has departed from ${origin}${destination ? ' to ' + destination : ''}.`,
        'Delivered': 'Order has been successfully delivered.',
        'Returned': 'Order is being returned.',
        'Exception': 'Exception occurred in delivery.'
      }
    };
  
    return messages[courierType]?.[eventType] || messages.default[eventType] || `Order status: ${action}`;
};
// Transform functions for each courier
const transformers = {
    // DTDC Transformer
    DTDC: (data) => {
      const config = COURIER_CONFIGS.DTDC;
      const eventType = config.eventMap[data.strCode] || config.eventMap[data.strAction] || 'Arrived';
  
      return {
        eventType,
        status: data.strAction,
        warehouseId: parseInt(data.strOriginCode?.replace(/\D/g, '') || '5'),
        hubName: data.strOrigin,
        timestamp: formatDateTime(data.strActionDate, data.strActionTime),
        statusMessage: generateStatusMessage('DTDC', eventType, data.strAction, data.strOrigin, data.strDestination),
        location: data.strOrigin.split(' - ')[0],
        mode: data.strDestination ? 'Surface' : 'Other',
        hubsTrackingType: config.hubTypeMap[data.strCode] || config.hubTypeMap[data.strAction] || 'IntermediateHub'
      };
    },
  
    // BlueDart Transformer
    BLUEDART: (data) => {
      const config = COURIER_CONFIGS.BLUEDART;
      return {
        eventType: config.eventMap[data.status] || 'Arrived',
        warehouseId: parseInt(data.facilityId || '5'),
        hubName: data.facilityName,
        timestamp: data.scanDateTime,
        statusMessage: generateStatusMessage('BLUEDART', config.statusMap[data.status], data.status, data.facilityName, data.destination),
        location: data.city || data.facilityName,
        mode: data.serviceType || 'Surface',
        hubsTrackingType: config.hubTypeMap[data.status] || 'IntermediateHub',
        ...(data.agentDetails && {
          deliveryAgent: {
            agentName: data.agentDetails.name,
            agentContact: data.agentDetails.contact,
            agentId: data.agentDetails.id
          }
        })
      };
    },
  
    // Delhivery Transformer
    DELHIVERY: (data) => {
      const config = COURIER_CONFIGS.DELHIVERY;
      return {
        eventType: config.eventMap[data.status] || 'Arrived',
        warehouseId: parseInt(data.facilityCode || '5'),
        hubName: data.facilityName,
        timestamp: data.scanTime,
        statusMessage: generateStatusMessage('DELHIVERY', config.statusMap[data.status], data.status, data.facilityName, data.destination),
        location: data.location || data.facilityName,
        mode: data.transportMode || 'Surface',
        hubsTrackingType: config.hubTypeMap[data.status] || 'IntermediateHub',
        ...(data.deliveryAgent && {
          deliveryAgent: {
            agentName: data.deliveryAgent.name,
            agentContact: data.deliveryAgent.phone,
            agentId: data.deliveryAgent.id
          }
        })
      };
    }
  };
  

// Main transformation function
function transformCourierData(courierType, data) {
    const transformer = transformers[courierType.toUpperCase()];
    if (!transformer) {
      throw new Error(`Unsupported courier type: ${courierType}`);
    }
    
    if (Array.isArray(data)) {
      return data.map(entry => transformer(entry));
    }
    return transformer(data);
}
  

// Export the transformer

// Create the CourierDataTransformer object
const CourierDataTransformer = {
    transform: transformCourierData,
    
    addCourierConfig: function(courierType, config) {
      if (typeof courierType !== 'string') {
        throw new Error('Courier type must be a string');
      }
      COURIER_CONFIGS[courierType.toUpperCase()] = config;
    },
    
    addTransformer: function(courierType, transformer) {
      if (typeof courierType !== 'string') {
        throw new Error('Courier type must be a string');
      }
      if (typeof transformer !== 'function') {
        throw new Error('Transformer must be a function');
      }
      transformers[courierType.toUpperCase()] = transformer;
    }
  };
  
  // CommonJS export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CourierDataTransformer, formatDateTime };
}