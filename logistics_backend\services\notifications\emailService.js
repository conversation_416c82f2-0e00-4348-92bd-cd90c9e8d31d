const nodemailer = require('nodemailer');
const mailConfig = require('../../misc/mailConfig');
const emailTemplates = require('../../templates/emailTemplates');

const transporter = nodemailer.createTransport(mailConfig);

class EmailService {
    async sendEmail(to, templateName, data) {
        const template = emailTemplates[templateName](...data);

        const mailOptions = {
            from: mailConfig.auth.user,
            to,
            subject: template.subject,
            text: template.text,
            html: template.html,
        };

        try {
            await transporter.sendMail(mailOptions);
            console.log(`Email sent to ${to}`);
        } catch (error) {
            console.error('Error sending email:', error);
            throw new Error('Failed to send email');
        }
    }
}

module.exports = new EmailService();