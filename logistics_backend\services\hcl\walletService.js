const dbpool = require('../../startup/db');
const helpers = require('../../misc/helpers.js');
const miscConstants = require("../../misc/constants.js");
const logger = require ("../../misc/logger.js")

const bcrypt = require('bcrypt');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class WalletService {

    /**
     * 
     * @param {*} clientId 
     * @param {*} amount 
     * @param {*} transactionType 
     * @param {*} transactionReference 
     * @param {*} operation 
     * @param {*} createdBy 
     * @returns 
     */

    static async rechargeWallet (clientId, amount, transactionType, transactionReference, operation, createdBy) {
        try {
            const query = `${hcldb}.RechargeWallet`;
            const [rows] = await dbpool.executeProcedure(query, [clientId, amount, transactionType, 
                                                                transactionReference, operation, createdBy]);
            return rows[0];
        } catch (error) {
            console.error('Error in walletService rechargeWallet:', error.message);
            throw error;
        }    
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} amount 
     * @param {*} transactionType 
     * @param {*} awbNumber 
     * @param {*} transactionReference 
     * @param {*} operation 
     * @param {*} createdBy 
     * @returns 
     */
    static async deductFromWallet (clientId, 
        amount, transactionType, awbNumber, 
        transactionReference, operation, createdBy) {
        try {
            const query = `${hcldb}.DeductFromWallet`;
            const [rows] = await dbpool.executeProcedure(query, [clientId,  amount, transactionType, awbNumber, 
                                                                transactionReference, operation,  createdBy]);
            return rows[0];
        } catch (error) {
            console.error('Error in walletService deductFromWallet:', error.message);
            throw error;
        }        
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} amount 
     * @param {*} transactionType 
     * @param {*} awbNumber 
     * @param {*} transactionReference 
     * @param {*} operation 
     * @param {*} createdBy 
     * @returns 
     */

    static async refundToWallet (clientId, amount, transactionType, awbNumber, 
        transactionReference, operation, createdBy) {
        try{
            const query = `${hcldb}.RefundToWallet`;
            const [rows] = await dbpool.executeProcedure(query, [clientId, amount, transactionType, awbNumber, 
                                                                transactionReference, operation, createdBy]);
            return rows[0];
        } catch (error) {
            console.error('Error in walletService refundToWallet:', error.message);
            throw error;
        }      
    }

    /**
     * 
     * @param {*} filters 
     * @returns 
     */

    static async getWalletTransactions (filters) {
        try{
            const query = `${hcldb}.GetWalletTransactions`;
            const {clientId, awb, fromDate, toDate, pageNum, pageSize} = filters;
            const [transactions, total_count] = await dbpool.executeProcedure(query, [clientId,
                                                                    awb || null, 
                                                                    fromDate || null, 
                                                                    toDate || null, 
                                                                    parseInt(pageNum) || 1, 
                                                                    parseInt(pageSize) || 10]);
            return {
                transactions,
                totalRecords:total_count[0].total_count,
                pageNum,
                pageSize
            }
        } catch (error) {
            console.error('Error in walletService getWalletTransactions:', error.message);
            throw error;
        }        
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} page 
     * @param {*} limit 
     * @returns : wallet balance
     */
    static async getWalletBalancebyClientId (clientId, page, limit) {
        try{
            const query = `${hcldb}.GetWalletBalancebyClientId`;
            const [balance] = await dbpool.executeProcedure(query, [clientId]);
            return balance[0];
        } catch (error) {
            // if(SQLSTATE == '45000') {
                // return 0;
            // } else {
            //     console.error('Error in walletService.getWalletBalancebyClientId:', error.message);
            throw error;
            // }    
        }      
    }

    /**
     * 
     * @param {*} page 
     * @param {*} limit 
     * @returns 
     */

    static async getWalletBalances (organizationId, page, limit) {
        try{
            const query = `${hcldb}.GetWalletBalances`;
            const [balances, totalCount] = await dbpool.executeProcedure(query, [organizationId, page, limit]);
            return {
                balances,
                totalRecords:totalCount[0].totalRecords,
            }
        } catch (error) {
            console.error('Error in walletService getWalletBalances:', error.message);
            throw error;
        }      
    }



      /**
     * 
     * @param {} orderId 
     * @param {*} clientId 
     * @param {*} amount 
     * @param {*} awb 
     * @param {*} userId 
     * @returns 
     */
      static async chargeForShipment(orderId, clientId, amount, awb, userId) {
        try {
            const transactionReference = `${miscConstants.WALLET.TRANSACTION_REF.ORDER_CHARGE}${amount}`;
            const walletBalance = await this.getWalletBalancebyClientId(clientId);
    
            if (parseFloat(walletBalance.balance) >= parseFloat(amount)) {
                console.log(`Charging client for shipment. OrderId: ${orderId}, Amount: ${amount}`, 
                    { clientId, orderId, awb, amount });
                logger.logInfo(`Charging client for shipment. OrderId: ${orderId}, Amount: ${amount}`, 
                               { clientId, orderId, awb, amount });
    
                await this.deductFromWallet(
                    clientId, 
                    amount, 
                    miscConstants.WALLET.TRANSACTION_TYPE.ORDER_TRXN, 
                    awb, 
                    transactionReference, 
                    miscConstants.WALLET.OPERATION.ORDER_CHARGE, 
                    userId
                );
    
                return { success: true, message: "Wallet charged successfully" };
            } else {
                logger.logInfo(`Insufficient wallet balance for OrderId: ${orderId}, required: ${amount}, available: ${walletBalance.balance}`);
                throw new Error(`Insufficient wallet balance. Please recharge the wallet.`);
            }
        } catch (error) {
            logger.logError(`Error charging wallet for OrderId: ${orderId}`, error);
            throw error;
        }
    }


}    

module.exports = WalletService;
