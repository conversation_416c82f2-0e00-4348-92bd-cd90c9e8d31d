-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `map_channel_payment_method`
--

DROP TABLE IF EXISTS `map_channel_payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `map_channel_payment_method` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `ch_payment_method` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `fby_payment_method` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `createdOn` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `map_channel_payment_method`
--

LOCK TABLES `map_channel_payment_method` WRITE;
/*!40000 ALTER TABLE `map_channel_payment_method` DISABLE KEYS */;
INSERT INTO `map_channel_payment_method` VALUES (1,'shopify','','NaN','2022-11-29 11:42:13'),(2,'shopify','Cash on Delivery (COD)','COD','2022-11-29 11:42:13'),(3,'shopify','Contrassegno (SOLO ITALIA)','COD','2022-11-29 11:42:13'),(4,'shopify','credit_card_mollie_','CC','2022-11-29 11:42:13'),(5,'shopify','manual','NaN','2022-11-29 11:42:13'),(7,'shopify','paypal','PP','2022-11-29 11:42:13'),(8,'Shopify IT','','NaN','2022-11-29 11:42:13'),(9,'Shopify IT','Cash on Delivery (COD)','COD','2022-11-29 11:42:13'),(10,'Shopify IT','Contrassegno (SOLO ITALIA)','COD','2022-11-29 11:42:13'),(11,'Shopify IT','credit_card_mollie_','CC','2022-11-29 11:42:13'),(12,'Shopify IT','manual','NaN','2022-11-29 11:42:13'),(13,'Shopify IT','paypal','PP','2022-11-29 11:42:13'),(14,'woocommerce','cod','COD','2022-11-29 11:42:13'),(15,'Woocommerce IT','cod','COD','2022-11-29 11:42:13'),(16,'shopify','credit_card','CC','2022-11-29 11:42:13'),(17,'Shopify IT','credit_card','CC','2022-11-29 11:42:13'),(18,'woocommerce','credit_card','CC','2022-11-29 11:42:13'),(19,'Woocommerce IT','credit_card','CC','2022-11-29 11:42:13'),(20,'Woocommerce IT','woocommerce_payments','CC','2022-11-29 11:42:13'),(21,'woocommerce','woocommerce_payments','CC','2022-11-29 11:42:13'),(22,'woocommerce','amazon_payments_advanced','AMP','2022-11-29 11:42:13'),(23,'Woocommerce IT','amazon_payments_advanced','AMP','2022-11-29 11:42:13'),(24,'Shopify IT','AmazonPay','AMP','2022-11-29 11:42:13'),(25,'shopify','AmazonPay','AMP','2022-11-29 11:42:13'),(26,'Storeden IT','Custom','NaN','2022-11-29 11:42:13'),(27,'Woocommerce IT','other','NaN','2022-11-29 11:42:13'),(28,'Woocommerce IT','','NaN','2022-11-29 11:42:13'),(29,'prestashop','Bonifico bancario','BN','2022-11-29 11:42:13'),(30,'prestashop','PrestaShop Checkout','CC','2022-11-29 11:42:13'),(31,'prestashop','Assegno','CHK','2022-11-29 11:42:13'),(32,'ebay','CustomCode','NaN','2022-11-29 11:42:13'),(33,'Shopify IT','Bank Deposit','BN','2022-11-29 11:42:13'),(34,'Woocommerce IT','bacs','BN','2022-11-29 11:42:13'),(35,'shopify','shopify_payments','CC','2022-11-29 11:42:13'),(37,'Shopify IT','shopify_payments','CC','2022-11-29 11:42:13'),(38,'Shopify IT','Splitit Payments V2','CC','2022-11-29 11:42:13'),(39,'Shopify IT','_apple_pay','CC','2022-11-29 11:42:13'),(40,'Woocommerce IT','ppcp-gateway','PP','2022-11-29 11:42:13'),(41,'woocommerce','ppcp-gateway','PP','2022-11-29 11:42:13'),(42,'Woocommerce IT','ppec_paypal','PP','2022-11-29 11:42:13'),(43,'woocommerce','ppec_paypal','PP','2022-11-29 11:42:13'),(44,'Woocommerce IT','PayPal','PP','2022-11-29 11:42:13'),(45,'woocommerce','PayPal','PP','2022-11-29 11:42:13'),(46,'Woocommerce IT','stripe','CC','2022-11-29 11:42:13'),(47,'woocommerce','stripe','CC','2022-11-29 11:42:13'),(48,'Shopify IT','Scalapay','SP','2022-11-29 11:42:13'),(49,'Shopify IT','COD','COD','2022-11-29 11:42:13'),(50,'Shopify IT','credit card','CC','2022-11-29 11:42:13'),(53,'Shopify IT','Amazon.it','AMZ','2022-11-29 11:42:13'),(54,'shopify','Klarna','KLR','2022-11-29 11:42:13'),(55,'Shopify IT','Klarna','KLR','2022-11-29 11:42:13'),(57,'Mirakl IT','AdyenComponent','CC ','2022-11-29 12:36:37'),(58,'Shopify IT','Bonifico Bancario','BN','2022-11-29 12:36:37'),(59,'Shopify IT','Amazon.de','AMZ','2022-12-01 13:05:27'),(61,'Shopify IT','Carta di Credito','CC','2023-04-12 13:05:38'),(62,'Shopify IT','Amazon.fr','AMZ','2023-04-12 13:05:38'),(63,'Shopify IT','gift_card','CC','2023-04-12 13:05:38'),(64,'prestashop','Card via Stripe','CC','2023-04-12 13:05:38'),(65,'prestashop','PayPal','PP','2023-04-12 13:05:38'),(66,'Woocommerce IT','ppcp','PP','2023-04-12 13:05:38'),(67,'Coin IT','AdyenComponent',NULL,'2023-05-03 14:39:51');
/*!40000 ALTER TABLE `map_channel_payment_method` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:26:44
