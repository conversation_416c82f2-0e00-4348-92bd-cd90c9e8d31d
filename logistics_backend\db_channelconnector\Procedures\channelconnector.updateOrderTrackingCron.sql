DROP PROCEDURE IF EXISTS channelconnector.updateOrderTrackingCron;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateOrderTrackingCron (
	IN `in_ordr_no` VARCHAR(256),
	IN `in_crn_name` VARCHAR(60), 
	IN `in_crnid` VARCHAR(100), 
	IN `time` DATETIME
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = time
	WHERE
		order_no = in_ordr_no AND is_trackable = 1;
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
