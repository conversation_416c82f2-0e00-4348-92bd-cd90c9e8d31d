const dbpool = require('../../startup/db');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants.js");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers');

// Insert/Update Weight Slab
class WeightSlabService {

    /**
     * Upserts (insert/update) a weight slab and refreshes the cache
     * @param {Object} data - Weight slab data
     * @param {number} data.id - Weight slab ID (null for new entries)
     * @param {number} data.clientId - Client ID
     * @param {string} data.mode - Shipping mode
     * @param {number} data.baseWeight - Base weight value
     * @param {number} data.additionalWeight - Additional weight value
     * @param {number} data.isActive - Active status (default: 1)
     * @param {number} userId - User performing the operation
     * @returns {Promise<Object>} Result of the upsert operation
     * @throws {ValidationError} If required fields are missing
     */
    static async upsertWeightSlab (data, userId) {   
        try {
           
            const {
                id = null,
                clientId,
                shippingProviderId,
                mode,
                baseWeight,
                additionalWeight,
                additionalWeightExtra,
                isActive = 1,
            } = data;

            // Execute upsert procedure
            const query = `${hcldb}.UpsertWeightSlab`;
            const [result] =  await dbpool.executeProcedure(query, [
                id,
                clientId,
                shippingProviderId,
                mode,
                baseWeight,
                additionalWeight || null,
                additionalWeightExtra || null,
                isActive,
                userId,
                userId
            ]);

            // Refresh cache after successful upsert
            await this.initializeWeightCache(clientId);
           
            return result[0]; // Return the first row containing success message
        } catch (error) {
            console.error('Error in weightSlabService.upsertWeightSlab:', {
                error: error.message,
                data,
                userId
            });
            throw error;
        }      
    }

    /**
     * Initializes or refreshes the weight slab cache
     * @param {number} clientId - Client ID
     * @param {string|null} mode - Shipping mode (null for all modes)
     * @returns {Promise<void>}
     */
    static async initializeWeightCache(clientId) {
        try {
            if (!clientId) {
                throw new Error('Client ID is required');
            }

            const query = `${hcldb}.GetWeightSlabs`;
            const [weightSlabs] = await dbpool.executeProcedure(query, [clientId, null]);

            // Cache the results
            const cachePrefix = miscConstants.CACHE.WEIGHT.KEY;
            const allKey = `${cachePrefix}_client_${clientId}_all`;
            const cacheTTL = miscConstants.CACHE.WEIGHT.TTL;
            
            cache.set(allKey, weightSlabs, cacheTTL);
 
        } catch (error) {
            console.error('Error in initialize weight Cache:', {
                clientId,
                error: error.message
            });
            throw error;
        }  
    }    

    /**
     * Retrieves weight slabs from cache or database
     * @param {number} clientId - Client ID
     * @param {string|null} mode - Shipping mode (null for all modes)
     * @param {number} refresh - Force cache refresh flag (0: use cache, 1: refresh)
     * @returns {Promise<Array>} Weight slabs
     */
    static async getWeightSlabsByClient(clientId) {
        try {
            if (!clientId) {
                throw new Error('Client ID is required');
            }
            const cacheKey = `${miscConstants.CACHE.WEIGHT.KEY}_client_${clientId}_all`;
            let weightSlabs = await cache.get(cacheKey);
            
            if (!weightSlabs) {
                await this.initializeWeightCache(clientId);
                weightSlabs = await cache.get(cacheKey);
            }

            return weightSlabs;

        } catch (error) {
            console.error('Error in weightSlabService.getWeightSlabsByClient:', {
                clientId,
                error: error.message
            });
            throw error;
        }   
    }

  
    static async getWeightSlabs(clientId, shippingProviderId = null, mode = null) {
        try {
            
            if (!clientId) {
                throw new Error('Client ID is required');
            }
    
            const weightSlabs = await this.getWeightSlabsByClient(clientId);
            if (!Array.isArray(weightSlabs) || weightSlabs.length === 0) {
                throw new Error('No weight slabs found');
            }
    
            const filteredSlabs = weightSlabs.filter(slab => {
                return (
                    (shippingProviderId === null || slab.shippingProviderId === Number(shippingProviderId)) &&
                    slab.isActive === 1 &&
                    (mode === null || slab.mode === mode) // Check mode only if it's provided
                );
            });

            return filteredSlabs;

        } catch (error) {
            console.error('Error in weightSlabService.getWeightSlabs:', {
                clientId,
                shippingProviderId,
                mode,
                error: error.message
            });
            throw error;
        }   
    }

    /**
     * Retrieves appropriate weight slab from cache based on weight criteria
     * @param {number} clientId - Client ID
     * @param {string} mode - Shipping mode
     * @param {number} effectiveWeight - Effective weight to match
     * @returns {Promise<Object>} Matching weight slab
     * @throws {Error} If no suitable slab is found
     */
    static async getWeightSlabFromCache(clientId, shippingProviderId, mode, effectiveWeight) {
       try {
            if (!clientId || !shippingProviderId || !mode || effectiveWeight === undefined) {
                throw new Error('ClientId, shippingProviderId, mode, and effectiveWeight are required');
            }
           
            const filteredSlabs = await this.getWeightSlabs(clientId, shippingProviderId, mode);         
           
            // Step 1: Filter slabs by client_id, mode, and is_active
            // const filteredSlabs = await weightSlabs.filter( async (slab) => 
            //     slab.clientId === clientId &&
            //     slab.mode === mode &&
            //     slab.isActive === 1
            // );

            // Step 2: Sort by base_weight in descending order
            const sortedSlabs = filteredSlabs.sort((a, b) => b.baseWeight - a.baseWeight);
        
            // Step 3: Find the first slab where base_weight <= effective_weight
            const matchingSlab = sortedSlabs.find(slab => slab.baseWeight <= effectiveWeight);
        
            if (!matchingSlab) {
                throw new Error(`No suitable weight slab found for weight: ${effectiveWeight}`);
            }
        
            return matchingSlab; // Return the matched weight slab
        } catch(error) {
            console.error('Error in weightSlabService.getWeightSlabFromCache:', {
                clientId,
                mode,
                effectiveWeight,
                error: error.message
            });
            throw error;
        }
    }    
}

module.exports = WeightSlabService;
