// /shippingPartners/delhivery/delhiveryService.js
const BaseService = require('../baseService');

class DelhiveryService extends BaseService {
  async checkServiceability(pincode) {
    // DTDC-specific API call to check serviceability
  }
  async createShipment(order) {
    // DTDC-specific API call to create a shipment
  }
  async trackShipment(trackingId) {
    // DTDC-specific API call to track a shipment
  }
}

module.exports = DelhiveryService;
