-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `fby_alert_domains`
--

DROP TABLE IF EXISTS `fby_alert_domains`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fby_alert_domains` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fby_alert_domains`
--

LOCK TABLES `fby_alert_domains` WRITE;
/*!40000 ALTER TABLE `fby_alert_domains` DISABLE KEYS */;
INSERT INTO `fby_alert_domains` VALUES (1,'Iupiter','Iupiter','2022-01-28 17:18:18'),(2,'Satellite','Satellite','2022-01-28 17:18:18'),(3,'Neteven','Neteven','2022-01-28 17:18:18'),(4,'Ebay','Ebay','2022-01-28 17:18:18'),(5,'Catalogo','Catalogo','2022-01-28 17:18:18'),(6,'Logistica','Logistica','2022-01-28 17:18:18'),(7,'Customer Service','Customer Service','2022-01-28 17:18:18'),(8,'Ordini','Ordini','2022-01-28 17:18:18'),(9,'Validazioni','Validazioni','2022-01-28 17:18:18'),(10,'Validazioni recuperabili','Validazioni recuperabili','2022-01-28 17:18:18'),(11,'Giacenze','Giacenze','2022-01-28 17:18:18'),(12,'Indirizzi','Indirizzi','2022-01-28 17:18:18'),(13,'Notifiche manuali','Notifiche manuali','2022-01-28 17:18:18'),(14,'Notifiche incomplete','Notifiche incomplete','2022-01-28 17:18:18'),(15,'Configurazioni','Configurazioni','2022-01-28 17:18:18'),(16,'Rifornimenti','Rifornimenti','2022-01-28 17:18:18'),(17,'File spedizioni','File spedizioni','2022-01-28 17:18:18'),(18,'File prezzi','File prezzi','2022-01-28 17:18:18'),(19,'File quantità','File quantità','2022-01-28 17:18:18'),(20,'Magazzini sospesi','Magazzini sospesi','2022-01-28 17:18:18'),(21,'Prodotti','Prodotti','2022-01-28 17:18:18'),(22,'Rimborsi','Rimborsi','2022-01-28 17:18:18'),(23,'Spedizioni','Spedizioni','2022-01-28 17:18:18'),(24,'Sincronizzazione','Sincronizzazione','2022-01-28 17:18:18');
/*!40000 ALTER TABLE `fby_alert_domains` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:26:01
