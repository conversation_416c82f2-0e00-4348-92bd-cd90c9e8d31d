const ShipmentsService = require('../../../services/hcl/shipmentsService.js');
const initializeShipMentSchema = require('./validators/shipmentsValidator.js');
const miscConstants = require("../../../misc/constants.js");
const helpers = require('../../../misc/helpers.js');
const { ServiceType } = require('../../../misc/enums/orderStatusEnum.js');
const { throwError } = require('rxjs');

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.createOrderShipment = async (req, res, next) => {
  try {
    const { orderId } = req.body;
    const user = req.user;

    if (!orderId || isNaN(orderId)) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
        miscConstants.ERRORCODES.BAD_REQUEST,
        "OrderId should be numeric!",
        req.body
      );
    }

    if (!user?.client?.isActive || !user?.client?.isWallet) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
        miscConstants.ERRORCODES.BAD_REQUEST,
        "Client is not allowed to ship. Please contact support.",
        req.body
      );
    }

    const response = await ShipmentsService.createOrderShipment(orderId, user);
    // if (!response.success) {
    //   return helpers.sendError(
    //     res,
    //     miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
    //     miscConstants.ERRORCODES.BAD_REQUEST,
    //     response.message,
    //     response
    //   );
    // }

   return helpers.sendSuccess(
      res,
      miscConstants.HTTPSTATUSCODES.OK,
      miscConstants.SUCESSSMESSAGES.GET,
      response
    );

  } catch (error) {
    helpers.sendError(
      res,
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
      error.message,
      req.body
    );
  }
};
/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getShipmentCost = async (req, res) => {
  try {

    const { error, value } = initializeShipMentSchema.shipmentCostSchema.validate(req.query);

    if (error) {
      return helpers.sendError(
          res, 
          miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
          miscConstants.ERRORCODES.BAD_REQUEST, 
          error.details[0].message,
          req.query
      );
    }

    let {
      sourcePincode,
      destinationPincode,
      weight,
      height,
      width,
      length,
      orderType,
      mode,
      isCod
    } = value;

    const clientId = req.user.clientId;
    if(!orderType) {
      orderType = req.user?.client?.isB2BAccess ? ServiceType.B2B : ServiceType.B2C;
    }  

    const costs = await ShipmentsService.calculateShipmentCost({
      clientId,
      sourcePincode,
      destinationPincode,
      weight: Number(weight),
      height: Number(height),
      width: Number(width),
      length: Number(length),
      mode,
      orderType,
      isCod
    });

   if(costs.isServiceable === true) {
      helpers.sendSuccess(
          res, 
          miscConstants.HTTPSTATUSCODES.OK, 
          miscConstants.SUCESSSMESSAGES.GET, 
          costs.shippingRates
      );
   } else {
    helpers.sendError(
      res, 
      miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
      miscConstants.ERRORCODES.NOT_FOUND, 
      `No rates found for ${sourcePincode} to ${destinationPincode}`,
      req.query
    );
   }  

  } catch (error) {
    helpers.sendError(
      res, 
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message,
      req.query
    );
  }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */

exports.getMultiPackageShipmentCost = async (req, res) => {
  try {

    const { error, value } = initializeShipMentSchema.multiPackageShipmentCostSchema.validate(req.body);

    if (error) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
        miscConstants.ERRORCODES.BAD_REQUEST,
        error.details[0].message,
        req.body
      );
    }

    let { sourcePincode, destinationPincode, packageDetails, orderType, mode, isCod } = value;

    // Validate package details
    if (!Array.isArray(packageDetails) || packageDetails.length === 0) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
        miscConstants.ERRORCODES.BAD_REQUEST,
        "Package details must be an array with at least one package",
        req.body
      );
    }

    const clientId = req.user.clientId;
    orderType = orderType || (req.user?.client?.isB2BAccess ? ServiceType.B2B : ServiceType.B2C);

    // Initialize provider-wise data aggregation
    const providerRates = {};

    for (const packet of packageDetails) {
      const { packageId, dimensions, weight } = packet;

      const costs = await ShipmentsService.calculateShipmentCost({
        clientId,
        sourcePincode,
        destinationPincode,
        weight: Number(weight.value),
        height: Number(dimensions.height),
        width: Number(dimensions.width),
        length: Number(dimensions.length),
        mode,
        orderType,
        isCod
      });

      if (costs.isServiceable) {
        for (const rate of costs.shippingRates) {
          const {
            providerId,
            providerName,
            cost,
            shippingCharges,
            codCharges,
            weight,
            weightSlabId,
            weightSlab,
            zone,
            pincodeDetails
          } = rate;

          if (!providerRates[providerId]) {
            providerRates[providerId] = {
              providerId,
              providerOption: `Option ${Object.keys(providerRates).length + 1}`,
              providerName,
              totalCost: 0,
              totalShippingCharges: 0,
              totalCODCharges: 0,
              totalWeight: 0,
              packages: [],
              isServiceable: true,
              pincodeDetails
            };
          }

          // Add packet details
          providerRates[providerId].packages.push({
            packageId,
            cost,
            shippingCharges,
            codCharges,
            weight,
            weightSlabId,
            weightSlab,
            zone
          });

          // Aggregate totals
          providerRates[providerId].totalCost += cost;
          providerRates[providerId].totalShippingCharges += shippingCharges;
          providerRates[providerId].totalCODCharges += codCharges;
          providerRates[providerId].totalWeight += weight;
        }
      }
    }

    // If providers have serviceable options, return response
    const providerRatesArray = Object.values(providerRates);
    if (providerRatesArray.length > 0) {
      return helpers.sendSuccess(
        res,
        miscConstants.HTTPSTATUSCODES.OK,
        miscConstants.SUCESSSMESSAGES.GET,
        providerRatesArray
      );
    } else {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.NOT_FOUND,
        miscConstants.ERRORCODES.NOT_FOUND,
        `No rates found for ${sourcePincode} to ${destinationPincode}`,
        req.body
      );
    }

  } catch (error) {
    console.error("Error in getMultiPackageShipmentCost:", error); // Logging for debugging
    return helpers.sendError(
      res,
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
      error.message,
      req.body
    );
  }
};


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */
exports.trackShipment = async (req, res) => {
  try {
    const { orderId = null, format = 'Json', awb = null, shippingProviderId = null, orderType = 'forward'} = req.query;
    if (!orderId && !awb) {
       return helpers.sendError(res, 
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
        miscConstants.ERRORCODES.BAD_REQUEST, 
        "Please provide Order Id or AWB should be numeric.", 
        req.query
      );
    }
    const response = await ShipmentsService.trackShipment(orderId, format, awb, shippingProviderId, orderType);
    if (response.success === false) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.NOT_FOUND,
        miscConstants.ERRORCODES.NOT_FOUND,
        response.message,
        response
      );
    } else {
      return helpers.sendSuccess(
        res, 
        miscConstants.HTTPSTATUSCODES.OK, 
        miscConstants.SUCESSSMESSAGES.GET, 
        response
      );
    }

  } catch (error) {
    return helpers.sendError(
      res, 
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message, 
      req.query
    );
  }
};


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */
exports.cancelAwb = async(req, res) => {
  try {
    const { orderId } = req.body;

    // Validate request
    if (!orderId || isNaN(orderId)) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
        miscConstants.ERRORCODES.BAD_REQUEST,
        "Invalid input: orderId should be numeric",
        req.body
      );
    }

    // Call service method
    const response = await ShipmentsService.cancelAwb(orderId);

    if (response.success === false) {
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.NOT_FOUND,
        miscConstants.ERRORCODES.NOT_FOUND,
        response.message,
        response
      );
    } else {
      return helpers.sendSuccess(
        res, 
        miscConstants.HTTPSTATUSCODES.OK, 
        response.message, 
        response.data
      );
    }

  } catch (error) {
    console.error('Error in cancelAwb controller:', error);
    return helpers.sendError(
      res, 
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message,
      req.query
    );   
  }
};