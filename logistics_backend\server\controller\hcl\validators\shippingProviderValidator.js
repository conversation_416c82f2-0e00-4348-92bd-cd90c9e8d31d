const Joi = require('joi');
const CONSTANT = require("../../../../misc/constants.js");

const providerType = Object.values(CONSTANT.SHIPPING.PROVIDER_TYPE);

// Common Schema for Add and Update
const shippingProviderSchema = Joi.object({
  name: Joi.string().max(255).required(),
  providerCode: Joi.string().max(50).required(),
  providerType: Joi.string().valid(...providerType).required(),
  description: Joi.string().allow(null, ''),
  isActive: Joi.number().default(1),
  isDeleted: Joi.number().default(0),
  apiKey: Joi.string().max(255).allow(null, ''),
  apiSecret: Joi.string().max(255).allow(null, ''),
  apiBaseUrl: Joi.string().uri().max(500).allow(null, ''),
  contactEmail: Joi.string().email().allow(null, ''),
  contactPhone: Joi.string().pattern(/^[0-9]{7,15}$/).allow(null, ''),  
});

// Validation for Update (ID is required)
const updateShippingProviderSchema = shippingProviderSchema.keys({
  id: Joi.number().integer().allow(),
  providerId: Joi.number().integer().required(),
});



module.exports = { shippingProviderSchema, updateShippingProviderSchema };