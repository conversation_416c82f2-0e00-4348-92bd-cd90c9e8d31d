const OrderStatus = Object.freeze({
    NEW: 'new',
    CANCELED: 'canceled',
    CLOSED: 'closed',
    COMPLETED: 'completed',
    FULFILLED: 'fulfilled',
    PAID: 'paid',
    PENDING: 'pending',
    RECEIVED: 'received',
    REFUSED: 'refused',
    SHIPPED: 'shipped',
    SHIPPING: 'shipping',
    STAGING: 'staging',
    REFUNDED: 'refunded',
    UNFULFILLED: 'unfulfilled'
});

const PaymentStatus = Object.freeze({
    CANCELLED: 'cancelled',
    CLOSED: 'closed',
    COMPLETED: 'completed',
    PAID: 'paid',
    PENDING: 'pending',
    RECEIVED: 'received',
    REFUSED: 'refused',
    REFUNDED: 'refunded'
});

const PaymentMode = Object.freeze({
    COD: 'COD',
    Prepaid: 'Prepaid',
});

const PAYMENT_MODES = Object.freeze({
    COD: 'COD',
    Prepaid: 'Prepaid',
});

const ServiceType = Object.freeze({
    B2B: 'B2B',
    B2C: 'B2C',
});

const ORDER_TYPES = Object.freeze({
    B2B: 'B2B',
    B2C: 'B2C',
});

const ORDER_TYPES_ARRAY = [ 'B2B', 'B2C' ];

const SHIPMENT_TYPES = Object.freeze({
    FORWARD: "forward",
    REVERSE: "reverse"
});



// to run the tracking for orders
const TRACKING_STATUSES = 'shipped, in_transit, rto_initiated, rto_in_transit';


 // Define valid status transitions
 const StatusTransitions = {
    'PENDING': ['CONFIRMED', 'canceled'],
    'CONFIRMED': ['PROCESSING', 'canceled'],
    'PROCESSING': ['SHIPPED', 'canceled'],

    'new': ['ready_to_ship', 'canceled'],
    'ready_to_ship': ['pickup_scheduled', 'shipped', 'canceled'],
    'pickup_scheduled': ['out_for_pickup', 'shipped', 'canceled'],
    'out_for_pickup': ['shipped', 'in_transit', 'canceled'],
    'shipped': ['in_transit', 'canceled'],
    'in_transit': ['out_for_delivery', 'delivered', 'undelivered', 'rto_initiated', "rto_it"],
    'out_for_delivery': ['delivered', 'undelivered', 'rto_initiated', "rto_it"],
    'undelivered': ['out_for_delivery', 'rto_initiated', "rto_it" ],
    'rto_it': ["rto" ],
    'delivered': [],
    'canceled': [],
    'returned': []
};


const GroupStatusesByState = (statuses) => {
    return statuses.reduce((acc, { state, state_label, label, status, type }) => {
        if (!acc[state]) {
            acc[state] = {state, state_label};
            acc[state]['statuses'] = [];
        }
        acc[state]['statuses'].push({ status, state_label, label, type });
        return acc;
    }, {});
};

const GroupStatusesByStatuses = (statuses) => {
    return statuses.reduce((acc, { state, state_label, label, status, type }) => {
        if (!acc[status]) {
            acc[status] = [];
        }
        acc[status].push({ state, state_label, status, label, type });
        return acc;
    }, {});
};

const GroupDataMasterByTypes = (orderTypeMaster) => {
    return orderTypeMaster.reduce((acc, { type, value }) => {
        if (!acc[type]) {
            acc[type] = [];
        }
        acc[type].push(value);
        return acc;
    }, {});
};

/**
 * 
 * @param {*} shippingProviderId 
 */
const GroupByShipingProviders = async(shippingProvidersData) => {
    const resultMap = new Map();
    shippingProvidersData.forEach(({ id, name }) => resultMap.set(id, name));
    return shippingProvidersData.reduce((acc, { id, name }) => {
        acc[parseInt(id, 10)] = name;
        return acc;
    }, {});
};


module.exports = {
    OrderStatus, 
    PaymentStatus, 
    PaymentMode,
    StatusTransitions,
    ServiceType,
    PAYMENT_MODES,
    ORDER_TYPES,
    ORDER_TYPES_ARRAY,
    SHIPMENT_TYPES,
    TRACKING_STATUSES,
    GroupStatusesByState,
    GroupStatusesByStatuses, 
    GroupDataMasterByTypes,
    GroupByShipingProviders,
};
