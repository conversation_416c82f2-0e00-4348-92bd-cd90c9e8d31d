-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: cclogs
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `_logs_error_mapping`
--

DROP TABLE IF EXISTS `_logs_error_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_logs_error_mapping` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `cc_operation` varchar(256) NOT NULL,
  `fby_alert_code` varchar(128) NOT NULL,
  `fby_alert_domain` varchar(256) NOT NULL,
  `fby_error_message` varchar(1024) NOT NULL DEFAULT '',
  `is_active` int DEFAULT '1',
  `createdOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedOn` datetime DEFAULT NULL,
  PRIMARY KEY (`id`,`cc_operation`,`fby_alert_code`,`fby_alert_domain`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_logs_error_mapping`
--

LOCK TABLES `_logs_error_mapping` WRITE;
/*!40000 ALTER TABLE `_logs_error_mapping` DISABLE KEYS */;
INSERT INTO `_logs_error_mapping` VALUES (1,'GET_ACCEPT_ORDER_MIRAKL','ORDER','Satellite','failed to get accept order mirakl',0,'2023-06-28 13:04:09',NULL),(2,'GET_ORDER_FROM_CHANNEL','ORDER','Satellite','Lettura ordini dal canale fallita',1,'2023-06-28 13:04:09',NULL),(3,'PUSH_ORDER_TO_FBY','ORDER','Satellite','Scrittura ordine ##orderid## su FBY fallita',1,'2023-06-28 13:04:09',NULL),(4,'PUSH_CANCELLED_ORDER_TO_FBY','RETURNED','Satellite','failed to push cancelled order to fby',0,'2023-06-28 13:04:09',NULL),(5,'GET_FULFILLMENT_ORDERS','SHIPMENT','Satellite','failed to get fulfillment orders',0,'2023-06-28 13:04:09',NULL),(6,'GET_STOCK_FROM_FBY','STOCK','Satellite','Ricezione stock da FBY fallita',1,'2023-06-28 13:04:09',NULL),(7,'PUSH_STOCK_TO_CHANNEL','STOCK','Satellite','Aggiornamento stock ##sku## sul canale fallito',1,'2023-06-28 13:04:09',NULL),(8,'GET_VALIDATE_SHIPMENT_MIRAKL','TRACKS','Satellite','failed to get validate shipment mirakl',0,'2023-06-28 13:04:09',NULL),(9,'PUSH_TRACKING_TO_CHANNEL','SHIPMENT','Satellite','Aggiornamento tracking ordine ##orderid## sul canale fallito',1,'2023-06-28 13:04:09',NULL),(10,'GET_MASTER_DATA_CARRIER_MIRAKL','UNKNOWN','Satellite','failed to get master data carrier mirakl',0,'2023-06-28 13:04:09',NULL),(11,'GET_PRODUCT_FROM_CHANNEL','UNKNOWN','Satellite','failed to get product from channel',0,'2023-06-28 13:04:09',NULL),(12,'GET_TRACKING_FROM_FBY','SHIPMENT','Satellite','Ricezione tracking da FBY fallita',1,'2023-07-31 07:31:52',NULL),(13,'PUSH_ORDER_TO_FBY_MISSING_DATA','ORDER','Ordini','Ordine ##orderid## non importato per dati obbligatori mancanti',1,'2023-09-14 07:31:52',NULL);
/*!40000 ALTER TABLE `_logs_error_mapping` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:25:25
