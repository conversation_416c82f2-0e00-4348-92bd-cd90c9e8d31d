DROP PROCEDURE IF EXISTS channelconnector._1_client_Post;

DE<PERSON><PERSON>ITER $$
CREATE PROCEDURE channelconnector.`_1_client_Post`(
  `in_clientId` varchar(1024),
  `in_name` varchar(1024),
  `in_ownerCode` varchar(1024)
  )
BEGIN
	/*
		call channelconnector.`_1_client_Post`(			'12345',			'Test Name',            'Test Owner Code'        );
        
         call channelconnector.`_1_client_Get`(
			''
        );
        
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
		WHERE 
			LOWER(T.`clientId`) = LOWER(`in_clientId`) 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
        INSERT INTO `channelconnector`.`_1_client`
		(
			`clientId`,
			`name`,
			`ownerCode`,
			`isActive`
		)
		VALUES
		(
			in_clientId,
			in_name,
			in_ownerCode,
			1
		);
        
		call channelconnector.`_1_client_Get`(`in_clientId`);
 
    END IF;
    
END$$
DELIMITER ;
