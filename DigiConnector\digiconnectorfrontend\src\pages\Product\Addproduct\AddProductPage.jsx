import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { NavBar } from '../../../components/Navbar/Navbar';
import { Sidebar } from '../../../components/SidePanel/Sidebar.jsx';
import { MediaComponent, InventoryComponent, VariantTable, OrganizeAndClassifyComponent, WeightAndDimensionsComponent, VariantsComponent, TitleAndDescriptionComponent } from './AddProductComponent.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSave, faArrowLeft, faUpload } from '@fortawesome/free-solid-svg-icons';
import './addProductPage.css';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, DialogTitle } from '@mui/material'; // Import Material-UI components


export const AddProductPage = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dialogVisible, setDialogVisible] = useState(false); // Define dialog visibility state

  // Define openDialog function
  const openDialog = () => {
    setDialogVisible(true);
  };

  const [product, setProduct] = useState({
    newProduct: {
      fields: {
        title: '',
        tags: [''],
        brand: '',
        weight: { unit: 'kg', value: '' },
        dimensions: { unit: 'm', width: '', height: '', length: '' },
        short_description: '',
        description: '',
        categories: [''],
        price: '',
        quantity: '',
        sku: '',
        barcode: '',
        asin: '',
        product_type: 'Test Product'
      },
      photos: ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],
      variants: [],
      options: [
        { name: 'Size', values: ['S', 'M', 'L'] },
        { name: 'Color', values: ['Red', 'Blue', 'Green'] }
      ],
      isUpdate: 'no',
    },
  });

  useEffect(() => {
    if (productId) {
      fetchProductDetails(productId);
    }
  }, [productId]);

  const fetchProductDetails = async (productId) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/common/api/get_product/?fby_user_id=8&sku=${productId}`,
        {
          isCreated: true,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`
          },
        }
      );
      const newProduct = response.data.newProduct;
      setProduct({
        newProduct: {
          fields: {
            title: newProduct.fields.title || '',
            tags: Array.isArray(newProduct.fields.tags) ? newProduct.fields.tags : [newProduct.fields.tags || ''],
            brand: newProduct.fields.brand || '',
            price: newProduct.fields.price || '',
            quantity: newProduct.fields.inventory_quantity || '',
            sku: newProduct.fields.sku || '',
            barcode: newProduct.fields.barcode || '',
            asin: newProduct.fields.asin || '',
            product_type: newProduct.fields.product_type || 'Test Product',
            weight: {
              value: newProduct.fields.weight_value || '',
              unit: newProduct.fields.weight_unit || 'kg'
            },
            dimensions: {
              unit: newProduct.fields.dimensions_unit || 'm',
              width: newProduct.fields.dimensions_width || '',
              height: newProduct.fields.dimensions_height || '',
              length: newProduct.fields.dimensions_length || '',
            },
            short_description: newProduct.fields.description || '',
            description: newProduct.fields.description || '',
            categories: Array.isArray(newProduct.fields.categories) ? newProduct.fields.categories : [newProduct.fields.category || ''],
          },
          photos: newProduct.photos ? newProduct.photos.map(photo => photo.image || photo) : ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],
          variants: newProduct.variants ? newProduct.variants.map(variant => ({
            id: variant.id,
            sku: variant.sku,
            barcode: variant.barcode,
            item_id: variant.item_id,
            title: variant.title,
            inventory_quantity: variant.inventory_quantity || 0,
            image: variant.image,
            price: variant.price || 0,
            specialPrice: variant.specialPrice,
            skuFamily: variant.skuFamily,
            position: variant.position || 1,
            option1: variant.option1 || '',
            option2: variant.option2 || ''
          })) : [],
          options: newProduct.options && Array.isArray(newProduct.options) ? newProduct.options : [
            { name: 'Size', values: ['S', 'M', 'L'] },
            { name: 'Color', values: ['Red', 'Blue', 'Green'] }
          ],
          isUpdate: productId ? 'yes' : 'no',
        },
      });
      console.log(newProduct);

    } catch (error) {
      console.error('Error fetching product details:', error);
    }
  };

  const validateProduct = () => {
    const errors = [];
    const fields = product.newProduct.fields;

    if (!fields.title || fields.title.trim() === '') {
      errors.push('Product title is required');
    }
    if (!fields.sku || fields.sku.trim() === '') {
      errors.push('SKU is required');
    }
    if (!fields.price || parseFloat(fields.price) <= 0) {
      errors.push('Valid price is required');
    }
    if (!fields.quantity || parseInt(fields.quantity) < 0) {
      errors.push('Valid quantity is required');
    }

    return errors;
  };

  const handleSave = async () => {
    try {
      // Validate product data
      const validationErrors = validateProduct();
      if (validationErrors.length > 0) {
        alert('Please fix the following errors:\n' + validationErrors.join('\n'));
        return;
      }

      var storedGroupCode = localStorage.getItem("groupCode");
      if (!storedGroupCode) {
        alert('Group code not found. Please login again.');
        return;
      }

      let url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}`;
      if (productId) {
        url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}&isUpdate=yes`;
      }

      console.log('Sending product data:', JSON.stringify(product, null, 2));
      console.log('Product fields:', product.newProduct.fields);
      console.log('SKU being sent:', product.newProduct.fields.sku);
      console.log('Quantity being sent:', product.newProduct.fields.quantity);
      console.log('Price being sent:', product.newProduct.fields.price);

      const res = await axios({
        url: url,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`
        },
        data: [product],
      });

      console.log('Response:', res);
      if (res.data && res.data.success) {
        alert('Product saved successfully!');
      } else {
        alert('Product saved, but there might be some issues. Check console for details.');
      }
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Error saving product: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleFieldChange = (e) => {
    const { name, value } = e.target;
    if (name === 'short_description' || name === 'description') {
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            [name]: value,
            // Also update the other description field to keep them in sync
            [name === 'short_description' ? 'description' : 'short_description']: value,
          },
        },
      }));
    } else if (name === 'categories') {
      // Handle categories as array
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            categories: value.split(',').map(cat => cat.trim()).filter(cat => cat),
          },
        },
      }));
    } else if (name === 'tags') {
      // Handle tags as array
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            tags: value.split(',').map(tag => tag.trim()).filter(tag => tag),
          },
        },
      }));
    } else if (name.startsWith('weight.')) {
      // Handle weight object properties
      const weightProp = name.split('.')[1];
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            weight: {
              ...prevProduct.newProduct.fields.weight,
              [weightProp]: value,
            },
          },
        },
      }));
    } else if (name.startsWith('dimensions.')) {
      // Handle dimensions object properties
      const dimensionProp = name.split('.')[1];
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            dimensions: {
              ...prevProduct.newProduct.fields.dimensions,
              [dimensionProp]: value,
            },
          },
        },
      }));
    } else {
      setProduct((prevProduct) => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          fields: {
            ...prevProduct.newProduct.fields,
            [name]: value,
          },
        },
      }));
    }
  };


  const handleImageChange = (file) => {
    // Upload image file and update the product's photos state
    // This function will vary depending on your backend implementation
    // For example:
    // const formData = new FormData();
    // formData.append('image', file);
    // axios.post('/upload', formData).then((response) => {
    //    const imageUrl = response.data.imageUrl;
    //    setProduct((prevProduct) => ({
    //      ...prevProduct,
    //      newProduct: {
    //        ...prevProduct.newProduct,
    //        photos: [...prevProduct.newProduct.photos, imageUrl],
    //      },
    //    }));
    // });
  };

  const handleAddProduct = (variants) => {
    setProduct((prevProduct) => ({
      ...prevProduct,
      newProduct: {
        ...prevProduct.newProduct,
        variants: variants,
      },
    }));
  };

  const handleVariantChange = (arg1, arg2, arg3) => {
    if (Array.isArray(arg1)) {
      // If the first argument is an array, assume it's an array of updatedVariants
      setProduct(prevProduct => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          variants: arg1
        },
      }));
      setProduct(prevProduct => ({
        ...prevProduct,
        newProduct: {
          ...prevProduct.newProduct,
          options: arg2
        },
      }));
    } else {
      // If the first argument is not an array, assume individual parameters (index, property, value)
      const index = arg1;
      const property = arg2;
      const value = arg3;

      setProduct(prevProduct => {
        const updatedVariants = [...prevProduct.newProduct.variants];
        updatedVariants[index][property] = value;

        return {
          ...prevProduct,
          newProduct: {
            ...prevProduct.newProduct,
            variants: updatedVariants,
          },
        };
      });
    }
  };

  const handleBack = () => {
    navigate(-1); // Go back to previous page
  };

  return (
    <>
      <NavBar selectedSidebarItem="products" />
      <Sidebar />
      <div className="page-content">
        <div className="header">
          {/* <Button onClick={handleBack} variant="contained" startIcon={<FontAwesomeIcon icon={faArrowLeft} />}>
            Back
          </Button> */}
          <ArrowBackIcon onClick={handleBack} />
          <DialogTitle><b>Product</b></DialogTitle>
          <Button onClick={handleSave} variant="contained" color="primary" startIcon={<FontAwesomeIcon icon={faSave} />}>
            Save
          </Button>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TitleAndDescriptionComponent product={product} handleFieldChange={handleFieldChange} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <MediaComponent product={product} handleImageChange={handleImageChange} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            {/* Inventory component */}
            <InventoryComponent product={product} handleFieldChange={handleFieldChange} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <VariantsComponent product={product} handleVariantChange={handleVariantChange} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <OrganizeAndClassifyComponent product={product} handleFieldChange={handleFieldChange} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <WeightAndDimensionsComponent product={product} handleFieldChange={handleFieldChange} />
          </Grid>
        </Grid>
      </div>
    </>
  );
};