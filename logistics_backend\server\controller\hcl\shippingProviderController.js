const { shippingProviderSchema, updateShippingProviderSchema } = require('./validators/shippingProviderValidator');
const ShippingProvideService = require('../../../services/hcl/shippingProviderService.js');
const helpers = require('../../../misc/helpers');
const miscConstants = require("../../../misc/constants");

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */
exports.addShippingProvider = async (req, res) => {
    try {
      // Validate input using Joi schema
      const { error, value } = shippingProviderSchema.validate(req.body, { abortEarly: false });
  
      if (error) {
        return helpers.sendError(
          res,
          miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
          miscConstants.ERRORCODES.VALIDATION_ERROR,
          error.details.map((err) => err.message),
          value
        );
      }
  
      // Add createdBy using user ID
      const shippingData = { ...value, createdBy: req?.user?.id };
  
      // Call service to add shipping provider
      const result = await ShippingProvideService.addShippingProvider(shippingData);
  
      return helpers.sendSuccess(
        res,
        miscConstants.HTTPSTATUSCODES.CREATED,
        'Shipping provider added successfully',
        result
      );
  
    } catch (error) {
      console.error('Error adding shipping provider:', error);
  
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
        error.message,
        req.body
      );
    }
  };

  /**
   * 
   * @param {*} req 
   * @param {*} res 
   * @returns 
   */
  exports.updateShippingProvider = async (req, res) => {
    try {
      // Validate input using Joi schema
      const { error, value } = updateShippingProviderSchema.validate(
        { ...req.body, providerId: parseInt(req.params.id) },
        { abortEarly: false }
      );
  
      if (error) {
        return helpers.sendError(
          res,
          miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
          miscConstants.ERRORCODES.VALIDATION_ERROR,
          error.details.map((err) => err.message)
        );
      }
  
      // Update provider with validated data
      const result = await ShippingProvideService.updateShippingProvider({
        ...value,
        updatedBy: req?.user?.id,
      });
  
      return helpers.sendSuccess(
        res,
        miscConstants.HTTPSTATUSCODES.OK,
        miscConstants.SUCESSSMESSAGES.UPDATE,
        result
      );
    } catch (error) {
      console.error('Error updating shipping provider:', error);
  
      return helpers.sendError(
        res,
        miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
        error.message
      );
    }
  };
  

exports.getShippingProviders = async (req, res) => {
  try {
    const { includeInActive = 'false', useCache = 'true' } = req.query; // Query parameter to include inactive providers
    const isActiveFilter = includeInActive === 'true' ? false : true;
    const shouldUseCache = useCache === 'true';

    const providers = await ShippingProvideService.getShippingProviders(isActiveFilter, shouldUseCache);
    
    return helpers.sendSuccess(
      res,
      miscConstants.HTTPSTATUSCODES.OK,
      'Shipping providers fetched successfully',
      providers
    );
  } catch (error) {
    return helpers.sendError(
      res,
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
      error.message,
      req.body
    );
  }
};

exports.getShippingProviderDeatils = async (req, res) => {
    try {
        const { providerId } = req.params;
        if (!providerId|| isNaN(providerId)) {
            helpers.sendError(res, 400, 'VALIDATION_ERROR', 'Invalid shipping provider ID', req.params)
          }
        const providerDetails = await ShippingProvideService.getShippingProviderDeatils(providerId);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            'Shipping providers fetched successfully', 
            providerDetails
        );
    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.params
        );
    }
};

exports.addPriorities = async (req, res) => {
    try {
        const {
            clientId,
            zoneId,
            weightSlabId,
            mode,
            orderType,
            priority1ProviderId,
            priority2ProviderId,
            priority3ProviderId,
        } = req.body;

        const result = await ShippingProvideService.addPriorities(
            clientId,
            zoneId,
            weightSlabId,
            mode,
            orderType,
            priority1ProviderId,
            priority2ProviderId,
            priority3ProviderId, 
            req.user.id);

        helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, result);   
    } catch (error) {
        helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

exports.addBulkPriorities = async (req, res) => {
    try {
        const { clientId, weightSlabId, mode, orderType, priorities } = req.body;
       // Validate request body
       if (!clientId || !weightSlabId || !mode || !orderType || !priorities || !Array.isArray(priorities)) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, miscConstants.ERRORCODES.BAD_REQUEST, "Invalid request. Ensure all required fields are provided.", req.query);
        }
        const result = await ShippingProvideService.addBulkPriorities({
            clientId,
            weightSlabId,
            mode,
            orderType,
            priorities
        },     
            req.user.id);

        helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, result);   
    } catch (error) {
        helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

exports.getPriorities = async (req, res) => {
    try {
        const { clientId, orderType, mode } = req.query;

        if (!clientId || !orderType || !mode) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, miscConstants.ERRORCODES.BAD_REQUEST, "ClientId, ordertype and mode are required!", req.query);
        }
        const priorities = await ShippingProvideService.getPriorities(clientId, orderType, mode);
        helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, miscConstants.SUCESSSMESSAGES.GET, priorities); 
    } catch (error) {
        helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.query);
    }
};

