ALTER TABLE `channelconnector`.`order_details` 
<PERSON>AN<PERSON> COLUMN `channel` `channel` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `channel_code` `channel_code` VARCHAR(128) CHARACTER SET 'latin1' COLLATE 'latin1_general_ci' NULL DEFAULT NULL ,
CHAN<PERSON> COLUMN `owner_code` `owner_code` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `payment_status` `payment_status` VARCHAR(128) NULL DEFAULT NULL ,
<PERSON><PERSON><PERSON> COLUMN `order_status` `order_status` VARCHAR(128) NULL DEFAULT NULL ,
CHAN<PERSON> COLUMN `cron_name` `cron_name` VARCHAR(256) NULL DEFAULT NULL ;

ALTER TABLE `channelconnector`.`order_masters` 
CHANGE COLUMN `channel_code` `channel_code` VARCHAR(128) CHARACTER SET 'latin1' COLLATE 'latin1_general_ci' NULL DEFAULT NULL ,
CHAN<PERSON> COLUMN `owner_code` `owner_code` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `SalesChannel` `SalesChannel` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `ship_state` `ship_state` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `ship_state_code` `ship_state_code` VARCHAR(128) CHARACTER SET 'latin1' COLLATE 'latin1_general_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `ship_country_code` `ship_country_code` VARCHAR(128) CHARACTER SET 'latin1' COLLATE 'latin1_general_ci' NULL DEFAULT NULL ,
CHANGE COLUMN `payment_status` `payment_status` VARCHAR(128) NULL DEFAULT NULL ,
CHANGE COLUMN `order_status` `order_status` VARCHAR(128) NULL DEFAULT NULL ;
