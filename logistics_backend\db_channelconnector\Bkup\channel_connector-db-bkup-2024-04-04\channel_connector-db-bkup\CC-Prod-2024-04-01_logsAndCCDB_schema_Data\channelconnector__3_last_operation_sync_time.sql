-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `_3_last_operation_sync_time`
--

DROP TABLE IF EXISTS `_3_last_operation_sync_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_3_last_operation_sync_time` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` int NOT NULL,
  `last_opearion_sync_time` datetime DEFAULT NULL,
  `cc_operation` varchar(512) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`,`fby_user_id`),
  UNIQUE KEY `channelId_UNIQUE` (`fby_user_id`,`cc_operation`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_3_last_operation_sync_time`
--

LOCK TABLES `_3_last_operation_sync_time` WRITE;
/*!40000 ALTER TABLE `_3_last_operation_sync_time` DISABLE KEYS */;
INSERT INTO `_3_last_operation_sync_time` VALUES (23,43,'2024-03-21 22:42:03','GET_STOCK_FROM_FBY','2023-06-23 13:28:19','2024-03-21 23:42:03'),(24,8,'2024-02-16 11:50:41','GET_STOCK_FROM_FBY','2023-06-23 13:28:21','2024-02-16 12:50:41'),(25,25,'2023-07-01 00:00:00','GET_STOCK_FROM_FBY','2023-06-23 13:30:18','2023-06-23 17:10:19'),(26,42,'2023-12-15 13:39:05','GET_STOCK_FROM_FBY','2023-06-23 13:30:18','2023-12-15 14:39:05'),(27,20,'2023-10-25 09:52:08','GET_STOCK_FROM_FBY','2023-06-23 13:30:19','2023-10-25 10:52:08'),(28,50,'2024-01-22 13:07:09','GET_STOCK_FROM_FBY','2023-06-23 13:32:18','2024-01-22 14:07:09'),(29,24,'2023-10-25 09:54:05','GET_STOCK_FROM_FBY','2023-06-23 13:32:20','2023-10-25 10:54:05'),(30,27,'2024-03-05 10:41:02','GET_STOCK_FROM_FBY','2023-06-23 13:33:18','2024-03-05 11:41:02'),(31,34,'2023-07-12 09:34:55','GET_STOCK_FROM_FBY','2023-06-23 13:34:21','2023-07-12 10:34:55'),(32,39,'2024-03-10 00:00:00','GET_STOCK_FROM_FBY','2023-06-23 13:05:21','2024-03-11 10:35:03'),(33,32,'2023-11-02 08:42:03','GET_STOCK_FROM_FBY','2023-06-23 13:36:18','2023-11-02 09:42:03'),(34,38,'2023-10-25 09:56:07','GET_STOCK_FROM_FBY','2023-06-23 13:36:22','2023-10-25 10:56:07'),(35,40,'2024-03-10 00:00:00','GET_STOCK_FROM_FBY','2023-06-23 13:07:37','2024-03-11 11:40:47'),(36,37,'2024-03-28 18:45:10','GET_STOCK_FROM_FBY','2023-06-23 13:39:18','2024-03-28 19:45:10'),(37,49,'2024-03-11 18:55:43','GET_STOCK_FROM_FBY','2023-06-23 13:43:34','2024-03-11 19:55:43'),(38,51,'2023-09-08 15:24:23','GET_STOCK_FROM_FBY','2023-06-23 13:44:50','2023-09-08 16:24:23'),(39,52,'2024-03-13 18:57:45','GET_STOCK_FROM_FBY','2023-06-23 13:46:37','2024-03-13 19:57:45'),(40,55,'2024-03-28 16:43:03','GET_STOCK_FROM_FBY','2023-07-20 15:43:18','2024-03-28 17:43:03'),(41,50,'2023-06-01 00:00:00','GET_STOCKS_FROM_FBY','2023-07-27 17:53:47','2023-07-27 17:55:56'),(42,39,'2023-09-12 17:20:24','GET_STOCKS_FROM_FBY','2023-09-12 18:20:24','2023-09-12 18:20:24'),(43,37,'2023-09-04 13:40:32','GET_STOCKS_FROM_FBY','2023-09-12 18:20:38','2023-09-12 18:20:38'),(44,57,'2024-01-24 13:58:03','GET_STOCK_FROM_FBY','2023-10-10 16:48:03','2024-01-24 14:58:03');
/*!40000 ALTER TABLE `_3_last_operation_sync_time` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:28:55
