.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 75vh; 
}

.login-form {
    width: 500px;
    max-width: 90%;
    background-color: white;
    border-radius: 5px;
    padding: 4rem;
    box-shadow: 6px 12px 60px rgba(0, 0, 0, 0.20);
}

.login-form-inner {
    width: 100%;
}

form {
    width: 100%;
}

.text-center {
    text-align: center;
}

input[type='text'],
input[type='email'],
input[type='password'] {
    width: 100%;
    padding: 0.8rem 1.2rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
}

button[type='submit'] {
    width: 100%;
    padding: 1.2rem 1.6rem;
    background-color: #666;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
}

button[type='submit']:hover {
    background-color: #0056b3;
}

.text-critical {
    color: #ff0000;
    font-size: 14px;
    margin-top: 5px;
}

.form-field-container {
    margin-bottom: 1.5rem;
}

.mt-1 {
    margin-top: 1rem;
}

.text-interactive {
    color: #007bff;
    text-decoration: none;
}

.text-interactive:hover {
    text-decoration: underline;
}