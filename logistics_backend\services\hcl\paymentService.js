 
const pool = require('../../startup/db');
// const { logRequest, logError } = require('../services/loggingService');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

const paymentService = {
    // Service to get all payment methods
    async getPaymentMethods() {
        try {
            // Log the SQL procedure call
            let sql = `${hcldb}.GetPaymentMethod`;
            let inputs = [];
            // Execute the stored procedure
            let [rows] = await pool.executeProcedure(sql,inputs);
            return rows;
        } catch (error) {
            throw error; // Bubble up the error to be handled by the controller
        }
    }
};

module.exports = paymentService;
