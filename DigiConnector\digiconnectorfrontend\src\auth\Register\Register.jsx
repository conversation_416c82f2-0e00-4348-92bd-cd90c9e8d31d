import React, { useState } from 'react';
import axios from 'axios';
import { Link, useNavigate } from 'react-router-dom';
import './register.css';
import { translate } from '../translate';
import { NavBar } from '../../components/Navbar/Navbar';

export const Register = () => {
    const navigate = useNavigate();

    // Form state variables
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [mobile, setMobile] = useState('');
    const [password, setPassword] = useState('');
    const [groupCode, setGroupCode] = useState('');
    const [clientId, setClientId] = useState('');
    const [organizationId, setOrganizationId] = useState('');
    const [roleId, setRoleId] = useState('');
    const [createdBy, setCreatedBy] = useState('');

    const [error, setError] = useState(null);

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Basic validation
        if (!name || !email || !mobile || !password || !groupCode || !clientId || !organizationId || !roleId || !createdBy) {
            setError("Please fill in all the required fields.");
            return;
        }

        try {
            await axios.post(`${process.env.REACT_APP_BASE_URL}/register`, {
                name,
                email,
                mobile,
                password,
                groupCode,
                clientId: parseInt(clientId),
                organizationId: parseInt(organizationId),
                roleId: parseInt(roleId),
                createdBy: parseInt(createdBy),
            });

            navigate('/login');
        } catch (error) {
            console.error(error);
            setError("Registration failed. Please try again.");
        }
    };

    return (
        <>
            <NavBar selectedSidebarItem="login" />
            <div className="register-container">
                <div className="register-form">
                    <div className="register-form-inner">
                        <h1 className="text-center">Create A New Account</h1>
                        {error && <div className="text-critical mb-1">{error}</div>}
                        <form onSubmit={handleSubmit}>
                            <div className="form-field-container">
                                <input id="newUsername" value={name} onChange={(e) => setName(e.target.value)} type="text" placeholder="Full Name" />
                            </div>
                            <div className="form-field-container">
                                <input id="newUserEmail" value={email} onChange={(e) => setEmail(e.target.value)} type="email" placeholder="Email" />
                            </div>
                            <div className="form-field-container">
                                <input id="mobile" value={mobile} onChange={(e) => setMobile(e.target.value)} type="tel" placeholder="Mobile Number" />
                            </div>
                            <div className="form-field-container">
                                <input id="newPassword" type="password" value={password} onChange={(e) => setPassword(e.target.value)} placeholder="Password" />
                            </div>
                            <div className="form-field-container">
                                <label htmlFor="groupCode">Select Group Code:</label>
                                <select
                                    id="groupCode"
                                    name="Group Code"
                                    value={groupCode}
                                    onChange={(e) => setGroupCode(e.target.value)}
                                >
                                    <option value="">Select Group Code</option>
                                    <option value="AEU">AEU</option>
                                    <option value="SHEU">SHEU</option>
                                    <option value="SREU">SREU</option>
                                    <option value="WCEU">WCEU</option>
                                    <option value="PSEU">PSEU</option>
                                    <option value="EEU">EEU</option>
                                    <option value="AMZ">AMZ</option>
                                    <option value="MRKL">MRKL</option>
                                    <option value="STREU">STREU</option>
                                    <option value="AMNA">AMNA</option>
                                    <option value="MGEU">MGEU</option>
                                    <option value="8">8</option>
                                </select>
                            </div>
                            <div className="form-field-container">
                                <input id="clientId" value={clientId} onChange={(e) => setClientId(e.target.value)} type="number" placeholder="Client ID" />
                            </div>
                            <div className="form-field-container">
                                <input id="organizationId" value={organizationId} onChange={(e) => setOrganizationId(e.target.value)} type="number" placeholder="Organization ID" />
                            </div>
                            <div className="form-field-container">
                                <input id="roleId" value={roleId} onChange={(e) => setRoleId(e.target.value)} type="number" placeholder="Role ID" />
                            </div>
                            <div className="form-field-container">
                                <input id="createdBy" value={createdBy} onChange={(e) => setCreatedBy(e.target.value)} type="number" placeholder="Created By (User ID)" />
                            </div>
                            <button type="submit">{translate('SIGN UP')}</button>
                        </form>
                        <div className="text-center mt-1">
                            <span>
                                Already have an account?{' '}
                                <Link to="/login" className="text-interactive">
                                    {translate('Login here')}
                                </Link>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};
