const OrderService = require('../../../services/hcl/orderService');
const BulkOrderService = require('../../../services/hcl/bulkOrderService');
const {
    orderCreateValidationSchema,
    orderUpdateValidationSchema,
    orderUpdateStatusSchema,
    bulkOrderUpdateStatusSchema,
    searcOrdersSchema
} = require('./validators/orderValidator');

const {
    trackingEventCreateSchema
} = require('./validators/trackingEventValidator');

const OrderStatus = require('../../../misc/enums/orderStatusEnum');
const helpers = require('../../../misc/helpers');
const miscConstants = require("../../../misc/constants");

const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

exports.createOrder = async (req, res) => {
    try {
        const orderData = req.body;
        const userData = req.user;
        const { error } = orderCreateValidationSchema.validate(orderData);
        if (error) {
           return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
            miscConstants.ERRORCODES.VALIDATION_ERROR,
             error.message, orderData);
        } else {
            const result = await OrderService.createOrder(orderData, userData);
            res.status(201).json({ success: true, message: 'Order created successfully!', order: result[0] });
        }
    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body)
    }
};

exports.getOrderById = async (req, res) => {
    try {
        let orderId = req.params?.id || req.params?.orderId || req.query?.orderId || req.query?.id || req.body?.orderId || req.body?.id || 0;

        if (orderId > 0) {
            const order = await OrderService.getOrder(orderId);
            if (!order) return res.status(404).json({ message: 'Order not found' });
            res.status(200).json(order);
        }
        else {
            res.status(400).json({ error: { message: "Invalid orderId" } });
        }
    } catch (error) {
        helpers.sendError(res, 500, 'getOrder', error.message, req.body)
    }
};


exports.searchOrders = async (req, res, next) => {
    try {
        // Validate Request
        const { error, value: validatedFilters } = searcOrdersSchema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.VALIDATION_ERROR,
                error.details.map(d => d.message),
                validatedFilters
            );
        }

        // Set Organization Filter
        const organizationId = req.userRoles?.isRoleSuperAdmin 
            ? null 
            : req.user?.organizationId;

        // Prepare Filters with Defaults
        const filters = {
            ...validatedFilters,
            organizationId,
            page: Math.max(1, parseInt(validatedFilters.page || 1, 10)),
            pageSize: Math.min(100, Math.max(10, parseInt(validatedFilters.pageSize || 25, 10)))
        };

        // Execute Search
        const result = await OrderService.searchOrders(filters);

        // Send Success Response
        return helpers.sendPaginationResponse(
            res,
            miscConstants.HTTPSTATUSCODES.OK,
            'Orders fetched successfully',
            result.orders,
            result.totalRecords,
            result.page,
            result.pageSize
        );

    } catch (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
            'Failed to retrieve orders',
            req.body        
        );
    } finally {
        next();
    }
};


exports.getAllOrders = async (req, res, next) => {
    try {
        // Validate and parse query parameters
        const { 
            status = null,
            state = null,
            paymentStatus = null,
            paymentMode = null,
            clientId = null,
            page = 1,
            pageSize = 25
        } = req.query;

        // Set organization filter based on user role
        const organizationId = req.userRoles?.isRoleSuperAdmin 
            ? null 
            : req.user?.organizationId;

        // 3. Convert and validate pagination parameters
        const pageNum = Math.max(1, parseInt(page, 10));
        const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize, 10)));

        // 4. Call service layer
        const { orders, pagination } = await OrderService.getAllOrders({
            status,
            state,
            paymentStatus,
            paymentMode,
            clientId,
            organizationId,
            page: pageNum,
            pageSize: pageSizeNum
        });

        // 5. Send response
        res.status(200).json({
            success: true,
            data: {
                orders,
                pagination
            }
        });

    } catch (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
            'Failed to retrieve orders',
            req.body        
        );
    } finally {
        next();
    }
};

exports.updateOrder = async (req, res) => {
    try {
        const { orderId } = req.params;
        const userData = req.user;
        const orderData = req.body;
        const { error, value } = orderUpdateValidationSchema.validate(orderData);
        if (error) {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, miscConstants.ERRORCODES.VALIDATION_ERROR, error.message, orderData);
        }
        if (isNaN(orderId)) {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, miscConstants.ERRORCODES.VALIDATION_ERROR, 'Order Id should be numeric', orderData);
        }

        if (orderId > 0) {
            const order = await OrderService.getOrder(orderId);
            if (!order) return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, miscConstants.ERRORCODES.NOT_FOUND, 'Order not found');
     
            const result = await OrderService.updateOrder(orderId, orderData, userData);

            if (result[0]?.[0]?.affectedRows === 0) return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.CONFLICT, 
                miscConstants.ERRORCODES.DUPLICATE_RESOURCE, result[0]?.[0]?.message);

            return res.status(200).json(
                { success: true, message: result[0]?.[0]?.message, data: { orderId: result[0]?.[0]?.orderId } }
            );
        }
        else {
            return res.status(400).json({ error: { message: "Invalid orderId" } });
        }

    } catch (error) {
       return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

exports.orderUpdateStatus = async (req, res, next) => {
    try {

        var userData = req.user;
        // process single order update
        if (!Array.isArray(req.body.orders)) {
            const { error } = orderUpdateStatusSchema.validate(req.body);
            if (error) {
              return helpers.sendError(res, 400, 'VALIDATION_ERROR', error.details[0].message, req.body);
            }

            const { orderId, status, remarks, timestamp = null } = req.body;

            // const order = await OrderService.checkOrderExists(orderId);
            // if (!order) return helpers.sendError(res, 404, 'RESOURCE_NOT_FOUND', 'Order not Found', req.body);

            const orderUpdate = await OrderService.orderUpdateStatus(orderId, status, remarks, timestamp, userData.id);
            return helpers.sendSuccess(res, 200, orderUpdate.message, { orderId: orderUpdate.orderId }, req.body);
        }

        const { error } = bulkOrderUpdateStatusSchema.validate(req.body.orders);
        if (error) {
            return helpers.sendError(res, 400, 'VALIDATION_ERROR', error.details[0].message, req.body);
        }
        // For bulk orders, iterate over each and process  
        const results = [];
        for (const order of req.body.orders) {
            const { orderId, status, remarks, timestamp = null } = order;
            try {
                const updateResult = await OrderService.orderUpdateStatus(orderId, status, remarks, timestamp, userData.id);
                results.push({
                    orderId,
                    status: 'SUCCESS',
                    message: updateResult.message
                });
            } catch (error) {
                results.push({
                    orderId,
                    status: 'FAILED',
                    message: error.message
                });
            }
        }
        // Send the bulk response
        return helpers.sendSuccess(res, 200, 'Bulk order update processed', results, req.body);
    } catch (error) {
        helpers.sendError(res, 500, 'INTERNAL_SERVER_ERROR', error.message, req.body);
    }
    next();
};

exports.deleteOrder = async (req, res, next) => {
    try {
        const { orderId } = req.params;
        const userData = req.user;
        if (!orderId || isNaN(orderId)) {
            helpers.sendError(res, 400, 'VALIDATION_ERROR', 'Invalid order ID', req.params);
        }
        const order = await OrderService.checkOrderExists(orderId);
        if (!order) helpers.sendError(res, 404, 'RESOURCE_NOT_FOUND', 'Order not Found', req.params);
        const deleteOrder = await OrderService.deleteOrder(orderId, userData.id);
        helpers.sendSuccess(res, 200, deleteOrder.message, { orderId: deleteOrder.orderId }, req.params);

    } catch (error) {
        helpers.sendError(res, 500, 'INTERNAL_SERVER_ERROR', error.message, req.params);
    }
    next();
};

exports.addTrackingEvents = async (req, res, next) => {
    try {
        const { orderId } = req.params;

        if (!orderId || isNaN(orderId)) {
            return helpers.sendError(res, 400, 'VALIDATION_ERROR', 'Invalid order ID', req.params);
        }

        const trackingEventArray = req.body.trackingDetails;

        const { error } = trackingEventCreateSchema.validate(trackingEventArray);

        if (error) {
            return res.status(400).json({ errors: error.details });
        }

        try {
            const trackingDetails = await OrderService.addTrackingEvents(orderId, trackingEventArray);
            const responseMessage = 'Tracking event(s) added successfully';
            return helpers.sendSuccess(res, 200, responseMessage, { orderId, trackingDetails }, req.params);
        } catch (err) {
            const responseMessage = `Error adding tracking event: ${err.message}`;
            return helpers.sendError(res, 500, 'RESOURCE_NOT_FOUND', responseMessage, req.params);
        }

    } catch (error) {
        return helpers.sendError(res, 500, 'INTERNAL_SERVER_ERROR', error.message, req.params);
    }
};


exports.getTrackingEvents = async (req, res, next) => {
    try {
        const { orderId } = req.params;
        const userData = req.user;   
        let responseMessage = "Request initiated";
        
         
        if (!orderId || isNaN(orderId)) {
            responseMessage = 'Invalid order ID';
            helpers.sendError(res, 400, 'VALIDATION_ERROR', responseMessage, req.params);
            return;
        }

        try {
             
            const trackingEvents = await OrderService.getTrackingEvents(orderId);

             
            if (trackingEvents.length === 0) {
                responseMessage = 'No tracking events found for this order';
                helpers.sendError(res, 404, 'NOT_FOUND', responseMessage, req.params);
                return;
            }
            
            responseMessage = 'Tracking events fetched successfully';
            helpers.sendSuccess(res, 200, responseMessage, { trackingDetails: trackingEvents }, req.params);
        } catch (error) {
            responseMessage = `Error fetching tracking events: ${error.message}`;
            helpers.sendError(res, 500, 'INTERNAL_SERVER_ERROR', responseMessage, req.params);
        }
    } catch (error) {
       return helpers.sendError(res, 500, 'INTERNAL_SERVER_ERROR', error.message, req.params);
    }
    next();
};


exports.uploadBulkOrders = async (req, res) => {
    
    try {
        
        if (!req.file) {
             return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST , 
                    miscConstants.ERRORCODES.VALIDATION_ERROR, 'No file uploaded', 
                    req.file);
        }    
        
        const filePath = req.file.path;
        const workbook = xlsx.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const ordersData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        const { successOrders, failedOrders } = await BulkOrderService.processBulkOrders(ordersData, req.user);

         // Save failed orders to an error log file
         if (failedOrders.length > 0) {
            const errorFilePath = path.join(miscConstants.MEDIA_PATH.UPLOADS_DIR, '/logs/failed_orders.json');
            fs.writeFileSync(errorFilePath, JSON.stringify(failedOrders, null, 2));
        }

        return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 
            `${successOrders.length} orders processed successfully. ${failedOrders.length} orders failed.`, 
            { successOrders, failedOrders }, req.params);

    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR , 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, 
            req.file);
    }
};
