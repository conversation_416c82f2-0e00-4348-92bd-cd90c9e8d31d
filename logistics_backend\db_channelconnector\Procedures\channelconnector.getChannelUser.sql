DROP PROCEDURE IF EXISTS channelconnector.getChannelUser;

DEL<PERSON><PERSON><PERSON> $$
CREATE PROCEDURE `getChannelUser`
(
	IN `in_channel_id` INT,
	IN `in_channel` VARCHAR(64),
	IN `in_channel_code` VARCHAR(20)
)
BEGIN
SET SQL_SAFE_UPDATES = 0;

    SELECT * FROM _2_channel AS T1 
	WHERE 
		T1.channelId = in_channel_id
        AND T1.channelName=in_channel 
		AND T1.channelCode = in_channel_code;
	
SET SQL_SAFE_UPDATES = 1;
END$$