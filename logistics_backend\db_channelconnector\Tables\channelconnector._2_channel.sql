
CREATE TABLE `_2_channel` (
  `id` int NOT NULL AUTO_INCREMENT,
  `channelId` int NOT NULL,
  `groupCode` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `currencyCode` varchar(56) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `ownerCode` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `channelCode` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `channelName` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `orderSyncStartDate` datetime DEFAULT NULL,
  `domain` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin NOT NULL,
  `username` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `password` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `siteId` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `compatibilityLevel` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `ebay_devid` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `ebay_appid` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `ebay_certid` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `ebay_quantity_update_by` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `apiKey` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `secret` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `token` varchar(2048) CHARACTER SET utf8mb3 COLLATE utf8_bin DEFAULT NULL,
  `isActive` tinyint DEFAULT '0',
  `isEnabled` tinyint DEFAULT '0',
  `createdon` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modifiedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `channelId_UNIQUE` (`channelId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;


/*
# DROP TABLE channelconnector._2_channel;

TRUNCATE channelconnector._2_channel;
*/

INSERT INTO `channelconnector`.`_2_channel`
(	
	`channelId` ,
	`groupCode` ,
	`currencyCode` ,
	`ownerCode` ,
	`channelCode`  ,
	`channelName` ,
	`domain`  ,
	`username` ,
	`password`  ,
	`apiKey`  ,
	`secret` ,
	`token`  ,
	`isEnabled` ,
	`isActive`
)
Select T.* from (
			Select 1002  as `channelId`,
			'AEU' as  `groupCode` ,
			'EUR' as  `currencyCode` ,
			'YT' as  `ownerCode`,
			'SFIT'  as `channelCode`, 
			'shopify'  as `channelName`,
			'shopping170.myshopify.com'  as `domain`, 
			NULL  as `username`, 
			'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' as `password`,
			'2ec972a612088fc392de502d7e4c3887' as `apiKey`, 
			'' as `secret`, 
			'' as `token`,
			1 as `isEnabled`,
            1 as `isActive`
) as T
LEFT JOIN `channelconnector`.`_2_channel` T2
ON T2.channelName = T.channelName
WHERE T2.channelName IS NULL;


SELECT 
    *
FROM
    channelconnector._2_channel;
