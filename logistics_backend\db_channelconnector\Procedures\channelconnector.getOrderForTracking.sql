DROP PROCEDURE IF EXISTS channelconnector.getOrderForTracking;

DEL<PERSON>ITER $$
CREATE PROCEDURE channelconnector.getOrderForTracking(
	IN `in_fby_user_id` VARCHAR(128)
)
BEGIN
/*,

call channelconnector.getOrderForTracking(27);

*/
DECLARE var_channel_name varchar(500);
SET SQL_SAFE_UPDATES = 0;

SET var_channel_name = (
SELECT LOWER(channelName) FROM channelconnector._2_channel
Where isActive = 1 and isEnabled = 1
AND channelId = in_fby_user_id
LIMIT 1
);

IF (var_channel_name like 'woo%comm%') 
THEN
	SELECT DISTINCT 
		OM.* , OD.status
    FROM order_masters AS OM 
    inner join order_details OD 
		on OD.fby_user_id = OM.fby_user_id
		AND OD.order_no = OM.order_no
    INNER JOIN (
		SELECT DISTINCT 
			OM1.fby_user_id,OM1.order_no 
		FROM order_masters AS OM1 
		inner join order_details OD1 
			on OD1.fby_user_id = OM1.fby_user_id
			AND OD1.order_no = OM1.order_no
		WHERE 
			OM1.fby_user_id = in_fby_user_id
			
			AND OM1.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
			AND OD1.status = 0  -- SET TO 1 when traking is updated at channel
			AND is_trackable = 1
            Order by OM1.seller_order_id
            LIMIT 5

        )    as OM2
        ON OM2.fby_user_id = OM.fby_user_id AND OM2.order_no = OM.order_no 
        
	WHERE 
		OM.fby_user_id = in_fby_user_id
        
        AND OM.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
        AND OD.status = 0  -- SET TO 1 when traking is updated at channel
        AND is_trackable = 1
         
	Order by OM.seller_order_id;

ELSE

	SELECT DISTINCT 
		OM.* , OD.status
    FROM order_masters AS OM 
    inner join order_details OD 
		on OD.fby_user_id = OM.fby_user_id
		AND OD.order_no = OM.order_no
	WHERE 
		OM.fby_user_id = in_fby_user_id
        
        AND OM.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
        AND OD.status = 0  -- SET TO 1 when traking is updated at channel
        AND is_trackable = 1
        
	Order by OM.order_no;
		-- AND OM.account_id = in_account_id 
		-- AND OM.channel_code = in_channel_code;
        
END IF;		

SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
