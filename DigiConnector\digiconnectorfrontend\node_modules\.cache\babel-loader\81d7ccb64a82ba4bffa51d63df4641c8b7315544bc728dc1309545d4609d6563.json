{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Order\\\\Addorder\\\\AddNewOrder.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, Typography, Grid, TextField, Button, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Menu, MenuItem, Select, FormControl, InputLabel, Chip, Box } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport SearchIcon from '@mui/icons-material/Search';\nimport AddIcon from '@mui/icons-material/Add';\nimport RemoveIcon from '@mui/icons-material/Remove';\nimport axios from 'axios';\nimport { NavBar } from '../../../components/Navbar/Navbar';\nimport { Sidebar } from '../../../components/SidePanel/Sidebar';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const AddNewOrder = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    orderNo\n  } = useParams();\n  const [products, setProducts] = useState([]);\n  const [groupedProducts, setGroupedProducts] = useState([]);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [productQuantities, setProductQuantities] = useState({});\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    phone: '',\n    address1: '',\n    city: '',\n    province: '',\n    country: '',\n    zip: ''\n  });\n  const [responseData, setResponseData] = useState([]);\n  const [paymentStatus, setPaymentStatus] = useState('Pending');\n  const [totalPayment, setTotalPayment] = useState(0);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [openCustomerDialog, setOpenCustomerDialog] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [paymentMethod, setPaymentMethod] = useState('');\n  const [customerAndShippingDetails, setCustomerAndShippingDetails] = useState(null);\n  const [orderStatus, setOrderStatus] = useState('fulfilled');\n  const [openTrackingDialog, setOpenTrackingDialog] = useState(false);\n  const [channelOrderId, setChannelOrderId] = useState('');\n  const [originalChannelOrderId, setOriginalChannelOrderId] = useState('');\n  const [channelCode, setChannelCode] = useState('');\n  const [skuEan, setSkuEan] = useState('');\n  const [skuCode, setSkuCode] = useState('');\n  const [tracking, setTracking] = useState('');\n  const [shipmentDate, setShipmentDate] = useState('');\n  const [carrier, setCarrier] = useState('');\n  const [shipUrl, setShipUrl] = useState('');\n  const [isReturn, setIsReturn] = useState('');\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        if (orderNo) {\n          // Fetch order details if orderNo exists\n          var storedGroupCode = localStorage.getItem(\"groupCode\");\n          const resDetails = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_order_detail?fby_user_id=${storedGroupCode}`, {\n            order_no: orderNo\n          }, {\n            headers: {\n              'Content-Type': 'application/json',\n              Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\n            }\n          });\n          if (resDetails.data.success) {\n            setResponseData(resDetails.data.success.data);\n            const responseData = resDetails.data.success.data.map(element => ({\n              id: element.id,\n              title: element.product_name,\n              image: element.image,\n              // Assuming there's an image property in the response\n              price: element.item_total_price,\n              quantity: element.quantity_purchased,\n              total_tax: element.item_tax,\n              line_item_id: element.order_line_item_id,\n              order_status: element.order_status,\n              payment_status: element.payment_status\n            }));\n            if (responseData.length > 0) {\n              setSelectedProducts(responseData.map(item => item)); // Update here\n              setOrderStatus(responseData[0].order_status);\n              setPaymentStatus(responseData[0].payment_status);\n            }\n            const shipData = resDetails.data.success.data.map(element => ({\n              first_name: element.recipient_name,\n              last_name: '',\n              email: element.buyer_email,\n              phone: element.ship_phone_number,\n              address1: element.ship_address_1,\n              city: element.ship_city,\n              province: element.ship_state_code,\n              country: element.ship_country,\n              zip: element.ship_postal_code\n            }));\n            // Set the customer and shipping details\n            setCustomerAndShippingDetails(shipData[0]);\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      }\n    };\n    fetchData();\n  }, [orderNo]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleCheckboxChange = product => {\n    setSelectedProducts(prevState => {\n      const isSelected = prevState.some(item => item.id === product.id);\n      if (!isSelected) {\n        // Initialize quantity when product is selected\n        setProductQuantities(prev => ({\n          ...prev,\n          [product.id]: 1\n        }));\n        return [...prevState, product];\n      } else {\n        // Remove quantity when product is deselected\n        setProductQuantities(prev => {\n          const newQuantities = {\n            ...prev\n          };\n          delete newQuantities[product.id];\n          return newQuantities;\n        });\n        return prevState.filter(item => item.id !== product.id);\n      }\n    });\n  };\n  const handleQuantityChange = (productId, newQuantity) => {\n    setProductQuantities(prev => ({\n      ...prev,\n      [productId]: newQuantity\n    }));\n  };\n  const handleBrowse = async () => {\n    try {\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      const res = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`, {}, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\n        }\n      });\n      if (res.data.success.data.length > 0) {\n        const responseData = res.data.success.data.map(element => ({\n          id: element.id,\n          title: element.title,\n          body_html: element.description,\n          image: element.image,\n          price: element.price,\n          quantity: element.inventory_quantity,\n          sku: element.sku,\n          option_1_value: element.option_1_value || '',\n          option_2_value: element.option_2_value || ''\n        }));\n\n        // Group products by title\n        const grouped = responseData.reduce((acc, product) => {\n          const existingGroup = acc.find(group => group.title === product.title);\n          if (existingGroup) {\n            existingGroup.variants.push(product);\n          } else {\n            acc.push({\n              title: product.title,\n              image: product.image,\n              body_html: product.body_html,\n              variants: [product]\n            });\n          }\n          return acc;\n        }, []);\n        setProducts(responseData);\n        setGroupedProducts(grouped);\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n    }\n  };\n\n  // Function to handle tracking submit\n  const handleTrackingSubmit = async () => {\n    try {\n      // Make API call to update tracking\n      const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/push_tracking`, {\n        channelOrderId: channelOrderId,\n        originalChannelOrderId: originalChannelOrderId,\n        channelCode: channelCode,\n        skuEan: skuEan,\n        skuCode: skuCode,\n        tracking: tracking,\n        shipmentDate: shipmentDate,\n        carrier: carrier,\n        shipUrl: shipUrl,\n        isReturn: isReturn\n      });\n      // Handle success response\n      console.log(response.data);\n    } catch (error) {\n      // Handle error\n      console.error('Error updating tracking:', error);\n    }\n  };\n  const handleSaveSelectedProducts = () => {\n    setSelectedProducts(selectedProducts);\n\n    // You can save the selected products to a state or perform any other required action here\n    // For example, you can update a state with the selected products\n    const selectedProductsData = selectedProducts.map(productId => {\n      return products.find(product => product.id === productId);\n    });\n    // Do something with the selected products data, for example:\n    console.log('Selected products:', selectedProductsData);\n    setOpenDialog(false);\n  };\n  const handleSaveCustomerDetails = () => {\n    // Save the entered customer and shipping details\n    const newCustomerAndShippingDetails = {\n      first_name: formData.first_name,\n      last_name: formData.last_name,\n      email: formData.email,\n      phone: formData.phone,\n      address1: formData.address1,\n      city: formData.city,\n      province: formData.province,\n      country: formData.country,\n      zip: formData.zip\n    };\n\n    // Set the customer and shipping details\n    setCustomerAndShippingDetails(newCustomerAndShippingDetails);\n\n    // Close the customer dialog\n    setOpenCustomerDialog(false);\n  };\n  const handleSave = async () => {\n    try {\n      // Prepare the data for the request\n      const requestData = {\n        line_items: selectedProducts.map(productId => {\n          const product = products.find(product => product.id === productId);\n          return {\n            variant_id: product.id,\n            quantity: product.quantity || 1 // Assuming quantity property\n          };\n        }),\n        customer: {\n          first_name: customerAndShippingDetails.first_name,\n          last_name: customerAndShippingDetails.last_name,\n          email: customerAndShippingDetails.email\n        },\n        billing_address: {\n          first_name: customerAndShippingDetails.first_name,\n          last_name: customerAndShippingDetails.last_name,\n          address1: customerAndShippingDetails.address1,\n          city: customerAndShippingDetails.city,\n          province: customerAndShippingDetails.province,\n          country: customerAndShippingDetails.country,\n          zip: customerAndShippingDetails.zip,\n          phone: customerAndShippingDetails.phone\n        },\n        shipping_address: {\n          first_name: formData.shipping_first_name,\n          last_name: formData.shipping_last_name,\n          address1: formData.address1,\n          city: formData.city,\n          province: formData.province,\n          country: formData.country,\n          zip: formData.zip,\n          phone: formData.shipping_phone\n        },\n        email: customerAndShippingDetails.email,\n        transactions: [{\n          kind: \"sale\",\n          // Assuming the transaction kind\n          status: \"success\",\n          // Assuming the transaction status\n          amount: totalPayment // Assuming the total payment amount\n        }],\n        financial_status: paymentStatus\n      };\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n\n      // Make the POST request\n      const res = await axios({\n        url: `${process.env.REACT_APP_BASE_URL}/common/api/create_shopify_order/?fby_user_id=${storedGroupCode}`,\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: process.env.REACT_APP_ACCESS_TOKEN\n        },\n        data: requestData\n      });\n      console.log(res);\n      if (res.data.success) {\n        // Handle success\n      }\n    } catch (error) {\n      console.error('Error saving Order:', error);\n    }\n  };\n  const handleAddCustomerAndShipping = () => {\n    setOpenCustomerDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n  };\n  const handleCustomerCloseDialog = () => {\n    setOpenCustomerDialog(false);\n  };\n  const handlePaymentClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handlePaymentClose = () => {\n    setAnchorEl(null);\n  };\n  const handlePaymentMethod = method => {\n    setPaymentMethod(method);\n    setAnchorEl(null);\n    if (method === 'Mark as Paid') {\n      setPaymentStatus('Paid');\n    }\n  };\n  const handleLineItemClick = lineItemId => {\n    navigate(`/orderMaster/${orderNo}/orderDetails/${lineItemId}`);\n  };\n  useEffect(() => {\n    // Calculate total payment with quantities\n    let totalPrice = 0;\n    for (const product of selectedProducts) {\n      const quantity = productQuantities[product.id] || 1;\n      const price = product.item_total_price || product.price || 0;\n      totalPrice += price * quantity;\n    }\n    setTotalPayment(totalPrice);\n  }, [selectedProducts, productQuantities]);\n  const handleBack = () => {\n    navigate(-1);\n  };\n  const handleButtonClick = () => {\n    setOpenTrackingDialog(true);\n  };\n\n  // Function to handle tracking dialog close\n  const handleTrackingDialogClose = () => {\n    setOpenTrackingDialog(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      selectedSidebarItem: \"products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      style: {\n        marginTop: '45px',\n        marginLeft: '255px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n            onClick: handleBack\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.2rem',\n              marginLeft: 8\n            },\n            children: \"Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            style: {\n              marginLeft: 'auto'\n            },\n            onClick: handleSave,\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [!orderNo && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h4\",\n              style: {\n                marginBottom: 10\n              },\n              children: \"Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex'\n              },\n              children: !orderNo && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Search Product\",\n                  variant: \"outlined\",\n                  fullWidth: true,\n                  size: \"small\",\n                  InputProps: {\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"primary\",\n                      \"aria-label\": \"search\",\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 53\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  style: {\n                    marginLeft: 10,\n                    fontSize: '0.8rem'\n                  },\n                  size: \"small\",\n                  onClick: handleBrowse,\n                  children: \"Browse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 25\n          }, this), selectedProducts.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  sx: {\n                    background: '#f5f5f5'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Title\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 49\n                    }, this), orderNo && /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Line Order Id\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: selectedProducts.map(newproduct => {\n                    const productQuantity = productQuantities[newproduct.id] || 1;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: newproduct.image || '',\n                          alt: newproduct.title,\n                          style: {\n                            maxWidth: '100px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 68\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          children: newproduct.title || ''\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 443,\n                          columnNumber: 61\n                        }, this), (newproduct.option_1_value || newproduct.option_2_value) && /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"textSecondary\",\n                          children: [newproduct.option_1_value, \" \", newproduct.option_2_value && `/ ${newproduct.option_2_value}`]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          display: \"block\",\n                          color: \"textSecondary\",\n                          children: [\"SKU: \", newproduct.sku]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 449,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 442,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleQuantityChange(newproduct.id, Math.max(1, productQuantity - 1)),\n                            disabled: productQuantity <= 1,\n                            children: /*#__PURE__*/_jsxDEV(RemoveIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 460,\n                              columnNumber: 69\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                            size: \"small\",\n                            type: \"number\",\n                            value: productQuantity,\n                            onChange: e => handleQuantityChange(newproduct.id, Math.max(1, parseInt(e.target.value) || 1)),\n                            inputProps: {\n                              min: 1,\n                              max: newproduct.quantity || 999,\n                              style: {\n                                textAlign: 'center',\n                                width: '60px'\n                              }\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 462,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleQuantityChange(newproduct.id, Math.min(newproduct.quantity || 999, productQuantity + 1)),\n                            disabled: productQuantity >= (newproduct.quantity || 999),\n                            children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 474,\n                              columnNumber: 69\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 469,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"textSecondary\",\n                          children: [\"Available: \", newproduct.quantity || 0]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 477,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: [\"$\", (newproduct.price || 0) * productQuantity]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 57\n                      }, this), orderNo && /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          onClick: () => handleLineItemClick(newproduct.line_item_id),\n                          children: newproduct.line_item_id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 484,\n                          columnNumber: 65\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 62\n                      }, this)]\n                    }, newproduct.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 53\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          style: {\n            marginTop: '20px',\n            marginLeft: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h4\",\n                children: \"Payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n                component: Paper,\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  children: /*#__PURE__*/_jsxDEV(TableBody, {\n                    children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Payment Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 507,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: paymentStatus\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: orderStatus !== 'unfulfilled' && orderStatus !== 'partially fulfilled' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: [/*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outlined\",\n                            color: \"primary\",\n                            onClick: handlePaymentClick,\n                            children: \"Collect Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 514,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n                            anchorEl: anchorEl,\n                            open: Boolean(anchorEl),\n                            onClose: handlePaymentClose,\n                            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                              onClick: () => handlePaymentMethod('Enter Credit Card'),\n                              children: \"Enter Credit Card\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 522,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                              onClick: () => handlePaymentMethod('Mark as Paid'),\n                              children: \"Mark as Paid\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 523,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 517,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 513,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outlined\",\n                            color: \"primary\",\n                            children: \"Send Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 527,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 526,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outlined\",\n                          color: \"primary\",\n                          onClick: handleButtonClick,\n                          children: \"Add Tracking\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 534,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 533,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        style: {\n          marginTop: '80px'\n        },\n        children: customerAndShippingDetails ? /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h2\",\n              style: {\n                fontSize: '1.2rem'\n              },\n              children: \"Customer and Shipping Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Name: \", customerAndShippingDetails.first_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Email: \", customerAndShippingDetails.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Phone: \", customerAndShippingDetails.phone]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Address: \", customerAndShippingDetails.address1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"City: \", customerAndShippingDetails.city]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Province: \", customerAndShippingDetails.province]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"Country: \", customerAndShippingDetails.country]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [\"ZIP: \", customerAndShippingDetails.zip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"h2\",\n              style: {\n                fontSize: '0.8rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Add Customer and Shipping Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 104\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleAddCustomerAndShipping,\n                style: {\n                  fontSize: '0.8rem',\n                  marginLeft: '5px'\n                },\n                children: \"Add\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openCustomerDialog,\n        onClose: handleCustomerCloseDialog,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Customer and Shipping Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Customer Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"First Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.first_name,\n                name: \"first_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Last Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.last_name,\n                name: \"last_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Email\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.email,\n                name: \"email\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Phone\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.phone,\n                name: \"phone\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Shipping Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"First Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_first_name,\n                name: \"shipping_first_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Last Name\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_last_name,\n                name: \"shipping_last_name\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Address\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.address1,\n                name: \"address1\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Phone\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.shipping_phone,\n                name: \"shipping_phone\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"City\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.city,\n                name: \"city\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Province\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.province,\n                name: \"province\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Country\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.country,\n                name: \"country\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"ZIP\",\n                variant: \"outlined\",\n                fullWidth: true,\n                size: \"small\",\n                value: formData.zip,\n                name: \"zip\",\n                onChange: handleChange,\n                style: {\n                  marginBottom: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveCustomerDetails,\n            color: \"primary\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCustomerCloseDialog,\n            color: \"primary\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            fontSize: '26px'\n          },\n          children: \"Select Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                sx: {\n                  background: '#f5f5f5'\n                },\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Variants\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Available Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Price\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 724,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: groupedProducts.map((productGroup, groupIndex) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: productGroup.image || '',\n                      alt: productGroup.title,\n                      style: {\n                        maxWidth: '80px',\n                        height: '80px',\n                        objectFit: 'cover'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      fontWeight: \"bold\",\n                      children: productGroup.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: productGroup.body_html\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map((variant, variantIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          label: `${variant.option_1_value || 'Default'} ${variant.option_2_value ? '/ ' + variant.option_2_value : ''}`,\n                          size: \"small\",\n                          variant: \"outlined\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 742,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          children: [\"SKU: \", variant.sku]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 747,\n                          columnNumber: 61\n                        }, this)]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [variant.quantity || 0, \" units\"]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [\"$\", variant.price || 0]\n                      }, variant.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: 1\n                      },\n                      children: productGroup.variants.map(variant => /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        checked: selectedProducts.some(item => item.id === variant.id),\n                        onChange: () => handleCheckboxChange(variant)\n                      }, variant.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 773,\n                        columnNumber: 57\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 45\n                  }, this)]\n                }, `group-${groupIndex}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveSelectedProducts,\n            color: \"primary\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            color: \"primary\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openTrackingDialog,\n        onClose: handleTrackingDialogClose,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Add Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Channel Order Id\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: channelOrderId,\n            onChange: e => setChannelOrderId(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Original Channel Order Id\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: originalChannelOrderId,\n            onChange: e => setOriginalChannelOrderId(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Channel Code\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: channelCode,\n            onChange: e => setChannelCode(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"SKU EAN\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: skuEan,\n            onChange: e => setSkuEan(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"SKU Code\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: skuCode,\n            onChange: e => setSkuCode(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tracking\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: tracking,\n            onChange: e => setTracking(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Shipment Date\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: shipmentDate,\n            onChange: e => setShipmentDate(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Carrier\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: carrier,\n            onChange: e => setCarrier(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Ship URL\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: shipUrl,\n            onChange: e => setShipUrl(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Is Return\",\n            variant: \"outlined\",\n            fullWidth: true,\n            size: \"small\",\n            value: isReturn,\n            onChange: e => setIsReturn(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleTrackingSubmit,\n            color: \"primary\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleTrackingDialogClose,\n            color: \"primary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(AddNewOrder, \"F1sUCvHlaX796zdrrNuC50YXoxQ=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = AddNewOrder;\nvar _c;\n$RefreshReg$(_c, \"AddNewOrder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "TextField", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Box", "ArrowBackIcon", "SearchIcon", "AddIcon", "RemoveIcon", "axios", "NavBar", "Sidebar", "useParams", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddNewOrder", "_s", "navigate", "orderNo", "products", "setProducts", "groupedProducts", "setGroupedProducts", "selectedProducts", "setSelectedProducts", "productQuantities", "setProductQuantities", "formData", "setFormData", "first_name", "last_name", "email", "phone", "address1", "city", "province", "country", "zip", "responseData", "setResponseData", "paymentStatus", "setPaymentStatus", "totalPayment", "setTotalPayment", "openDialog", "setOpenDialog", "openCustomerDialog", "setOpenCustomerDialog", "anchorEl", "setAnchorEl", "paymentMethod", "setPaymentMethod", "customerAndShippingDetails", "setCustomerAndShippingDetails", "orderStatus", "setOrderStatus", "openTrackingDialog", "setOpenTrackingDialog", "channelOrderId", "setChannelOrderId", "originalChannelOrderId", "setOriginalChannelOrderId", "channelCode", "setChannelCode", "sku<PERSON>an", "setSkuEan", "skuCode", "setSkuCode", "tracking", "setTracking", "shipmentDate", "setShipmentDate", "carrier", "<PERSON><PERSON><PERSON><PERSON>", "shipUrl", "setShipUrl", "isReturn", "setIsReturn", "fetchData", "storedGroupCode", "localStorage", "getItem", "resDetails", "post", "process", "env", "REACT_APP_BASE_URL", "order_no", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "data", "success", "map", "element", "id", "title", "product_name", "image", "price", "item_total_price", "quantity", "quantity_purchased", "total_tax", "item_tax", "line_item_id", "order_line_item_id", "order_status", "payment_status", "length", "item", "shipData", "recipient_name", "buyer_email", "ship_phone_number", "ship_address_1", "ship_city", "ship_state_code", "ship_country", "ship_postal_code", "error", "console", "handleChange", "e", "name", "value", "target", "handleCheckboxChange", "product", "prevState", "isSelected", "some", "prev", "newQuantities", "filter", "handleQuantityChange", "productId", "newQuantity", "handleBrowse", "res", "body_html", "description", "inventory_quantity", "sku", "option_1_value", "option_2_value", "grouped", "reduce", "acc", "existingGroup", "find", "group", "variants", "push", "handleTrackingSubmit", "response", "log", "handleSaveSelectedProducts", "selectedProductsData", "handleSaveCustomerDetails", "newCustomerAndShippingDetails", "handleSave", "requestData", "line_items", "variant_id", "customer", "billing_address", "shipping_address", "shipping_first_name", "shipping_last_name", "shipping_phone", "transactions", "kind", "status", "amount", "financial_status", "url", "method", "handleAddCustomerAndShipping", "handleCloseDialog", "handleCustomerCloseDialog", "handlePaymentClick", "event", "currentTarget", "handlePaymentClose", "handlePaymentMethod", "handleLineItemClick", "lineItemId", "totalPrice", "handleBack", "handleButtonClick", "handleTrackingDialogClose", "children", "selectedSidebarItem", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "style", "marginTop", "marginLeft", "xs", "md", "variant", "gutterBottom", "display", "alignItems", "onClick", "fontSize", "color", "component", "marginBottom", "label", "fullWidth", "size", "InputProps", "endAdornment", "sx", "background", "newproduct", "productQuantity", "src", "alt", "max<PERSON><PERSON><PERSON>", "gap", "Math", "max", "disabled", "type", "onChange", "parseInt", "inputProps", "min", "textAlign", "width", "open", "Boolean", "onClose", "sm", "productGroup", "groupIndex", "height", "objectFit", "fontWeight", "flexDirection", "variantIndex", "checked", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Order/Addorder/AddNewOrder.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Card, CardContent, Typography, Grid, TextField, Button, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Menu, MenuItem, Select, FormControl, InputLabel, Chip, Box } from '@mui/material';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport SearchIcon from '@mui/icons-material/Search';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport RemoveIcon from '@mui/icons-material/Remove';\r\nimport axios from 'axios';\r\nimport { NavBar } from '../../../components/Navbar/Navbar';\r\nimport { Sidebar } from '../../../components/SidePanel/Sidebar';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\n\r\nexport const AddNewOrder = () => {\r\n    const navigate = useNavigate();\r\n    const { orderNo } = useParams()\r\n    const [products, setProducts] = useState([]);\r\n    const [groupedProducts, setGroupedProducts] = useState([]);\r\n    const [selectedProducts, setSelectedProducts] = useState([]);\r\n    const [productQuantities, setProductQuantities] = useState({});\r\n    const [formData, setFormData] = useState({\r\n        first_name: '',\r\n        last_name: '',\r\n        email: '',\r\n        phone: '',\r\n        address1: '',\r\n        city: '',\r\n        province: '',\r\n        country: '',\r\n        zip: '',\r\n    })\r\n    const [responseData, setResponseData] = useState([]);\r\n    const [paymentStatus, setPaymentStatus] = useState('Pending');\r\n    const [totalPayment, setTotalPayment] = useState(0);\r\n    const [openDialog, setOpenDialog] = useState(false);\r\n    const [openCustomerDialog, setOpenCustomerDialog] = useState(false);\r\n    const [anchorEl, setAnchorEl] = useState(null);\r\n    const [paymentMethod, setPaymentMethod] = useState('');\r\n    const [customerAndShippingDetails, setCustomerAndShippingDetails] = useState(null);\r\n    const [orderStatus, setOrderStatus] = useState('fulfilled');\r\n    const [openTrackingDialog, setOpenTrackingDialog] = useState(false);\r\n    const [channelOrderId, setChannelOrderId] = useState('');\r\n    const [originalChannelOrderId, setOriginalChannelOrderId] = useState('');\r\n    const [channelCode, setChannelCode] = useState('');\r\n    const [skuEan, setSkuEan] = useState('');\r\n    const [skuCode, setSkuCode] = useState('');\r\n    const [tracking, setTracking] = useState('');\r\n    const [shipmentDate, setShipmentDate] = useState('');\r\n    const [carrier, setCarrier] = useState('');\r\n    const [shipUrl, setShipUrl] = useState('');\r\n    const [isReturn, setIsReturn] = useState('');\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            try {\r\n                if (orderNo) {\r\n                    // Fetch order details if orderNo exists\r\n                    var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n                    const resDetails = await axios.post(\r\n                        `${process.env.REACT_APP_BASE_URL}/common/api/get_order_detail?fby_user_id=${storedGroupCode}`,\r\n                        { order_no: orderNo },\r\n                        {\r\n                            headers: {\r\n                            'Content-Type': 'application/json',\r\n                            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\r\n                        }\r\n                        }\r\n                    );\r\n                    if (resDetails.data.success) {\r\n                        setResponseData(resDetails.data.success.data);\r\n                        const responseData = resDetails.data.success.data.map(element => ({\r\n                            id: element.id,\r\n                            title: element.product_name,\r\n                            image: element.image, // Assuming there's an image property in the response\r\n                            price: element.item_total_price,\r\n                            quantity: element.quantity_purchased,\r\n                            total_tax: element.item_tax,\r\n                            line_item_id: element.order_line_item_id,\r\n                            order_status: element.order_status,\r\n                            payment_status: element.payment_status\r\n                        }));\r\n                        if (responseData.length > 0) {\r\n                            setSelectedProducts(responseData.map(item => item)); // Update here\r\n                            setOrderStatus(responseData[0].order_status)\r\n                            setPaymentStatus(responseData[0].payment_status)\r\n                        }\r\n                        const shipData = resDetails.data.success.data.map(element => ({\r\n                            first_name: element.recipient_name,\r\n                            last_name: '',\r\n                            email: element.buyer_email,\r\n                            phone: element.ship_phone_number,\r\n                            address1: element.ship_address_1,\r\n                            city: element.ship_city,\r\n                            province: element.ship_state_code,\r\n                            country: element.ship_country,\r\n                            zip: element.ship_postal_code,\r\n                        }));\r\n                        // Set the customer and shipping details\r\n                        setCustomerAndShippingDetails(shipData[0]);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                console.error('Error fetching data:', error);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [orderNo]);\r\n\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData({ ...formData, [name]: value });\r\n    };\r\n\r\n    const handleCheckboxChange = (product) => {\r\n        setSelectedProducts(prevState => {\r\n            const isSelected = prevState.some(item => item.id === product.id);\r\n            if (!isSelected) {\r\n                // Initialize quantity when product is selected\r\n                setProductQuantities(prev => ({\r\n                    ...prev,\r\n                    [product.id]: 1\r\n                }));\r\n                return [...prevState, product];\r\n            } else {\r\n                // Remove quantity when product is deselected\r\n                setProductQuantities(prev => {\r\n                    const newQuantities = { ...prev };\r\n                    delete newQuantities[product.id];\r\n                    return newQuantities;\r\n                });\r\n                return prevState.filter(item => item.id !== product.id);\r\n            }\r\n        });\r\n    };\r\n\r\n    const handleQuantityChange = (productId, newQuantity) => {\r\n        setProductQuantities(prev => ({\r\n            ...prev,\r\n            [productId]: newQuantity\r\n        }));\r\n    };\r\n\r\n    const handleBrowse = async () => {\r\n        try {\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n\r\n            const res = await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`,\r\n                {},\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\r\n                    }\r\n                }\r\n            );\r\n            if (res.data.success.data.length > 0) {\r\n                const responseData = res.data.success.data.map(element => ({\r\n                    id: element.id,\r\n                    title: element.title,\r\n                    body_html: element.description,\r\n                    image: element.image,\r\n                    price: element.price,\r\n                    quantity: element.inventory_quantity,\r\n                    sku: element.sku,\r\n                    option_1_value: element.option_1_value || '',\r\n                    option_2_value: element.option_2_value || '',\r\n                }));\r\n\r\n                // Group products by title\r\n                const grouped = responseData.reduce((acc, product) => {\r\n                    const existingGroup = acc.find(group => group.title === product.title);\r\n                    if (existingGroup) {\r\n                        existingGroup.variants.push(product);\r\n                    } else {\r\n                        acc.push({\r\n                            title: product.title,\r\n                            image: product.image,\r\n                            body_html: product.body_html,\r\n                            variants: [product]\r\n                        });\r\n                    }\r\n                    return acc;\r\n                }, []);\r\n\r\n                setProducts(responseData);\r\n                setGroupedProducts(grouped);\r\n                setOpenDialog(true);\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fetching products:', error);\r\n        }\r\n    };\r\n\r\n    // Function to handle tracking submit\r\n    const handleTrackingSubmit = async () => {\r\n        try {\r\n            // Make API call to update tracking\r\n            const response = await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/push_tracking`,\r\n                {\r\n                    channelOrderId: channelOrderId,\r\n                    originalChannelOrderId: originalChannelOrderId,\r\n                    channelCode: channelCode,\r\n                    skuEan: skuEan,\r\n                    skuCode: skuCode,\r\n                    tracking: tracking,\r\n                    shipmentDate: shipmentDate,\r\n                    carrier: carrier,\r\n                    shipUrl: shipUrl,\r\n                    isReturn: isReturn\r\n                }\r\n            );\r\n            // Handle success response\r\n            console.log(response.data);\r\n        } catch (error) {\r\n            // Handle error\r\n            console.error('Error updating tracking:', error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleSaveSelectedProducts = () => {\r\n        setSelectedProducts(selectedProducts);\r\n\r\n        // You can save the selected products to a state or perform any other required action here\r\n        // For example, you can update a state with the selected products\r\n        const selectedProductsData = selectedProducts.map(productId => {\r\n            return products.find(product => product.id === productId);\r\n        });\r\n        // Do something with the selected products data, for example:\r\n        console.log('Selected products:', selectedProductsData);\r\n\r\n        setOpenDialog(false);\r\n    };\r\n\r\n    const handleSaveCustomerDetails = () => {\r\n        // Save the entered customer and shipping details\r\n        const newCustomerAndShippingDetails = {\r\n            first_name: formData.first_name,\r\n            last_name: formData.last_name,\r\n            email: formData.email,\r\n            phone: formData.phone,\r\n            address1: formData.address1,\r\n            city: formData.city,\r\n            province: formData.province,\r\n            country: formData.country,\r\n            zip: formData.zip,\r\n        };\r\n\r\n        // Set the customer and shipping details\r\n        setCustomerAndShippingDetails(newCustomerAndShippingDetails);\r\n\r\n        // Close the customer dialog\r\n        setOpenCustomerDialog(false);\r\n    };\r\n\r\n    const handleSave = async () => {\r\n        try {\r\n            // Prepare the data for the request\r\n            const requestData = {\r\n                line_items: selectedProducts.map(productId => {\r\n                    const product = products.find(product => product.id === productId);\r\n                    return {\r\n                        variant_id: product.id,\r\n                        quantity: product.quantity || 1, // Assuming quantity property\r\n                    };\r\n                }),\r\n                customer: {\r\n                    first_name: customerAndShippingDetails.first_name,\r\n                    last_name: customerAndShippingDetails.last_name,\r\n                    email: customerAndShippingDetails.email,\r\n                },\r\n                billing_address: {\r\n                    first_name: customerAndShippingDetails.first_name,\r\n                    last_name: customerAndShippingDetails.last_name,\r\n                    address1: customerAndShippingDetails.address1,\r\n                    city: customerAndShippingDetails.city,\r\n                    province: customerAndShippingDetails.province,\r\n                    country: customerAndShippingDetails.country,\r\n                    zip: customerAndShippingDetails.zip,\r\n                    phone: customerAndShippingDetails.phone,\r\n                },\r\n                shipping_address: {\r\n                    first_name: formData.shipping_first_name,\r\n                    last_name: formData.shipping_last_name,\r\n                    address1: formData.address1,\r\n                    city: formData.city,\r\n                    province: formData.province,\r\n                    country: formData.country,\r\n                    zip: formData.zip,\r\n                    phone: formData.shipping_phone,\r\n                },\r\n                email: customerAndShippingDetails.email,\r\n                transactions: [\r\n                    {\r\n                        kind: \"sale\", // Assuming the transaction kind\r\n                        status: \"success\", // Assuming the transaction status\r\n                        amount: totalPayment, // Assuming the total payment amount\r\n                    }\r\n                ],\r\n                financial_status: paymentStatus,\r\n            };\r\n\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n\r\n            // Make the POST request\r\n            const res = await axios({\r\n                url: `${process.env.REACT_APP_BASE_URL}/common/api/create_shopify_order/?fby_user_id=${storedGroupCode}`,\r\n                method: \"POST\",\r\n                headers: {\r\n                    \"Content-Type\": \"application/json\",\r\n                    Authorization: process.env.REACT_APP_ACCESS_TOKEN\r\n                },\r\n                data: requestData,\r\n            });\r\n\r\n            console.log(res);\r\n            if (res.data.success) {\r\n                // Handle success\r\n            }\r\n        } catch (error) {\r\n            console.error('Error saving Order:', error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleAddCustomerAndShipping = () => {\r\n        setOpenCustomerDialog(true);\r\n    };\r\n\r\n    const handleCloseDialog = () => {\r\n        setOpenDialog(false);\r\n    };\r\n\r\n    const handleCustomerCloseDialog = () => {\r\n        setOpenCustomerDialog(false);\r\n    };\r\n    const handlePaymentClick = (event) => {\r\n        setAnchorEl(event.currentTarget);\r\n    };\r\n\r\n    const handlePaymentClose = () => {\r\n        setAnchorEl(null);\r\n    };\r\n\r\n    const handlePaymentMethod = (method) => {\r\n        setPaymentMethod(method);\r\n        setAnchorEl(null);\r\n        if (method === 'Mark as Paid') {\r\n            setPaymentStatus('Paid');\r\n        }\r\n    };\r\n\r\n    const handleLineItemClick = (lineItemId) => {\r\n        navigate(`/orderMaster/${orderNo}/orderDetails/${lineItemId}`);\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        // Calculate total payment with quantities\r\n        let totalPrice = 0;\r\n        for (const product of selectedProducts) {\r\n            const quantity = productQuantities[product.id] || 1;\r\n            const price = product.item_total_price || product.price || 0;\r\n            totalPrice += price * quantity;\r\n        }\r\n        setTotalPayment(totalPrice);\r\n    }, [selectedProducts, productQuantities]);\r\n\r\n    const handleBack = () => {\r\n        navigate(-1);\r\n    };\r\n\r\n    const handleButtonClick = () => {\r\n        setOpenTrackingDialog(true);\r\n    };\r\n\r\n    // Function to handle tracking dialog close\r\n    const handleTrackingDialogClose = () => {\r\n        setOpenTrackingDialog(false);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <NavBar selectedSidebarItem=\"products\" />\r\n            <Sidebar />\r\n            <Grid container spacing={3} style={{ marginTop: '45px', marginLeft: '255px' }}>\r\n                <Grid item xs={12} md={6}>\r\n                    <Typography variant=\"h5\" gutterBottom style={{ display: 'flex', alignItems: 'center' }}>\r\n                        <ArrowBackIcon onClick={handleBack} />\r\n                        <span style={{ fontSize: '1.2rem', marginLeft: 8 }}>Order</span>\r\n                        <Button variant=\"contained\" color=\"primary\" style={{ marginLeft: 'auto' }} onClick={handleSave}>Save</Button>\r\n                    </Typography>\r\n                    <Grid container spacing={3}>\r\n                        <Grid item xs={12}>\r\n                            {(!orderNo) && (\r\n                                <Typography variant=\"h5\" component=\"h4\" style={{ marginBottom: 10 }}>Product</Typography>\r\n                            )}\r\n                            <div style={{ display: 'flex' }}>\r\n                                {(!orderNo) && (\r\n                                    <>\r\n                                        <TextField\r\n                                            label=\"Search Product\"\r\n                                            variant=\"outlined\"\r\n                                            fullWidth\r\n                                            size=\"small\"\r\n                                            InputProps={{\r\n                                                endAdornment: (\r\n                                                    <IconButton color=\"primary\" aria-label=\"search\" size=\"small\">\r\n                                                        <SearchIcon />\r\n                                                    </IconButton>\r\n                                                ),\r\n                                            }}\r\n                                        />\r\n                                        <Button variant=\"outlined\" style={{ marginLeft: 10, fontSize: '0.8rem' }} size=\"small\" onClick={handleBrowse}>Browse</Button>\r\n                                    </>\r\n                                )}\r\n                            </div>\r\n                        </Grid>\r\n                        {(selectedProducts.length > 0) && (\r\n                            <Grid item xs={12}>\r\n                                <TableContainer component={Paper}>\r\n                                    <Table>\r\n                                        <TableHead sx={{ background: '#f5f5f5' }}>\r\n                                            <TableRow>\r\n                                                <TableCell>Image</TableCell>\r\n                                                <TableCell>Title</TableCell>\r\n                                                <TableCell>Quantity</TableCell>\r\n                                                <TableCell>Price</TableCell>\r\n                                                {(orderNo) && (\r\n                                                    <TableCell>Line Order Id</TableCell>\r\n                                                )}\r\n                                            </TableRow>\r\n                                        </TableHead>\r\n                                        <TableBody>\r\n                                            {selectedProducts.map(newproduct => {\r\n                                                const productQuantity = productQuantities[newproduct.id] || 1;\r\n                                                return (\r\n                                                    <TableRow key={newproduct.id}>\r\n                                                        <TableCell><img src={(newproduct.image || '')} alt={newproduct.title} style={{ maxWidth: '100px' }} /></TableCell>\r\n                                                        <TableCell>\r\n                                                            <Typography variant=\"subtitle2\">{newproduct.title || ''}</Typography>\r\n                                                            {(newproduct.option_1_value || newproduct.option_2_value) && (\r\n                                                                <Typography variant=\"caption\" color=\"textSecondary\">\r\n                                                                    {newproduct.option_1_value} {newproduct.option_2_value && `/ ${newproduct.option_2_value}`}\r\n                                                                </Typography>\r\n                                                            )}\r\n                                                            <Typography variant=\"caption\" display=\"block\" color=\"textSecondary\">\r\n                                                                SKU: {newproduct.sku}\r\n                                                            </Typography>\r\n                                                        </TableCell>\r\n                                                        <TableCell>\r\n                                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                                                <IconButton\r\n                                                                    size=\"small\"\r\n                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.max(1, productQuantity - 1))}\r\n                                                                    disabled={productQuantity <= 1}\r\n                                                                >\r\n                                                                    <RemoveIcon />\r\n                                                                </IconButton>\r\n                                                                <TextField\r\n                                                                    size=\"small\"\r\n                                                                    type=\"number\"\r\n                                                                    value={productQuantity}\r\n                                                                    onChange={(e) => handleQuantityChange(newproduct.id, Math.max(1, parseInt(e.target.value) || 1))}\r\n                                                                    inputProps={{ min: 1, max: newproduct.quantity || 999, style: { textAlign: 'center', width: '60px' } }}\r\n                                                                />\r\n                                                                <IconButton\r\n                                                                    size=\"small\"\r\n                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.min((newproduct.quantity || 999), productQuantity + 1))}\r\n                                                                    disabled={productQuantity >= (newproduct.quantity || 999)}\r\n                                                                >\r\n                                                                    <AddIcon />\r\n                                                                </IconButton>\r\n                                                            </Box>\r\n                                                            <Typography variant=\"caption\" color=\"textSecondary\">\r\n                                                                Available: {newproduct.quantity || 0}\r\n                                                            </Typography>\r\n                                                        </TableCell>\r\n                                                        <TableCell>${(newproduct.price || 0) * productQuantity}</TableCell>\r\n                                                        {orderNo &&\r\n                                                            (<TableCell>\r\n                                                                <Button onClick={() => handleLineItemClick(newproduct.line_item_id)}>{newproduct.line_item_id}</Button>\r\n                                                            </TableCell>)\r\n                                                        }\r\n                                                    </TableRow>\r\n                                                );\r\n                                            })}\r\n                                        </TableBody>\r\n\r\n                                    </Table>\r\n                                </TableContainer>\r\n                            </Grid>\r\n                        )}\r\n\r\n                    </Grid>\r\n                    {/* Payment Information Card */}\r\n                    <Grid container spacing={3} style={{ marginTop: '20px', marginLeft: '5px' }}>\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h6\" component=\"h4\">Payment</Typography>\r\n                                <TableContainer component={Paper}>\r\n                                    <Table>\r\n                                        <TableBody>\r\n                                            <TableRow>\r\n                                                <TableCell>Payment Status:</TableCell>\r\n                                                <TableCell>{paymentStatus}</TableCell>\r\n                                            </TableRow>\r\n                                            <TableRow>\r\n                                                {orderStatus !== 'unfulfilled' && orderStatus !== 'partially fulfilled' ? (\r\n                                                    <>\r\n                                                        <TableCell>\r\n                                                            <Button variant=\"outlined\" color=\"primary\" onClick={handlePaymentClick}>\r\n                                                                Collect Payment\r\n                                                            </Button>\r\n                                                            <Menu\r\n                                                                anchorEl={anchorEl}\r\n                                                                open={Boolean(anchorEl)}\r\n                                                                onClose={handlePaymentClose}\r\n                                                            >\r\n                                                                <MenuItem onClick={() => handlePaymentMethod('Enter Credit Card')}>Enter Credit Card</MenuItem>\r\n                                                                <MenuItem onClick={() => handlePaymentMethod('Mark as Paid')}>Mark as Paid</MenuItem>\r\n                                                            </Menu>\r\n                                                        </TableCell>\r\n                                                        <TableCell>\r\n                                                            <Button variant=\"outlined\" color=\"primary\">\r\n                                                                Send Invoice\r\n                                                            </Button>\r\n                                                        </TableCell>\r\n                                                    </>\r\n                                                ) : (\r\n                                                    <TableCell>\r\n                                                        <Button variant=\"outlined\" color=\"primary\" onClick={handleButtonClick}>\r\n                                                            Add Tracking\r\n                                                        </Button>\r\n                                                    </TableCell>\r\n                                                )}\r\n                                            </TableRow>\r\n\r\n                                        </TableBody>\r\n                                    </Table>\r\n                                </TableContainer>\r\n                            </CardContent>\r\n                        </Card>\r\n                    </Grid>\r\n                </Grid>\r\n                <Grid item xs={12} md={3} style={{ marginTop: '80px' }}>\r\n                    {customerAndShippingDetails ? (\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h5\" component=\"h2\" style={{ fontSize: '1.2rem' }}>Customer and Shipping Details:</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Name: {customerAndShippingDetails.first_name}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Email: {customerAndShippingDetails.email}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Phone: {customerAndShippingDetails.phone}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Address: {customerAndShippingDetails.address1}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>City: {customerAndShippingDetails.city}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Province: {customerAndShippingDetails.province}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>Country: {customerAndShippingDetails.country}</Typography>\r\n                                <Typography variant=\"body1\" gutterBottom>ZIP: {customerAndShippingDetails.zip}</Typography>\r\n                            </CardContent>\r\n                        </Card>\r\n                    ) : (\r\n                        <Card>\r\n                            <CardContent>\r\n                                <Typography variant=\"h5\" component=\"h2\" style={{ fontSize: '0.8rem' }}><h5>Add Customer and Shipping Details</h5>\r\n                                    <Button variant=\"contained\" color=\"primary\" onClick={handleAddCustomerAndShipping} style={{ fontSize: '0.8rem', marginLeft: '5px' }}>Add</Button>\r\n                                </Typography>\r\n                            </CardContent>\r\n                        </Card>\r\n                    )}\r\n                </Grid>\r\n                <Dialog open={openCustomerDialog} onClose={handleCustomerCloseDialog}>\r\n                    <DialogTitle>Customer and Shipping Details</DialogTitle>\r\n                    <DialogContent>\r\n                        <Grid container spacing={2}>\r\n                            <Grid item xs={12} sm={6}>\r\n                                <Typography variant=\"h6\" gutterBottom>Customer Data</Typography>\r\n                                <TextField\r\n                                    label=\"First Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.first_name}\r\n                                    name=\"first_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Last Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.last_name}\r\n                                    name=\"last_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Email\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.email}\r\n                                    name=\"email\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Phone\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.phone}\r\n                                    name=\"phone\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                {/* Add more input fields for customer data */}\r\n                            </Grid>\r\n                            <Grid item xs={12} sm={6}>\r\n                                <Typography variant=\"h6\" gutterBottom>Shipping Address</Typography>\r\n                                <TextField\r\n                                    label=\"First Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_first_name}\r\n                                    name=\"shipping_first_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Last Name\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_last_name}\r\n                                    name=\"shipping_last_name\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Address\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.address1}\r\n                                    name=\"address1\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Phone\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.shipping_phone}\r\n                                    name=\"shipping_phone\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"City\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.city}\r\n                                    name=\"city\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Province\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.province}\r\n                                    name=\"province\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"Country\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.country}\r\n                                    name=\"country\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                <TextField\r\n                                    label=\"ZIP\"\r\n                                    variant=\"outlined\"\r\n                                    fullWidth\r\n                                    size=\"small\"\r\n                                    value={formData.zip}\r\n                                    name=\"zip\"\r\n                                    onChange={handleChange}\r\n                                    style={{ marginBottom: '16px' }}\r\n                                />\r\n                                {/* Add more input fields for shipping address */}\r\n                            </Grid>\r\n                        </Grid>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleSaveCustomerDetails} color=\"primary\">Save</Button>\r\n                        <Button onClick={handleCustomerCloseDialog} color=\"primary\">Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n\r\n                <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\r\n                    <DialogTitle sx={{ fontSize: '26px' }}>Select Products</DialogTitle>\r\n                    <DialogContent>\r\n                        <TableContainer component={Paper}>\r\n                            <Table>\r\n                                <TableHead sx={{ background: '#f5f5f5' }}>\r\n                                    <TableRow>\r\n                                        <TableCell>Image</TableCell>\r\n                                        <TableCell>Product</TableCell>\r\n                                        <TableCell>Variants</TableCell>\r\n                                        <TableCell>Available Stock</TableCell>\r\n                                        <TableCell>Price</TableCell>\r\n                                        <TableCell>Select</TableCell>\r\n                                    </TableRow>\r\n                                </TableHead>\r\n                                <TableBody>\r\n                                    {groupedProducts.map((productGroup, groupIndex) => (\r\n                                        <TableRow key={`group-${groupIndex}`}>\r\n                                            <TableCell>\r\n                                                <img src={productGroup.image || ''} alt={productGroup.title} style={{ maxWidth: '80px', height: '80px', objectFit: 'cover' }} />\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Typography variant=\"subtitle1\" fontWeight=\"bold\">{productGroup.title}</Typography>\r\n                                                <Typography variant=\"body2\" color=\"textSecondary\">{productGroup.body_html}</Typography>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant, variantIndex) => (\r\n                                                        <Box key={variant.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                                            <Chip\r\n                                                                label={`${variant.option_1_value || 'Default'} ${variant.option_2_value ? '/ ' + variant.option_2_value : ''}`}\r\n                                                                size=\"small\"\r\n                                                                variant=\"outlined\"\r\n                                                            />\r\n                                                            <Typography variant=\"caption\">SKU: {variant.sku}</Typography>\r\n                                                        </Box>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <Typography key={variant.id} variant=\"body2\">\r\n                                                            {variant.quantity || 0} units\r\n                                                        </Typography>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <Typography key={variant.id} variant=\"body2\">\r\n                                                            ${variant.price || 0}\r\n                                                        </Typography>\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                            <TableCell>\r\n                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\r\n                                                    {productGroup.variants.map((variant) => (\r\n                                                        <input\r\n                                                            key={variant.id}\r\n                                                            type=\"checkbox\"\r\n                                                            checked={selectedProducts.some(item => item.id === variant.id)}\r\n                                                            onChange={() => handleCheckboxChange(variant)}\r\n                                                        />\r\n                                                    ))}\r\n                                                </Box>\r\n                                            </TableCell>\r\n                                        </TableRow>\r\n                                    ))}\r\n                                </TableBody>\r\n                            </Table>\r\n                        </TableContainer>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleSaveSelectedProducts} color=\"primary\">Save</Button>\r\n                        <Button onClick={handleCloseDialog} color=\"primary\">Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n                <Dialog open={openTrackingDialog} onClose={handleTrackingDialogClose}>\r\n                    <DialogTitle>Add Tracking</DialogTitle>\r\n                    <DialogContent>\r\n                        <TextField\r\n                            label=\"Channel Order Id\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={channelOrderId}\r\n                            onChange={(e) => setChannelOrderId(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Original Channel Order Id\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={originalChannelOrderId}\r\n                            onChange={(e) => setOriginalChannelOrderId(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Channel Code\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={channelCode}\r\n                            onChange={(e) => setChannelCode(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"SKU EAN\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={skuEan}\r\n                            onChange={(e) => setSkuEan(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"SKU Code\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={skuCode}\r\n                            onChange={(e) => setSkuCode(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Tracking\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={tracking}\r\n                            onChange={(e) => setTracking(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Shipment Date\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={shipmentDate}\r\n                            onChange={(e) => setShipmentDate(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Carrier\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={carrier}\r\n                            onChange={(e) => setCarrier(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Ship URL\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={shipUrl}\r\n                            onChange={(e) => setShipUrl(e.target.value)}\r\n                        />\r\n                        <TextField\r\n                            label=\"Is Return\"\r\n                            variant=\"outlined\"\r\n                            fullWidth\r\n                            size=\"small\"\r\n                            value={isReturn}\r\n                            onChange={(e) => setIsReturn(e.target.value)}\r\n                        />\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={handleTrackingSubmit} color=\"primary\">Submit</Button>\r\n                        <Button onClick={handleTrackingDialogClose} color=\"primary\">Cancel</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n            </Grid>\r\n        </>\r\n    );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AAC1R,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,OAAO,QAAQ,uCAAuC;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAQ,CAAC,GAAGT,SAAS,CAAC,CAAC;EAC/B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC;IACrCsD,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,GAAG,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAClF,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,WAAW,CAAC;EAC3D,MAAM,CAACiF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACuF,WAAW,EAAEC,cAAc,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyF,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2F,OAAO,EAAEC,UAAU,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiG,OAAO,EAAEC,UAAU,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmG,OAAO,EAAEC,UAAU,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACZ,MAAMsG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA,IAAI5D,OAAO,EAAE;UACT;UACA,IAAI6D,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;UACvD,MAAMC,UAAU,GAAG,MAAM5E,KAAK,CAAC6E,IAAI,CAC/B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,4CAA4CP,eAAe,EAAE,EAC9F;YAAEQ,QAAQ,EAAErE;UAAQ,CAAC,EACrB;YACIsE,OAAO,EAAE;cACT,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;YAC/D;UACA,CACJ,CAAC;UACD,IAAIR,UAAU,CAACS,IAAI,CAACC,OAAO,EAAE;YACzBrD,eAAe,CAAC2C,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAAC;YAC7C,MAAMrD,YAAY,GAAG4C,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;cAC9DC,EAAE,EAAED,OAAO,CAACC,EAAE;cACdC,KAAK,EAAEF,OAAO,CAACG,YAAY;cAC3BC,KAAK,EAAEJ,OAAO,CAACI,KAAK;cAAE;cACtBC,KAAK,EAAEL,OAAO,CAACM,gBAAgB;cAC/BC,QAAQ,EAAEP,OAAO,CAACQ,kBAAkB;cACpCC,SAAS,EAAET,OAAO,CAACU,QAAQ;cAC3BC,YAAY,EAAEX,OAAO,CAACY,kBAAkB;cACxCC,YAAY,EAAEb,OAAO,CAACa,YAAY;cAClCC,cAAc,EAAEd,OAAO,CAACc;YAC5B,CAAC,CAAC,CAAC;YACH,IAAItE,YAAY,CAACuE,MAAM,GAAG,CAAC,EAAE;cACzBrF,mBAAmB,CAACc,YAAY,CAACuD,GAAG,CAACiB,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC;cACrDvD,cAAc,CAACjB,YAAY,CAAC,CAAC,CAAC,CAACqE,YAAY,CAAC;cAC5ClE,gBAAgB,CAACH,YAAY,CAAC,CAAC,CAAC,CAACsE,cAAc,CAAC;YACpD;YACA,MAAMG,QAAQ,GAAG7B,UAAU,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;cAC1DjE,UAAU,EAAEiE,OAAO,CAACkB,cAAc;cAClClF,SAAS,EAAE,EAAE;cACbC,KAAK,EAAE+D,OAAO,CAACmB,WAAW;cAC1BjF,KAAK,EAAE8D,OAAO,CAACoB,iBAAiB;cAChCjF,QAAQ,EAAE6D,OAAO,CAACqB,cAAc;cAChCjF,IAAI,EAAE4D,OAAO,CAACsB,SAAS;cACvBjF,QAAQ,EAAE2D,OAAO,CAACuB,eAAe;cACjCjF,OAAO,EAAE0D,OAAO,CAACwB,YAAY;cAC7BjF,GAAG,EAAEyD,OAAO,CAACyB;YACjB,CAAC,CAAC,CAAC;YACH;YACAlE,6BAA6B,CAAC0D,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC9C;QACJ;MACJ,CAAC,CAAC,OAAOS,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD;IACJ,CAAC;IAED1C,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,CAAC5D,OAAO,CAAC,CAAC;EAGb,MAAMwG,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClG,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACiG,IAAI,GAAGC;IAAM,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,oBAAoB,GAAIC,OAAO,IAAK;IACtCxG,mBAAmB,CAACyG,SAAS,IAAI;MAC7B,MAAMC,UAAU,GAAGD,SAAS,CAACE,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAKiC,OAAO,CAACjC,EAAE,CAAC;MACjE,IAAI,CAACmC,UAAU,EAAE;QACb;QACAxG,oBAAoB,CAAC0G,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP,CAACJ,OAAO,CAACjC,EAAE,GAAG;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,GAAGkC,SAAS,EAAED,OAAO,CAAC;MAClC,CAAC,MAAM;QACH;QACAtG,oBAAoB,CAAC0G,IAAI,IAAI;UACzB,MAAMC,aAAa,GAAG;YAAE,GAAGD;UAAK,CAAC;UACjC,OAAOC,aAAa,CAACL,OAAO,CAACjC,EAAE,CAAC;UAChC,OAAOsC,aAAa;QACxB,CAAC,CAAC;QACF,OAAOJ,SAAS,CAACK,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAKiC,OAAO,CAACjC,EAAE,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAMwC,oBAAoB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;IACrD/G,oBAAoB,CAAC0G,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACI,SAAS,GAAGC;IACjB,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,IAAI3D,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAEvD,MAAM0D,GAAG,GAAG,MAAMrI,KAAK,CAAC6E,IAAI,CACxB,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,mDAAmDP,eAAe,EAAE,EACrG,CAAC,CAAC,EACF;QACIS,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC/D;MACJ,CACJ,CAAC;MACD,IAAIiD,GAAG,CAAChD,IAAI,CAACC,OAAO,CAACD,IAAI,CAACkB,MAAM,GAAG,CAAC,EAAE;QAClC,MAAMvE,YAAY,GAAGqG,GAAG,CAAChD,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,GAAG,CAACC,OAAO,KAAK;UACvDC,EAAE,EAAED,OAAO,CAACC,EAAE;UACdC,KAAK,EAAEF,OAAO,CAACE,KAAK;UACpB4C,SAAS,EAAE9C,OAAO,CAAC+C,WAAW;UAC9B3C,KAAK,EAAEJ,OAAO,CAACI,KAAK;UACpBC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBE,QAAQ,EAAEP,OAAO,CAACgD,kBAAkB;UACpCC,GAAG,EAAEjD,OAAO,CAACiD,GAAG;UAChBC,cAAc,EAAElD,OAAO,CAACkD,cAAc,IAAI,EAAE;UAC5CC,cAAc,EAAEnD,OAAO,CAACmD,cAAc,IAAI;QAC9C,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,OAAO,GAAG5G,YAAY,CAAC6G,MAAM,CAAC,CAACC,GAAG,EAAEpB,OAAO,KAAK;UAClD,MAAMqB,aAAa,GAAGD,GAAG,CAACE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACvD,KAAK,KAAKgC,OAAO,CAAChC,KAAK,CAAC;UACtE,IAAIqD,aAAa,EAAE;YACfA,aAAa,CAACG,QAAQ,CAACC,IAAI,CAACzB,OAAO,CAAC;UACxC,CAAC,MAAM;YACHoB,GAAG,CAACK,IAAI,CAAC;cACLzD,KAAK,EAAEgC,OAAO,CAAChC,KAAK;cACpBE,KAAK,EAAE8B,OAAO,CAAC9B,KAAK;cACpB0C,SAAS,EAAEZ,OAAO,CAACY,SAAS;cAC5BY,QAAQ,EAAE,CAACxB,OAAO;YACtB,CAAC,CAAC;UACN;UACA,OAAOoB,GAAG;QACd,CAAC,EAAE,EAAE,CAAC;QAENhI,WAAW,CAACkB,YAAY,CAAC;QACzBhB,kBAAkB,CAAC4H,OAAO,CAAC;QAC3BrG,aAAa,CAAC,IAAI,CAAC;MACvB;IACJ,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAMkC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA;MACA,MAAMC,QAAQ,GAAG,MAAMrJ,KAAK,CAAC6E,IAAI,CAC7B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,2BAA2B,EAC5D;QACI5B,cAAc,EAAEA,cAAc;QAC9BE,sBAAsB,EAAEA,sBAAsB;QAC9CE,WAAW,EAAEA,WAAW;QACxBE,MAAM,EAAEA,MAAM;QACdE,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAEA,QAAQ;QAClBE,YAAY,EAAEA,YAAY;QAC1BE,OAAO,EAAEA,OAAO;QAChBE,OAAO,EAAEA,OAAO;QAChBE,QAAQ,EAAEA;MACd,CACJ,CAAC;MACD;MACA6C,OAAO,CAACmC,GAAG,CAACD,QAAQ,CAAChE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACZ;MACAC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;EAGD,MAAMqC,0BAA0B,GAAGA,CAAA,KAAM;IACrCrI,mBAAmB,CAACD,gBAAgB,CAAC;;IAErC;IACA;IACA,MAAMuI,oBAAoB,GAAGvI,gBAAgB,CAACsE,GAAG,CAAC2C,SAAS,IAAI;MAC3D,OAAOrH,QAAQ,CAACmI,IAAI,CAACtB,OAAO,IAAIA,OAAO,CAACjC,EAAE,KAAKyC,SAAS,CAAC;IAC7D,CAAC,CAAC;IACF;IACAf,OAAO,CAACmC,GAAG,CAAC,oBAAoB,EAAEE,oBAAoB,CAAC;IAEvDjH,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMkH,yBAAyB,GAAGA,CAAA,KAAM;IACpC;IACA,MAAMC,6BAA6B,GAAG;MAClCnI,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;MAC7BC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;MACrBC,KAAK,EAAEL,QAAQ,CAACK,KAAK;MACrBC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;MAC3BC,IAAI,EAAEP,QAAQ,CAACO,IAAI;MACnBC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;MAC3BC,OAAO,EAAET,QAAQ,CAACS,OAAO;MACzBC,GAAG,EAAEV,QAAQ,CAACU;IAClB,CAAC;;IAED;IACAgB,6BAA6B,CAAC2G,6BAA6B,CAAC;;IAE5D;IACAjH,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMkH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA;MACA,MAAMC,WAAW,GAAG;QAChBC,UAAU,EAAE5I,gBAAgB,CAACsE,GAAG,CAAC2C,SAAS,IAAI;UAC1C,MAAMR,OAAO,GAAG7G,QAAQ,CAACmI,IAAI,CAACtB,OAAO,IAAIA,OAAO,CAACjC,EAAE,KAAKyC,SAAS,CAAC;UAClE,OAAO;YACH4B,UAAU,EAAEpC,OAAO,CAACjC,EAAE;YACtBM,QAAQ,EAAE2B,OAAO,CAAC3B,QAAQ,IAAI,CAAC,CAAE;UACrC,CAAC;QACL,CAAC,CAAC;QACFgE,QAAQ,EAAE;UACNxI,UAAU,EAAEuB,0BAA0B,CAACvB,UAAU;UACjDC,SAAS,EAAEsB,0BAA0B,CAACtB,SAAS;UAC/CC,KAAK,EAAEqB,0BAA0B,CAACrB;QACtC,CAAC;QACDuI,eAAe,EAAE;UACbzI,UAAU,EAAEuB,0BAA0B,CAACvB,UAAU;UACjDC,SAAS,EAAEsB,0BAA0B,CAACtB,SAAS;UAC/CG,QAAQ,EAAEmB,0BAA0B,CAACnB,QAAQ;UAC7CC,IAAI,EAAEkB,0BAA0B,CAAClB,IAAI;UACrCC,QAAQ,EAAEiB,0BAA0B,CAACjB,QAAQ;UAC7CC,OAAO,EAAEgB,0BAA0B,CAAChB,OAAO;UAC3CC,GAAG,EAAEe,0BAA0B,CAACf,GAAG;UACnCL,KAAK,EAAEoB,0BAA0B,CAACpB;QACtC,CAAC;QACDuI,gBAAgB,EAAE;UACd1I,UAAU,EAAEF,QAAQ,CAAC6I,mBAAmB;UACxC1I,SAAS,EAAEH,QAAQ,CAAC8I,kBAAkB;UACtCxI,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;UAC3BC,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;UAC3BC,OAAO,EAAET,QAAQ,CAACS,OAAO;UACzBC,GAAG,EAAEV,QAAQ,CAACU,GAAG;UACjBL,KAAK,EAAEL,QAAQ,CAAC+I;QACpB,CAAC;QACD3I,KAAK,EAAEqB,0BAA0B,CAACrB,KAAK;QACvC4I,YAAY,EAAE,CACV;UACIC,IAAI,EAAE,MAAM;UAAE;UACdC,MAAM,EAAE,SAAS;UAAE;UACnBC,MAAM,EAAEpI,YAAY,CAAE;QAC1B,CAAC,CACJ;QACDqI,gBAAgB,EAAEvI;MACtB,CAAC;MAED,IAAIuC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;;MAEvD;MACA,MAAM0D,GAAG,GAAG,MAAMrI,KAAK,CAAC;QACpB0K,GAAG,EAAE,GAAG5F,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDP,eAAe,EAAE;QACxGkG,MAAM,EAAE,MAAM;QACdzF,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAEL,OAAO,CAACC,GAAG,CAACK;QAC/B,CAAC;QACDC,IAAI,EAAEuE;MACV,CAAC,CAAC;MAEFzC,OAAO,CAACmC,GAAG,CAACjB,GAAG,CAAC;MAChB,IAAIA,GAAG,CAAChD,IAAI,CAACC,OAAO,EAAE;QAClB;MAAA;IAER,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC/C;EACJ,CAAC;EAGD,MAAM0D,4BAA4B,GAAGA,CAAA,KAAM;IACvCnI,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMoI,iBAAiB,GAAGA,CAAA,KAAM;IAC5BtI,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMuI,yBAAyB,GAAGA,CAAA,KAAM;IACpCrI,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EACD,MAAMsI,kBAAkB,GAAIC,KAAK,IAAK;IAClCrI,WAAW,CAACqI,KAAK,CAACC,aAAa,CAAC;EACpC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7BvI,WAAW,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMwI,mBAAmB,GAAIR,MAAM,IAAK;IACpC9H,gBAAgB,CAAC8H,MAAM,CAAC;IACxBhI,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIgI,MAAM,KAAK,cAAc,EAAE;MAC3BxI,gBAAgB,CAAC,MAAM,CAAC;IAC5B;EACJ,CAAC;EAED,MAAMiJ,mBAAmB,GAAIC,UAAU,IAAK;IACxC1K,QAAQ,CAAC,gBAAgBC,OAAO,iBAAiByK,UAAU,EAAE,CAAC;EAClE,CAAC;EAGDnN,SAAS,CAAC,MAAM;IACZ;IACA,IAAIoN,UAAU,GAAG,CAAC;IAClB,KAAK,MAAM5D,OAAO,IAAIzG,gBAAgB,EAAE;MACpC,MAAM8E,QAAQ,GAAG5E,iBAAiB,CAACuG,OAAO,CAACjC,EAAE,CAAC,IAAI,CAAC;MACnD,MAAMI,KAAK,GAAG6B,OAAO,CAAC5B,gBAAgB,IAAI4B,OAAO,CAAC7B,KAAK,IAAI,CAAC;MAC5DyF,UAAU,IAAIzF,KAAK,GAAGE,QAAQ;IAClC;IACA1D,eAAe,CAACiJ,UAAU,CAAC;EAC/B,CAAC,EAAE,CAACrK,gBAAgB,EAAEE,iBAAiB,CAAC,CAAC;EAEzC,MAAMoK,UAAU,GAAGA,CAAA,KAAM;IACrB5K,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAM6K,iBAAiB,GAAGA,CAAA,KAAM;IAC5BrI,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMsI,yBAAyB,GAAGA,CAAA,KAAM;IACpCtI,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,oBACI7C,OAAA,CAAAE,SAAA;IAAAkL,QAAA,gBACIpL,OAAA,CAACL,MAAM;MAAC0L,mBAAmB,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCzL,OAAA,CAACJ,OAAO;MAAA0L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXzL,OAAA,CAAChC,IAAI;MAAC0N,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAV,QAAA,gBAC1EpL,OAAA,CAAChC,IAAI;QAACkI,IAAI;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,gBACrBpL,OAAA,CAACjC,UAAU;UAACkO,OAAO,EAAC,IAAI;UAACC,YAAY;UAACN,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAhB,QAAA,gBACnFpL,OAAA,CAACV,aAAa;YAAC+M,OAAO,EAAEpB;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCzL,OAAA;YAAM4L,KAAK,EAAE;cAAEU,QAAQ,EAAE,QAAQ;cAAER,UAAU,EAAE;YAAE,CAAE;YAAAV,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChEzL,OAAA,CAAC9B,MAAM;YAAC+N,OAAO,EAAC,WAAW;YAACM,KAAK,EAAC,SAAS;YAACX,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAACO,OAAO,EAAEhD,UAAW;YAAA+B,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC,eACbzL,OAAA,CAAChC,IAAI;UAAC0N,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACvBpL,OAAA,CAAChC,IAAI;YAACkI,IAAI;YAAC6F,EAAE,EAAE,EAAG;YAAAX,QAAA,GACZ,CAAC9K,OAAO,iBACNN,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,IAAI;cAACO,SAAS,EAAC,IAAI;cAACZ,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAG,CAAE;cAAArB,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC3F,eACDzL,OAAA;cAAK4L,KAAK,EAAE;gBAAEO,OAAO,EAAE;cAAO,CAAE;cAAAf,QAAA,EAC1B,CAAC9K,OAAO,iBACNN,OAAA,CAAAE,SAAA;gBAAAkL,QAAA,gBACIpL,OAAA,CAAC/B,SAAS;kBACNyO,KAAK,EAAC,gBAAgB;kBACtBT,OAAO,EAAC,UAAU;kBAClBU,SAAS;kBACTC,IAAI,EAAC,OAAO;kBACZC,UAAU,EAAE;oBACRC,YAAY,eACR9M,OAAA,CAAC7B,UAAU;sBAACoO,KAAK,EAAC,SAAS;sBAAC,cAAW,QAAQ;sBAACK,IAAI,EAAC,OAAO;sBAAAxB,QAAA,eACxDpL,OAAA,CAACT,UAAU;wBAAA+L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAEpB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFzL,OAAA,CAAC9B,MAAM;kBAAC+N,OAAO,EAAC,UAAU;kBAACL,KAAK,EAAE;oBAAEE,UAAU,EAAE,EAAE;oBAAEQ,QAAQ,EAAE;kBAAS,CAAE;kBAACM,IAAI,EAAC,OAAO;kBAACP,OAAO,EAAEvE,YAAa;kBAAAsD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eAC/H;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL9K,gBAAgB,CAACsF,MAAM,GAAG,CAAC,iBACzBjG,OAAA,CAAChC,IAAI;YAACkI,IAAI;YAAC6F,EAAE,EAAE,EAAG;YAAAX,QAAA,eACdpL,OAAA,CAACzB,cAAc;cAACiO,SAAS,EAAE9N,KAAM;cAAA0M,QAAA,eAC7BpL,OAAA,CAAC5B,KAAK;gBAAAgN,QAAA,gBACFpL,OAAA,CAACxB,SAAS;kBAACuO,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAU,CAAE;kBAAA5B,QAAA,eACrCpL,OAAA,CAACvB,QAAQ;oBAAA2M,QAAA,gBACLpL,OAAA,CAAC1B,SAAS;sBAAA8M,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BzL,OAAA,CAAC1B,SAAS;sBAAA8M,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BzL,OAAA,CAAC1B,SAAS;sBAAA8M,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/BzL,OAAA,CAAC1B,SAAS;sBAAA8M,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,EAC1BnL,OAAO,iBACLN,OAAA,CAAC1B,SAAS;sBAAA8M,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CACtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZzL,OAAA,CAAC3B,SAAS;kBAAA+M,QAAA,EACLzK,gBAAgB,CAACsE,GAAG,CAACgI,UAAU,IAAI;oBAChC,MAAMC,eAAe,GAAGrM,iBAAiB,CAACoM,UAAU,CAAC9H,EAAE,CAAC,IAAI,CAAC;oBAC7D,oBACInF,OAAA,CAACvB,QAAQ;sBAAA2M,QAAA,gBACLpL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,eAACpL,OAAA;0BAAKmN,GAAG,EAAGF,UAAU,CAAC3H,KAAK,IAAI,EAAI;0BAAC8H,GAAG,EAAEH,UAAU,CAAC7H,KAAM;0BAACwG,KAAK,EAAE;4BAAEyB,QAAQ,EAAE;0BAAQ;wBAAE;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAClHzL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,gBACNpL,OAAA,CAACjC,UAAU;0BAACkO,OAAO,EAAC,WAAW;0BAAAb,QAAA,EAAE6B,UAAU,CAAC7H,KAAK,IAAI;wBAAE;0BAAAkG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,EACpE,CAACwB,UAAU,CAAC7E,cAAc,IAAI6E,UAAU,CAAC5E,cAAc,kBACpDrI,OAAA,CAACjC,UAAU;0BAACkO,OAAO,EAAC,SAAS;0BAACM,KAAK,EAAC,eAAe;0BAAAnB,QAAA,GAC9C6B,UAAU,CAAC7E,cAAc,EAAC,GAAC,EAAC6E,UAAU,CAAC5E,cAAc,IAAI,KAAK4E,UAAU,CAAC5E,cAAc,EAAE;wBAAA;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClF,CACf,eACDzL,OAAA,CAACjC,UAAU;0BAACkO,OAAO,EAAC,SAAS;0BAACE,OAAO,EAAC,OAAO;0BAACI,KAAK,EAAC,eAAe;0BAAAnB,QAAA,GAAC,OAC3D,EAAC6B,UAAU,CAAC9E,GAAG;wBAAA;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACZzL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,gBACNpL,OAAA,CAACX,GAAG;0BAAC0N,EAAE,EAAE;4BAAEZ,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEkB,GAAG,EAAE;0BAAE,CAAE;0BAAAlC,QAAA,gBACvDpL,OAAA,CAAC7B,UAAU;4BACPyO,IAAI,EAAC,OAAO;4BACZP,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAACsF,UAAU,CAAC9H,EAAE,EAAEoI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,eAAe,GAAG,CAAC,CAAC,CAAE;4BACrFO,QAAQ,EAAEP,eAAe,IAAI,CAAE;4BAAA9B,QAAA,eAE/BpL,OAAA,CAACP,UAAU;8BAAA6L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACbzL,OAAA,CAAC/B,SAAS;4BACN2O,IAAI,EAAC,OAAO;4BACZc,IAAI,EAAC,QAAQ;4BACbzG,KAAK,EAAEiG,eAAgB;4BACvBS,QAAQ,EAAG5G,CAAC,IAAKY,oBAAoB,CAACsF,UAAU,CAAC9H,EAAE,EAAEoI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEI,QAAQ,CAAC7G,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAE;4BACjG4G,UAAU,EAAE;8BAAEC,GAAG,EAAE,CAAC;8BAAEN,GAAG,EAAEP,UAAU,CAACxH,QAAQ,IAAI,GAAG;8BAAEmG,KAAK,EAAE;gCAAEmC,SAAS,EAAE,QAAQ;gCAAEC,KAAK,EAAE;8BAAO;4BAAE;0BAAE;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1G,CAAC,eACFzL,OAAA,CAAC7B,UAAU;4BACPyO,IAAI,EAAC,OAAO;4BACZP,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAACsF,UAAU,CAAC9H,EAAE,EAAEoI,IAAI,CAACO,GAAG,CAAEb,UAAU,CAACxH,QAAQ,IAAI,GAAG,EAAGyH,eAAe,GAAG,CAAC,CAAC,CAAE;4BAChHO,QAAQ,EAAEP,eAAe,KAAKD,UAAU,CAACxH,QAAQ,IAAI,GAAG,CAAE;4BAAA2F,QAAA,eAE1DpL,OAAA,CAACR,OAAO;8BAAA8L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNzL,OAAA,CAACjC,UAAU;0BAACkO,OAAO,EAAC,SAAS;0BAACM,KAAK,EAAC,eAAe;0BAAAnB,QAAA,GAAC,aACrC,EAAC6B,UAAU,CAACxH,QAAQ,IAAI,CAAC;wBAAA;0BAAA6F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACZzL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,GAAC,GAAC,EAAC,CAAC6B,UAAU,CAAC1H,KAAK,IAAI,CAAC,IAAI2H,eAAe;sBAAA;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,EAClEnL,OAAO,iBACHN,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,eACPpL,OAAA,CAAC9B,MAAM;0BAACmO,OAAO,EAAEA,CAAA,KAAMvB,mBAAmB,CAACmC,UAAU,CAACpH,YAAY,CAAE;0BAAAuF,QAAA,EAAE6B,UAAU,CAACpH;wBAAY;0BAAAyF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAE;oBAAA,GA7CNwB,UAAU,CAAC9H,EAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+ClB,CAAC;kBAEnB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,eAEPzL,OAAA,CAAChC,IAAI;UAAC0N,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,SAAS,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,eACxEpL,OAAA,CAACnC,IAAI;YAAAuN,QAAA,eACDpL,OAAA,CAAClC,WAAW;cAAAsN,QAAA,gBACRpL,OAAA,CAACjC,UAAU;gBAACkO,OAAO,EAAC,IAAI;gBAACO,SAAS,EAAC,IAAI;gBAAApB,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5DzL,OAAA,CAACzB,cAAc;gBAACiO,SAAS,EAAE9N,KAAM;gBAAA0M,QAAA,eAC7BpL,OAAA,CAAC5B,KAAK;kBAAAgN,QAAA,eACFpL,OAAA,CAAC3B,SAAS;oBAAA+M,QAAA,gBACNpL,OAAA,CAACvB,QAAQ;sBAAA2M,QAAA,gBACLpL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtCzL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,EAAExJ;sBAAa;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACXzL,OAAA,CAACvB,QAAQ;sBAAA2M,QAAA,EACJ1I,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,qBAAqB,gBACnE1C,OAAA,CAAAE,SAAA;wBAAAkL,QAAA,gBACIpL,OAAA,CAAC1B,SAAS;0BAAA8M,QAAA,gBACNpL,OAAA,CAAC9B,MAAM;4BAAC+N,OAAO,EAAC,UAAU;4BAACM,KAAK,EAAC,SAAS;4BAACF,OAAO,EAAE5B,kBAAmB;4BAAAW,QAAA,EAAC;0BAExE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTzL,OAAA,CAACjB,IAAI;4BACDqD,QAAQ,EAAEA,QAAS;4BACnB6L,IAAI,EAAEC,OAAO,CAAC9L,QAAQ,CAAE;4BACxB+L,OAAO,EAAEvD,kBAAmB;4BAAAQ,QAAA,gBAE5BpL,OAAA,CAAChB,QAAQ;8BAACqN,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAAC,mBAAmB,CAAE;8BAAAO,QAAA,EAAC;4BAAiB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC,eAC/FzL,OAAA,CAAChB,QAAQ;8BAACqN,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAAC,cAAc,CAAE;8BAAAO,QAAA,EAAC;4BAAY;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACZzL,OAAA,CAAC1B,SAAS;0BAAA8M,QAAA,eACNpL,OAAA,CAAC9B,MAAM;4BAAC+N,OAAO,EAAC,UAAU;4BAACM,KAAK,EAAC,SAAS;4BAAAnB,QAAA,EAAC;0BAE3C;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,eACd,CAAC,gBAEHzL,OAAA,CAAC1B,SAAS;wBAAA8M,QAAA,eACNpL,OAAA,CAAC9B,MAAM;0BAAC+N,OAAO,EAAC,UAAU;0BAACM,KAAK,EAAC,SAAS;0BAACF,OAAO,EAAEnB,iBAAkB;0BAAAE,QAAA,EAAC;wBAEvE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBACd;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPzL,OAAA,CAAChC,IAAI;QAACkI,IAAI;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACJ,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAT,QAAA,EAClD5I,0BAA0B,gBACvBxC,OAAA,CAACnC,IAAI;UAAAuN,QAAA,eACDpL,OAAA,CAAClC,WAAW;YAAAsN,QAAA,gBACRpL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,IAAI;cAACO,SAAS,EAAC,IAAI;cAACZ,KAAK,EAAE;gBAAEU,QAAQ,EAAE;cAAS,CAAE;cAAAlB,QAAA,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClHzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,QAAM,EAAC5I,0BAA0B,CAACvB,UAAU;YAAA;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnGzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,SAAO,EAAC5I,0BAA0B,CAACrB,KAAK;YAAA;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC/FzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,SAAO,EAAC5I,0BAA0B,CAACpB,KAAK;YAAA;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC/FzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,WAAS,EAAC5I,0BAA0B,CAACnB,QAAQ;YAAA;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACpGzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,QAAM,EAAC5I,0BAA0B,CAAClB,IAAI;YAAA;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7FzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,YAAU,EAAC5I,0BAA0B,CAACjB,QAAQ;YAAA;cAAA+J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrGzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,WAAS,EAAC5I,0BAA0B,CAAChB,OAAO;YAAA;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnGzL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,GAAC,OAAK,EAAC5I,0BAA0B,CAACf,GAAG;YAAA;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,gBAEPzL,OAAA,CAACnC,IAAI;UAAAuN,QAAA,eACDpL,OAAA,CAAClC,WAAW;YAAAsN,QAAA,eACRpL,OAAA,CAACjC,UAAU;cAACkO,OAAO,EAAC,IAAI;cAACO,SAAS,EAAC,IAAI;cAACZ,KAAK,EAAE;gBAAEU,QAAQ,EAAE;cAAS,CAAE;cAAAlB,QAAA,gBAACpL,OAAA;gBAAAoL,QAAA,EAAI;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GzL,OAAA,CAAC9B,MAAM;gBAAC+N,OAAO,EAAC,WAAW;gBAACM,KAAK,EAAC,SAAS;gBAACF,OAAO,EAAE/B,4BAA6B;gBAACsB,KAAK,EAAE;kBAAEU,QAAQ,EAAE,QAAQ;kBAAER,UAAU,EAAE;gBAAM,CAAE;gBAAAV,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACPzL,OAAA,CAACrB,MAAM;QAACsP,IAAI,EAAE/L,kBAAmB;QAACiM,OAAO,EAAE3D,yBAA0B;QAAAY,QAAA,gBACjEpL,OAAA,CAACpB,WAAW;UAAAwM,QAAA,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxDzL,OAAA,CAACnB,aAAa;UAAAuM,QAAA,eACVpL,OAAA,CAAChC,IAAI;YAAC0N,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAP,QAAA,gBACvBpL,OAAA,CAAChC,IAAI;cAACkI,IAAI;cAAC6F,EAAE,EAAE,EAAG;cAACqC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACrBpL,OAAA,CAACjC,UAAU;gBAACkO,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAd,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChEzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,YAAY;gBAClBT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACE,UAAW;gBAC3B+F,IAAI,EAAC,YAAY;gBACjB2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,WAAW;gBACjBT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACG,SAAU;gBAC1B8F,IAAI,EAAC,WAAW;gBAChB2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,OAAO;gBACbT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACI,KAAM;gBACtB6F,IAAI,EAAC,OAAO;gBACZ2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,OAAO;gBACbT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACK,KAAM;gBACtB4F,IAAI,EAAC,OAAO;gBACZ2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEA,CAAC,eACPzL,OAAA,CAAChC,IAAI;cAACkI,IAAI;cAAC6F,EAAE,EAAE,EAAG;cAACqC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACrBpL,OAAA,CAACjC,UAAU;gBAACkO,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAd,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnEzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,YAAY;gBAClBT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAAC6I,mBAAoB;gBACpC5C,IAAI,EAAC,qBAAqB;gBAC1B2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,WAAW;gBACjBT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAAC8I,kBAAmB;gBACnC7C,IAAI,EAAC,oBAAoB;gBACzB2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,SAAS;gBACfT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACM,QAAS;gBACzB2F,IAAI,EAAC,UAAU;gBACf2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,OAAO;gBACbT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAAC+I,cAAe;gBAC/B9C,IAAI,EAAC,gBAAgB;gBACrB2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,MAAM;gBACZT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACO,IAAK;gBACrB0F,IAAI,EAAC,MAAM;gBACX2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,UAAU;gBAChBT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACQ,QAAS;gBACzByF,IAAI,EAAC,UAAU;gBACf2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,SAAS;gBACfT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACS,OAAQ;gBACxBwF,IAAI,EAAC,SAAS;gBACd2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzL,OAAA,CAAC/B,SAAS;gBACNyO,KAAK,EAAC,KAAK;gBACXT,OAAO,EAAC,UAAU;gBAClBU,SAAS;gBACTC,IAAI,EAAC,OAAO;gBACZ3F,KAAK,EAAElG,QAAQ,CAACU,GAAI;gBACpBuF,IAAI,EAAC,KAAK;gBACV2G,QAAQ,EAAE7G,YAAa;gBACvB8E,KAAK,EAAE;kBAAEa,YAAY,EAAE;gBAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAChBzL,OAAA,CAAClB,aAAa;UAAAsM,QAAA,gBACVpL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAElD,yBAA0B;YAACoD,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzEzL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAE7B,yBAA0B;YAAC+B,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAETzL,OAAA,CAACrB,MAAM;QAACsP,IAAI,EAAEjM,UAAW;QAACmM,OAAO,EAAE5D,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACV,SAAS;QAAAvB,QAAA,gBACzEpL,OAAA,CAACpB,WAAW;UAACmO,EAAE,EAAE;YAAET,QAAQ,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpEzL,OAAA,CAACnB,aAAa;UAAAuM,QAAA,eACVpL,OAAA,CAACzB,cAAc;YAACiO,SAAS,EAAE9N,KAAM;YAAA0M,QAAA,eAC7BpL,OAAA,CAAC5B,KAAK;cAAAgN,QAAA,gBACFpL,OAAA,CAACxB,SAAS;gBAACuO,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBAAA5B,QAAA,eACrCpL,OAAA,CAACvB,QAAQ;kBAAA2M,QAAA,gBACLpL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/BzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACtCzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZzL,OAAA,CAAC3B,SAAS;gBAAA+M,QAAA,EACL3K,eAAe,CAACwE,GAAG,CAAC,CAACoJ,YAAY,EAAEC,UAAU,kBAC1CtO,OAAA,CAACvB,QAAQ;kBAAA2M,QAAA,gBACLpL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,eACNpL,OAAA;sBAAKmN,GAAG,EAAEkB,YAAY,CAAC/I,KAAK,IAAI,EAAG;sBAAC8H,GAAG,EAAEiB,YAAY,CAACjJ,KAAM;sBAACwG,KAAK,EAAE;wBAAEyB,QAAQ,EAAE,MAAM;wBAAEkB,MAAM,EAAE,MAAM;wBAAEC,SAAS,EAAE;sBAAQ;oBAAE;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC,eACZzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,gBACNpL,OAAA,CAACjC,UAAU;sBAACkO,OAAO,EAAC,WAAW;sBAACwC,UAAU,EAAC,MAAM;sBAAArD,QAAA,EAAEiD,YAAY,CAACjJ;oBAAK;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACnFzL,OAAA,CAACjC,UAAU;sBAACkO,OAAO,EAAC,OAAO;sBAACM,KAAK,EAAC,eAAe;sBAAAnB,QAAA,EAAEiD,YAAY,CAACrG;oBAAS;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACZzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,eACNpL,OAAA,CAACX,GAAG;sBAAC0N,EAAE,EAAE;wBAAEZ,OAAO,EAAE,MAAM;wBAAEuC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,EACzDiD,YAAY,CAACzF,QAAQ,CAAC3D,GAAG,CAAC,CAACgH,OAAO,EAAE0C,YAAY,kBAC7C3O,OAAA,CAACX,GAAG;wBAAkB0N,EAAE,EAAE;0BAAEZ,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEkB,GAAG,EAAE;wBAAE,CAAE;wBAAAlC,QAAA,gBACxEpL,OAAA,CAACZ,IAAI;0BACDsN,KAAK,EAAE,GAAGT,OAAO,CAAC7D,cAAc,IAAI,SAAS,IAAI6D,OAAO,CAAC5D,cAAc,GAAG,IAAI,GAAG4D,OAAO,CAAC5D,cAAc,GAAG,EAAE,EAAG;0BAC/GuE,IAAI,EAAC,OAAO;0BACZX,OAAO,EAAC;wBAAU;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACFzL,OAAA,CAACjC,UAAU;0BAACkO,OAAO,EAAC,SAAS;0BAAAb,QAAA,GAAC,OAAK,EAACa,OAAO,CAAC9D,GAAG;wBAAA;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC;sBAAA,GANvDQ,OAAO,CAAC9G,EAAE;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOf,CACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,eACNpL,OAAA,CAACX,GAAG;sBAAC0N,EAAE,EAAE;wBAAEZ,OAAO,EAAE,MAAM;wBAAEuC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,EACzDiD,YAAY,CAACzF,QAAQ,CAAC3D,GAAG,CAAEgH,OAAO,iBAC/BjM,OAAA,CAACjC,UAAU;wBAAkBkO,OAAO,EAAC,OAAO;wBAAAb,QAAA,GACvCa,OAAO,CAACxG,QAAQ,IAAI,CAAC,EAAC,QAC3B;sBAAA,GAFiBwG,OAAO,CAAC9G,EAAE;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEf,CACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,eACNpL,OAAA,CAACX,GAAG;sBAAC0N,EAAE,EAAE;wBAAEZ,OAAO,EAAE,MAAM;wBAAEuC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,EACzDiD,YAAY,CAACzF,QAAQ,CAAC3D,GAAG,CAAEgH,OAAO,iBAC/BjM,OAAA,CAACjC,UAAU;wBAAkBkO,OAAO,EAAC,OAAO;wBAAAb,QAAA,GAAC,GACxC,EAACa,OAAO,CAAC1G,KAAK,IAAI,CAAC;sBAAA,GADP0G,OAAO,CAAC9G,EAAE;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEf,CACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACZzL,OAAA,CAAC1B,SAAS;oBAAA8M,QAAA,eACNpL,OAAA,CAACX,GAAG;sBAAC0N,EAAE,EAAE;wBAAEZ,OAAO,EAAE,MAAM;wBAAEuC,aAAa,EAAE,QAAQ;wBAAEpB,GAAG,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,EACzDiD,YAAY,CAACzF,QAAQ,CAAC3D,GAAG,CAAEgH,OAAO,iBAC/BjM,OAAA;wBAEI0N,IAAI,EAAC,UAAU;wBACfkB,OAAO,EAAEjO,gBAAgB,CAAC4G,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACf,EAAE,KAAK8G,OAAO,CAAC9G,EAAE,CAAE;wBAC/DwI,QAAQ,EAAEA,CAAA,KAAMxG,oBAAoB,CAAC8E,OAAO;sBAAE,GAHzCA,OAAO,CAAC9G,EAAE;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAIlB,CACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAnDD,SAAS6C,UAAU,EAAE;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoD1B,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAChBzL,OAAA,CAAClB,aAAa;UAAAsM,QAAA,gBACVpL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAEpD,0BAA2B;YAACsD,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1EzL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAE9B,iBAAkB;YAACgC,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACTzL,OAAA,CAACrB,MAAM;QAACsP,IAAI,EAAErL,kBAAmB;QAACuL,OAAO,EAAEhD,yBAA0B;QAAAC,QAAA,gBACjEpL,OAAA,CAACpB,WAAW;UAAAwM,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvCzL,OAAA,CAACnB,aAAa;UAAAuM,QAAA,gBACVpL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,kBAAkB;YACxBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEnE,cAAe;YACtB6K,QAAQ,EAAG5G,CAAC,IAAKhE,iBAAiB,CAACgE,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,2BAA2B;YACjCT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEjE,sBAAuB;YAC9B2K,QAAQ,EAAG5G,CAAC,IAAK9D,yBAAyB,CAAC8D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,cAAc;YACpBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAE/D,WAAY;YACnByK,QAAQ,EAAG5G,CAAC,IAAK5D,cAAc,CAAC4D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,SAAS;YACfT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAE7D,MAAO;YACduK,QAAQ,EAAG5G,CAAC,IAAK1D,SAAS,CAAC0D,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,UAAU;YAChBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAE3D,OAAQ;YACfqK,QAAQ,EAAG5G,CAAC,IAAKxD,UAAU,CAACwD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,UAAU;YAChBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEzD,QAAS;YAChBmK,QAAQ,EAAG5G,CAAC,IAAKtD,WAAW,CAACsD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,eAAe;YACrBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEvD,YAAa;YACpBiK,QAAQ,EAAG5G,CAAC,IAAKpD,eAAe,CAACoD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,SAAS;YACfT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAErD,OAAQ;YACf+J,QAAQ,EAAG5G,CAAC,IAAKlD,UAAU,CAACkD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,UAAU;YAChBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEnD,OAAQ;YACf6J,QAAQ,EAAG5G,CAAC,IAAKhD,UAAU,CAACgD,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACFzL,OAAA,CAAC/B,SAAS;YACNyO,KAAK,EAAC,WAAW;YACjBT,OAAO,EAAC,UAAU;YAClBU,SAAS;YACTC,IAAI,EAAC,OAAO;YACZ3F,KAAK,EAAEjD,QAAS;YAChB2J,QAAQ,EAAG5G,CAAC,IAAK9C,WAAW,CAAC8C,CAAC,CAACG,MAAM,CAACD,KAAK;UAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAChBzL,OAAA,CAAClB,aAAa;UAAAsM,QAAA,gBACVpL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAEvD,oBAAqB;YAACyD,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtEzL,OAAA,CAAC9B,MAAM;YAACmO,OAAO,EAAElB,yBAA0B;YAACoB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eACT,CAAC;AAEX,CAAC;AAACrL,EAAA,CAz2BWD,WAAW;EAAA,QACHL,WAAW,EACRD,SAAS;AAAA;AAAAgP,EAAA,GAFpB1O,WAAW;AAAA,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}