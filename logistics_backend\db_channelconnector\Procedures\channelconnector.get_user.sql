

DROP PROCEDURE IF E<PERSON>ISTS channelconnector.get_user;

<PERSON><PERSON><PERSON><PERSON><PERSON> $$
CREATE  PROCEDURE channelconnector.`get_user` 
(
	IN `in_user_id` INT
)  
BEGIN
	/*
		#Retun fby credentials for given user_id by using default credentials 
        call channelconnector.get_user(1006);
        
        SELECT * FROM channelconnector._2_channel;
    */
    DECLARE in_channelName VARCHAR(128);
    
    SET in_channelName = (
		SELECT channelName from channelconnector._2_channel
		Where channelId = in_user_id
		order by id desc
		limit 1
    );
    
 	SELECT DISTINCT
		t1.`id`,
		t1.`name`,
		t1.`email`,
		in_user_id as `fby_user_id`,
		t1.`auth_username`,
		t1.`auth_password`,
		t1.`created_at`,
		t1.`updated_at`,
        in_channelName as channelName,
        t2.domain,
        t2.stockUpdate,
		t2.priceUpdate,
		t2.orderSync,
		t2.productPublish
        
	FROM 
		`channelconnector`.`users` AS t1
        INNER JOIN `channelconnector`.`_2_channel` AS t2
        ON t2.channelId = in_user_id
	ORDER BY t1.id
	LIMIT 1;
    
END$$