-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `fby_cancel_reason`
--

DROP TABLE IF EXISTS `fby_cancel_reason`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fby_cancel_reason` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fby_cancel_reason`
--

LOCK TABLES `fby_cancel_reason` WRITE;
/*!40000 ALTER TABLE `fby_cancel_reason` DISABLE KEYS */;
INSERT INTO `fby_cancel_reason` VALUES (1,'Ricevuto prodotto difettato','RPRODIF','2022-01-28 17:03:17'),(2,'Ricevuto prodotto errato','RPROERR','2022-01-28 17:03:17'),(3,'Taglia errata','RTAGERR','2022-01-28 17:03:17'),(4,'Ricevuto colore errato','RCOLERR','2022-01-28 17:03:17'),(5,'Ripensamento cliente','RRIPCLI','2022-01-28 17:03:18'),(6,'Acquisto grandi quantità prodotto','RRIMFDK','2022-01-28 17:03:18'),(7,'Acquisto non autorizzato','RACQNOT','2022-01-28 17:03:18'),(8,'Articolo non desiderato','RPRONOD','2022-01-28 17:03:18'),(9,'Articolo non richiesto incluso nella spedizione','RPRONRS','2022-01-28 17:03:18'),(10,'Cambio colore','RCAMCOL','2022-01-28 17:03:18'),(11,'Cambio modello','RCAMMOD','2022-01-28 17:03:18'),(12,'Cambio prodotto','RCAMPRO','2022-01-28 17:03:18'),(13,'Cambio taglia','RCAMTAG','2022-01-28 17:03:18'),(14,'Condizioni diverse prodotto','RCONDIV','2022-01-28 17:03:18'),(15,'Doppia spedizione','RDOPSPE','2022-01-28 17:03:18'),(16,'Mancano parti/accessori al prodotto','RPARMAN','2022-01-28 17:03:18'),(17,'Mancata consegna','RMANCON','2022-01-28 17:03:18'),(18,'Ordine sbagliato dal cliente','RORDERR','2022-01-28 17:03:18'),(19,'Prestazioni/qualità differenti da quelle attese','RPROASP','2022-01-28 17:03:18'),(20,'Prodotto consegnato in ritardo','RCONRIT','2022-01-28 17:03:18'),(21,'Prodotto danneggiato dal corriere','RDANCOR','2022-01-28 17:03:18'),(22,'Prodotto non completamente compatibile con il mio sistema','RPRONOS','2022-01-28 17:03:18'),(23,'Prodotto non conforme alle aspettative','RPRONOA','2022-01-28 17:03:18'),(24,'Prodotto non corrisponde alla descrizione','RPRONOC','2022-01-28 17:03:18'),(25,'Tempi consegna/spedizione lunghi','RTEMLUN','2022-01-28 17:03:18'),(26,'Trovato prezzo migliore altrove','RPREMIG','2022-01-28 17:03:19'),(53,'Indirizzo inutilizzabile','RIND','2022-03-02 00:05:01'),(54,'Errore inserimento ordine','ERR','2022-03-02 00:05:01'),(55,'Destinazione rifiutata dal corriere','DESTRIF','2022-03-02 00:05:01');
/*!40000 ALTER TABLE `fby_cancel_reason` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:28:02
