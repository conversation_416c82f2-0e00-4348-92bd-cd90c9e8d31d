const { upsertProviderWeightSlabSchema, getProviderWeightSlabsSchema } = require('./validators/weightSlabValidator');
const ShippingProviderWeightsService = require('../../../services/hcl/shippingProviderWeightsService.js');
const Constants = require("../../../misc/constants.js");
const helpers = require('../../../misc/helpers.js');

// Insert/Update Weight Slab
exports.upsertWeightSlab = async (req, res) => {
    try {
        ///Validate request body
        const { error, value } = upsertProviderWeightSlabSchema.validate(req.body, { abortEarly: false });
        if (error) {
           return helpers.sendError(
                res, 
                Constants.HTTPSTATUSCODES.BAD_REQUEST, 
                Constants.ERRORCODES.VALIDATION_ERROR, 
                error.details.map((err) => err.message),
                req.body
            );
        }
        // Proceed with the service call
        const result = await ShippingProviderWeightsService.upsertWeightSlab(req.body, req.user.id);
        return helpers.sendSuccess(
            res, 
            Constants.HTTPSTATUSCODES.OK, 
            result.message, 
            result, 
            req.body
        );
    } catch (error) {
        if (error.sqlState === '45000') {
            helpers.sendError(
                res,  
                Constants.HTTPSTATUSCODES.CONFLICT, 
                Constants.ERRORCODES.DUPLICATE_RESOURCE, 
                error.message, 
                req.body
            );
        } else {
            helpers.sendError(res, Constants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
                Constants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
        }
    }
};

// Get Weight Slabs by Client ID
exports.getWeightSlabs = async (req, res) => {
    try {
        const { error, value } = getProviderWeightSlabsSchema.validate(req.query, { abortEarly: false });
        if (error) {
            helpers.sendError(
                res, 
                Constants.HTTPSTATUSCODES.BAD_REQUEST, 
                Constants.ERRORCODES.VALIDATION_ERROR, 
                error.details.map((err) => err.message), 
                req.query
            );
        }
        
        const {shippingProviderId, mode = null, orderType = null } = req.query;
        const result = await ShippingProviderWeightsService.getWeightSlabs(shippingProviderId, mode, orderType);

        return helpers.sendSuccess(res, 
            Constants.HTTPSTATUSCODES.OK, 
            Constants.SUCESSSMESSAGES.GET, 
            result, 
            req.query
        );
    } catch (error) {
        return helpers.sendError(
            res, 
            Constants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            Constants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query
        );
    }
};