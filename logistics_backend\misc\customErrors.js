// Custom error classes for better error handling
class ShipmentCalculationError extends Error {
    constructor(message, context) {
        super(message);
        this.name = 'ShipmentCalculationError';
        this.context = context;
    }
}

class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ValidationError';
    }
}

class ServiceabilityError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ServiceabilityError';
    }
}
