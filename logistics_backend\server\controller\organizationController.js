const helpers = require('../../misc/helpers.js');
const miscConstants = require("../../misc/constants.js");
const OrganizationService = require('../../services/organizations/organizationService.js');

exports.createOrganization = async (req, res) => {
    try {
       
        const { name, domain, orderVolumes, themeColors } = req.body;

        // Validate required fields
        if (!name || !domain) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.VALIDATION_ERROR,
                'Organization name and domain are required',
                req.body
            );
        }

        // Check if logo file was uploaded
        if (!req.file) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.VALIDATION_ERROR,
                'Organization logo is required',
                req.body
            );
        }

        // Process logo upload
        const path = `/${miscConstants.MEDIA_PATH.UPLOADS_DIR}/org-logos/`;
        const logoUrl = `${path}${req.file.filename}`;

        const result = await OrganizationService.createOrganization({
            name,
            domain,
            logoUrl,
            orderVolumes,
            themeColors
        });
         
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.CREATED, 
            'Organization created successfully', 
            result
        );

    } catch (error) {
        // Handle duplicate organization/domain errors specifically
        if (error.message.includes('already exists')) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.CONFLICT,
                miscConstants.ERRORCODES.DUPLICATE_RESOURCE,
                error.message,
                req.body
            );
        }

        // For other errors
        console.error('Organization creation error:', error);
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message || 'Failed to create organization',
            req.body
        );
    }
};

exports.getOrganization = async (req, res) => {
    try {
        const { id } = req.params;
         // Validate input
         if (!id || isNaN(id)) {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.VALIDATION_ERROR, 'Invalid organization ID', req.params);
        }

        const orgData = await OrganizationService.getOrganizationData(parseInt(id));

        if (!orgData) {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
                miscConstants.ERRORCODES.NOT_FOUND,  'Organization not found', req.params);
        }

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            orgData
        ); 
    } catch (error) {
        return helpers.sendError(res,miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.params);
    }
};

exports.getAllOrganizations = async (req, res) => {
    try {
   
        const { page = 1, pageSize = 0} = req.query;
        const userData = req.user;
        // Parse pagination values to integers
        const pageNum = parseInt(page, 10) || 1;
        const pageSizeNum = pageSize || 0 ;
        const orgnizations = await OrganizationService.getAllOrganizations(pageNum, pageSizeNum);

        if (!orgnizations) {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
                miscConstants.ERRORCODES.NOT_FOUND,  'Organizations not found', req.params);
        }

        return helpers.sendPaginationResponse(res, miscConstants.HTTPSTATUSCODES.OK, 
                'Organizations fetched successfully', orgnizations, orgnizations.length , pageNum, pageSizeNum);
    } catch (error) {
        return helpers.sendError(res,miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.params);
    }
};



exports.createOrgDetails = async (req, res) => {
    try {
        const { orgId } = req.params;
        const {
            gstNumber,
            aboutContent,
            privacyPolicy,
            contactPerson,
            support,
            contactAddress
        } = req.body;
        const userId = req.user.id;

        const result = await OrganizationService.createOrgDetails({
                organizationId: orgId,
                gstNumber,
                aboutContent,
                privacyPolicy,
                contactPerson,
                support,
                contactAddress,
        }, userId);

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.CREATED, 
            miscConstants.SUCESSSMESSAGES.INSERT, 
            result
        ); 
    } catch (error) {
        return helpers.sendError(res,miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

exports.updateOrgPage = async (req, res) => {
    try {
        const { orgId, pageType } = req.params;
        const { title, content, metaDescription, isPublished } = req.body;

        const result = await OrganizationService.updateOrgPage({
            organizationId: orgId,
            pageType,
            title,
            content,
            metaDescription,
            isPublished
        });

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.UPDATE, 
            result
        ); 
    } catch (error) {
        return helpers.sendError(res,miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
                  miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

exports.getOrgPage = async (req, res) => {
    try {
        const { orgId, pageType } = req.params;
        const page = await OrganizationService.getOrgPage(orgId, pageType);
        
        if (!page || !page.isPublished) {
            return helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
                miscConstants.ERRORCODES.NOT_FOUND,
                miscConstants.ERRORMESSAGES.NOT_FOUND, 
                req.params
            ); 
        }

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            page
        ); 
    } catch (error) {
        return helpers.sendError(res,miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.params);
    }
};
