DROP PROCEDURE IF EXISTS channelconnector.updateOrderCancelStatus;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateOrderCancelStatus(
	`in_fby_id` VARCHAR(128),
	`in_ordr_number` VARCHAR(256),
	`in_financial_status` VARCHAR(20),
	`in_cancl_reson` VARCHAR(128),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	/*
	CALL updateOrderCancelStatus(8,'4666394706178',"refunded","customer","get_Shopify_Orders","2d404606-c467-4325-98e2-748eca2761f5","2022-03-03 05:12:00");
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details AS od 
	SET 
		od.payment_status = in_financial_status,
		od.order_status = 'canceled',
		od.cancel_reason = in_cancl_reson,
		od.is_canceled_fby = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_canceled_fby IS NULL;
    
	UPDATE order_masters AS om 
	SET 
		om.payment_status = in_financial_status,
		om.order_status = 'canceled',
		om.is_canceled = 0,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_id
			AND om.order_no = in_ordr_number
			AND om.is_canceled <> 1;
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;