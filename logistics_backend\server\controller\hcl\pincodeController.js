const PincodeService = require('../../../services/hcl/pincodeService.js');
const miscConstants = require("../../../misc/constants.js");
const helpers = require('../../../misc/helpers.js');
const logger = require('../../../misc/logger.js');
const multer = require('multer');
const path = require('path');

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
      cb(null, miscConstants.MEDIA_PATH.UPLOADS_DIR);
  },
  filename: (req, file, cb) => {
      cb(null, `${Date.now()}-${file.originalname}`);
  }
});

exports.upload = multer({ storage });

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */
exports.uploadPincodes = async (req, res) => {
  try {
    if (!req.file) {
      return helpers.sendError(res,  miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
        miscConstants.ERRORCODES.VALIDATION_ERROR, 
        'No file uploaded', req.file);
    }
    const userId = req.user.id;
    const uploadDir = miscConstants.MEDIA_PATH.UPLOADS_DIR;
    const filePath = path.join(uploadDir, req.file.filename);
    await PincodeService.importPincodes(filePath, userId);

    helpers.sendSuccess(
      res, 
      miscConstants.HTTPSTATUSCODES.OK, 
      'Pincodes imported successfully',
      req.file
    );

  } catch (error) {
    logger.logError('Error uploading pincodes:', error);
    return helpers.sendError(
      res,  
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message, 
      req.file
    );
  }
};

// Add Pincode
exports.addPincode = async (req, res) => {
  try {

    const {
      pincode,
      shippingProviderId,
      city,
      state,
      isActive = true,
      air = false,
      surface = false,
      rail = false,
      dp = false,
      oda = false,
      pickup = false,
      prepaid = false,
      reverse = false,
      cod = false,
      zone = ''
    } = req.body;
  
    const userData = req.user;
  
    // Input validation
    if (!pincode || !shippingProviderId || !city || !state ) {
      return helpers.sendError(res, 400, 'Validation_ERROR', 'Missing required fields: pincode, shippingProviderId, city, state', req.body);
    }

    const result = await PincodeService.addPincode({
      pincode,
      shippingProviderId,
      city,
      state,
      isActive,
      air,
      surface,
      rail,
      dp,
      oda,
      pickup,
      prepaid,
      reverse,
      cod,
      zone, 
    },  userData.id);

    return helpers.sendSuccess(res, 200, result.message, result, req.body);

  } catch (error) {
    logger.logError('Error adding pincodes:', error);
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
  }
};

// Add Pincode
exports.getShippingPincodes = async (req, res) => {
  try {
    const { shippingProviderId = null, page = 1, pageSize = 0 } = req.query;
    const userData = req.user;
  // Input validation
    if (!page) {
      return helpers.sendError(res, 400, 'Validation_ERROR', 'Missing required fields: pageNum', req.query);
    }
    const {pincodes, totalRecords} = await PincodeService.getPincodes(shippingProviderId, parseInt(page), pageSize);
    return helpers.sendPaginationResponse(res, miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, pincodes, totalRecords, page, pageSize);
  } catch (error) {
    logger.logError('Error getShippingPincodes:', error);
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message, req.query);
  }
};

/**
 * getShippingPincodeDetails
 * @param {*} req 
 * @param {*} res 
 */
exports.getPincodeDetails = async (req, res) => {
  try {
    const {shippingProviderId = 1, pincode} = req.query;

    if (isNaN(shippingProviderId) || isNaN(pincode)) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
        miscConstants.ERRORCODES.BAD_REQUEST, 
        "Pincode and Shipping Providers are required.", 
        req.query);   
    }
    const result = await PincodeService.getPincodeDetails(pincode, shippingProviderId);

    if(result)
      return helpers.sendSuccess(res, 200, "Shipping Pincode Details", result, req.query);
    else
      return helpers.sendError(
              res, 
              miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
              miscConstants.ERRORCODES.NOT_FOUND, 
              "Pincode not found", req.query
            );
  } catch (error) {
    logger.logError('Error getPincodeDetails:', error);
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message, req.query);
  }

};


/**
 * getShippingPincodeDetails
 * @param {*} req 
 * @param {*} res 
 */
exports.getPincodeDetailsWithProviders = async (req, res) => {
  try {
    const {pincode} = req.query;
    const result = await PincodeService.getPincodeDetailsWithProviders(pincode);
    if(result.found === true)
      return helpers.sendSuccess(res, 200, "Shipping Pincode Details", result, req.query);
    else
    return helpers.sendError(
      res, 
      miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
      miscConstants.ERRORCODES.NOT_FOUND, 
      result.message, 
      req.query
    );
  } catch (error) {
    logger.logError('Error getPincodeDetailsWithProviders:', error);
    return helpers.sendError(res,  miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
      error.message, req.query);
  }
};



/**
 * searchPincodeMaster
 * @param {*} req 
 * @param {*} res 
 */
exports.searchPincodeMaster = async (req, res) => {
  try {
    const pincode = req.query.pincode;
    const result = await PincodeService.searchPincodeMaster(pincode);
    if(result)
      helpers.sendSuccess(res, 200, "Master Pincode Detail", result, req.query);
    else
      helpers.sendError(res, 500, 'RESOURCE_NOT_FOUND', "Pincode not found", req.query);
  } catch (error) {
    helpers.sendError(res,  miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.query);
  }
};

/**
 * Check pincode serviceability
 * @param {*} req 
 * @param {*} res 
 */
exports.checkPincodeServiceability = async (req, res) => {
  try {
    const { 
      sourcePincode, 
      destinationPincode, 
      mode = '',
      orderType = 'B2C',
      shippingProviderId
    } = req.query;

    let serviceabilityResult;

    if (shippingProviderId) {
      // Check for specific provider
      serviceabilityResult = await PincodeService.checkDeliveryServiceability(
        sourcePincode,
        destinationPincode,
        mode,
        orderType,
        parseInt(shippingProviderId)
      );
    } else {
      // Check all providers
      serviceabilityResult = await PincodeService.checkMultiProviderServiceability(
        sourcePincode,
        destinationPincode,
        mode,
        orderType
      );
    }

    helpers.sendSuccess(
      res, 
      miscConstants.HTTPSTATUSCODES.OK, 
      miscConstants.SUCESSSMESSAGES.GET, 
      serviceabilityResult
    );
  } catch (error) {
    
    const errorCode = error.message.includes('Invalid') ? 
      miscConstants.ERRORCODES.BAD_REQUEST : 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR;
    
    const statusCode = error.message.includes('Invalid') ? 
      miscConstants.HTTPSTATUSCODES.BAD_REQUEST : 
      miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR;

      logger.logError('Error checkPincodeServiceability:', error);
      return helpers.sendError(res, statusCode, errorCode, error.message, req.query);
  }
};