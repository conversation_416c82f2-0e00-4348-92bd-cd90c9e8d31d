// controllers/paymentController.js
const paymentService = require('../../../services/hcl/paymentService.js');

const paymentController = {
    // Get all payment methods
    async getPaymentMethods(req, res) {
        try {
            let paymentMethods = await paymentService.getPaymentMethods();

            // Return the payment methods
            res.status(200).json({
                message: 'Payment methods retrieved successfully',
                data: paymentMethods
            });
        } catch (error) {
            // Handle errors and return a proper error message
            res.status(500).json({
                message: 'Error fetching payment methods',
                error: error.message
            });
        }
    }
};

module.exports = paymentController;
