DROP PROCEDURE IF EXISTS channelconnector.fbyCanclOrderErrorManage;

DELIMITER $$
CREATE PROCEDURE `fbyCanclOrderErrorManage`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256), 
	IN `in_sku` VARCHAR(256), 
	IN `in_exist` TINYINT(4) UNSIGNED, 
	IN `in_cron_name` VARCHAR(60), 
	IN `in_cron_id` VARCHAR(100), 
	IN `in_error_type` VARCHAR(60), 
	IN `in_error_msg` TEXT, 
	IN `in_time` DATETIME
)
BEGIN
    IF in_exist = 1 THEN
    SET SQL_SAFE_UPDATES = 0;
      UPDATE order_details AS OD 
	  SET 
	  	OD.count=OD.count+1,
		OD.updated_at=in_time 
	  WHERE 
	  	OD.cron_id=in_cron_id 
		AND OD.order_no=in_order_no 
		AND OD.sku=in_sku;
      
      UPDATE cron_error_log AS CL 
	  SET 
	  	CL.type_error=in_error_type,
		CL.error_message=in_error_msg 
	  WHERE 
	  	CL.cron_id=in_cron_id;
    SET SQL_SAFE_UPDATES = 1;
    ELSE
    SET SQL_SAFE_UPDATES = 0;
     UPDATE order_details AS OD 
	 SET 
	 	OD.fby_error_flag=1,
		OD.count=1,
		OD.cron_name=in_cron_name,
		OD.cron_id=in_cron_id,
		OD.updated_at=in_time 
	 WHERE 
	 	OD.fby_user_id=in_fby_user_id 
		AND OD.order_no=in_order_no 
		AND OD.sku=in_sku;
    SET SQL_SAFE_UPDATES = 1;
    END IF;
END$$
DELIMITER ;