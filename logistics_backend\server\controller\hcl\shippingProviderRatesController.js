const helpers = require('../../../misc/helpers.js');
const miscConstants = require("../../../misc/constants.js");
const ShippingProvideRatesService = require('../../../services/hcl/shippingProviderRatesService.js');

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.getShippingProviderRates = async (req, res, next) => {
    try {
        const {shippingProviderId, orderType, mode} = req.query;
        if (!shippingProviderId || !orderType || !mode) {
            helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.BAD_REQUEST, 
                "shippingProviderId, ordertype and mode are required!", 
                req.query
            );
        }
        const shippingProviderRates = await ShippingProvideRatesService.getShippingProviderRates(shippingProviderId, orderType, mode);
        helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            shippingProviderRates, 
            req.query
        ); 
    } catch (error) {
        helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.getShippingProviderZoneRates = async (req, res, next) => {
    try {
        const {shippingProviderId, orderType, mode} = req.query;
        if (!shippingProviderId || !orderType || !mode) {
            helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.BAD_REQUEST, 
                "shippingProviderId, ordertype and mode are required!", 
                req.query
            );
        }
        const shippingProviderRates = await ShippingProvideRatesService.getShippingProviderZoneRates(shippingProviderId, orderType, mode);
        helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            shippingProviderRates, 
            req.query
        ); 
    } catch (error) {
        helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.upsertShippingProviderRates = async (req, res, next) => {
    try {
        const { shippingProviderId, mode, orderType, weightSlabId, rates } = req.body;
        if (!Array.isArray(rates) || rates.length === 0) {
            helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.BAD_REQUEST,
                "Invalid data format. 'rates' should be a non-empty array.", 
                req.body
            );
        }

        const result = await ShippingProvideRatesService.upsertShippingProviderRates(
            { shippingProviderId, mode, orderType, weightSlabId, rates}, 
            req.user.id
        );
       return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result
        ); 

    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */

exports.getShippingProviderAdditionalRates = async (req, res, next) => {
    try {
        const { shippingProviderId, orderType } = req.query;
        const additionalRates = await ShippingProvideRatesService.getShippingProviderAdditionalRates(shippingProviderId, orderType);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            additionalRates
        ); 
    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query);
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.upsertShippingProviderAdditionalRates = async (req, res, next) => {
    try {
        
        const result = await ShippingProvideRatesService.upsertShippingProviderAdditionalRates(req.body, req.user.id);

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result
        ); 

    } catch (error) {

        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );

    }
    next();
};


