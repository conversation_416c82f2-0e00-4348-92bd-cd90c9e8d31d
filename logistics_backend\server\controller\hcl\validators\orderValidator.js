const Joi = require('joi');
const { timestamp } = require('rxjs');

const orderCreateValidationSchema = Joi.object({
    orderStatus: Joi.string().allow(''),
    paymentStatus: Joi.string().allow(''),
    channelOrderId: Joi.string().allow(''),
    supplierOrderId: Joi.string().allow(''),
    channelUserId: Joi.number().allow(''),

    shipmentDetails: Joi.object({
        invoiceNo: Joi.string().required(),
        originalInvoiceNo: Joi.string().allow(null,'').optional(),
        paymentMode: Joi.string().required(),
        ndd: Joi.string().valid('YES', 'NO').required(),
        express: Joi.string().required(),
        consigneeFirstName: Joi.string().required(),
        consigneeLastName: Joi.string().required(),
        consigneeEmail: Joi.string().email().required(),
        tnc: Joi.string().required(),
        numberOfPackages: Joi.number().integer().default(1),
        order: Joi.object({
            amount: Joi.number().required(),
            totalAmount: Joi.number().required(),
            collectibleCod: Joi.number().required(),
            currency: Joi.string().required(),
            taxes: Joi.array().items(
                Joi.object({
                    type: Joi.string().required(),
                    percentage: Joi.number().required(),
                    amount: Joi.number().required()
                })
            ).required(),
            extraCharges: Joi.array().items(
                Joi.object({
                    type: Joi.string().required(),
                    percentage: Joi.number().required(),
                    amount: Joi.number().required()
                })
            ).required()
        }).required(),
        productList: Joi.array().items(
            Joi.object({
                name: Joi.string().required(),
                quantity: Joi.number().required(),
                value: Joi.number().required(),
                currency: Joi.string().required(),
                sku: Joi.string().required(),
                gst: Joi.number().required(),
                gstAmount: Joi.number().required(),
                packageId: Joi.number().integer().default(1),
            })
        ).required(),
        packageDetails: Joi.array().items(
            Joi.object({
                packageId: Joi.number().integer().default(1),
                packetDetails: Joi.array().items(
                    Joi.object({
                        type: Joi.string().valid('Weight', 'DimensionsLength', 'DimensionsWidth', 'DimensionsHeight').required(),
                        unitMeasurement: Joi.string().when('type', {
                            is: 'Weight',
                            then: Joi.string().valid('KG').required(),
                            otherwise: Joi.string().valid('cms').required()
                        }),
                        unit: Joi.number().positive().required()
                    })
                ).length(4).required() // Ensures exactly 4 packetDetails per packet
            })
        ).required(),
        addressDetails: Joi.array().items(
            Joi.object({
                addressDetailType: Joi.string().required(),
                consigneeFirstName: Joi.string().allow(''),
                consigneeLastName: Joi.string().allow(''),
                warehouseId: Joi.number().optional(),
                contact: Joi.object({
                    consigneeContact: Joi.string().required(),
                    alternateContact: Joi.string().allow(''),
                    email: Joi.string().email().required()
                }).optional(),
                address: Joi.object({
            
                    addressType: Joi.string().optional(),
                    addressLine1: Joi.string().optional(),
                    addressLine2: Joi.string().optional(),
                    city: Joi.string().optional(),
                    state: Joi.string().optional(),
                    zip: Joi.string().optional(),
                    country: Joi.string().optional(),
                }).optional()
            })
        ).required()
    }).required(),
    shippingInfo: Joi.object({
        shippingProvoider: Joi.object({
           id: Joi.number().required(),
           name: Joi.string().allow(''),
        }).optional(), 
        awb: Joi.string().allow(''),
        totalShipmentCharges: Joi.number().required(),
        shipmentCharges: Joi.number().required(),
        codCharges: Joi.number().required(),
        effectiveWeight: Joi.number().required(),
        unitMeasurement: Joi.string().required(),
        isCharged: Joi.number().allow(''),
        packageCharges: Joi.array().items(
            Joi.object({ 
                packageId: Joi.number().integer().default(1),
                shipmentCharges: Joi.number().required(),
                codCharges: Joi.number().required(),
            }).optional()
        ).optional()                    
    }).required()
});

const orderUpdateValidationSchema = Joi.object({
    orderId:Joi.number().required(),
    orderStatus: Joi.string().required(),
    paymentStatus: Joi.string().required(),
    channelOrderId: Joi.string().allow('').optional(),
    supplierOrderId: Joi.string().allow('').optional(),
    channelUserId: Joi.number().allow('').optional(),
   
    shipmentDetails: Joi.object({
        invoiceNo: Joi.string().required(),
        originalInvoiceNo: Joi.string().allow(null, '').optional(),
        paymentMode: Joi.string().required(),
        ndd: Joi.string().valid('YES', 'NO').required(),
        express: Joi.string().required(),
        consigneeFirstName: Joi.string().required(),
        consigneeLastName: Joi.string().required(),
        consigneeName: Joi.string().allow('').optional(),
        consigneeEmail: Joi.string().email().required(),
        numberOfPackages: Joi.number().integer().default(1),
        tnc: Joi.string().allow('').optional(),
        order: Joi.object({
            amount: Joi.number().required(),
            totalAmount: Joi.number().required(),
            collectibleCod: Joi.number().required(),
            currency: Joi.string().required(),
            taxes: Joi.array().items(
                Joi.object({
                    type: Joi.string().required(),
                    percentage: Joi.number().required(),
                    amount: Joi.number().required()
                })
            ).required(),
            extraCharges: Joi.array().items(
                Joi.object({
                    type: Joi.string().required(),
                    percentage: Joi.number().required(),
                    amount: Joi.number().required()
                })
            ).required()
        }).required(),
        productList: Joi.array().items(
            Joi.object({
                name: Joi.string().required(),
                quantity: Joi.number().required(),
                value: Joi.number().required(),
                currency: Joi.string().required(),
                sku: Joi.string().required(),  
                gst: Joi.number().required(),
                gstAmount: Joi.number().required(),
                packageId: Joi.number().integer().default(1),
            })
        ).required(),
        packageDetails: Joi.array().items(
            Joi.object({
                packageId: Joi.number().integer().default(1),
                packetDetails: Joi.array().items(
                    Joi.object({
                        type: Joi.string().valid('Weight', 'DimensionsLength', 'DimensionsWidth', 'DimensionsHeight').required(),
                        unitMeasurement: Joi.string().when('type', {
                            is: 'Weight',
                            then: Joi.string().valid('KG').required(),
                            otherwise: Joi.string().valid('cms').required()
                        }),
                        unit: Joi.number().positive().required()
                    })
                ).length(4).required() // Ensures exactly 4 packetDetails per packet
            })
        ).required(),
        addressDetails: Joi.array().items(
            Joi.object({
                addressDetailType: Joi.string().required(),
                consigneeFirstName: Joi.string().allow(''),
                consigneeLastName: Joi.string().allow(''),
                warehouseId: Joi.number().optional(),
                contact: Joi.object({
                    consigneeContact: Joi.string().required(),
                    alternateContact: Joi.string().allow(''),
                    email: Joi.string().email().required()
                }).optional(),
                address: Joi.object({
                
                    addressType: Joi.string().optional(),
                    addressLine1: Joi.string().optional(),
                    addressLine2: Joi.string().optional(),
                    city: Joi.string().optional(),
                    state: Joi.string().optional(),
                    zip: Joi.string().optional(),
                    country: Joi.string().optional(),
                }).optional()
            })
        ).required()
    }).required(),
    shippingInfo: Joi.object({
        shippingProvoider: Joi.object({
           id: Joi.number().required(),
           name: Joi.string().allow(''),
        }).optional(), 
        awb: Joi.string().allow(''),
        totalShipmentCharges: Joi.number().required(),
        shipmentCharges: Joi.number().required(),
        codCharges: Joi.number().required(),
        effectiveWeight: Joi.number().required(),
        unitMeasurement: Joi.string().required(),
        isCharged: Joi.number().allow(''),
        packageCharges: Joi.array().items(
            Joi.object({ 
                packageId: Joi.number().integer().default(1),
                shipmentCharges: Joi.number().required(),
                codCharges: Joi.number().required(),
            }).optional()
        ).optional()     
    }).optional()
});

const searchOrderValidationSchema = Joi.object({
    orderId: Joi.number().optional(),
    statuses: Joi.array().items(Joi.string().valid('pending', 'completed', 'shipped')).optional(),
    paymentStatuses: Joi.array().items(Joi.string().valid('paid', 'unpaid')).optional(),
    page: Joi.number().min(1).default(1),
    pageSize: Joi.number().min(1).default(10),
});

// Validator for updating order status
const orderUpdateStatusSchema = Joi.object({
    orderId: Joi.number().integer().required(),
    status: Joi.string().max(20).required(),
    remarks: Joi.string().max(100).allow(null, ''),
    timestamp: Joi.date().allow(null, '').optional()
});

// Validator for updating bulk order status
const bulkOrderUpdateStatusSchema = Joi.array().items(
    Joi.object({
        orderId: Joi.number().required(),
        status: Joi.string().required(),
        remarks: Joi.string().optional(),
        timestamp: Joi.date().allow(null, '').optional()
    })
);

const searcOrdersSchema =  Joi.object({
    clientId: Joi.number().integer().allow(''),
    awb: Joi.string().allow(''),
    orderId: Joi.number().integer().allow(''),
    invoiceNumber: Joi.string().allow(''),
    express: Joi.string().allow(''),
    serviceProviderId: Joi.number().integer().allow(''),
    returnAddressId: Joi.number().integer().allow(''),
    pickupAddressId: Joi.number().integer().allow(''),
    status: Joi.string().allow(''),
    state: Joi.string().allow(''),
    paymentStatus: Joi.string().allow(''),
    paymentMode: Joi.string().allow(''),
    orderFrom: Joi.date().allow(''),
    orderTo: Joi.date().allow(''),
    dateType: Joi.string().allow(''),
    page: Joi.number().integer().optional().default(1),
    pageSize: Joi.number().integer().optional().default(50),
});


module.exports = { orderCreateValidationSchema, 
                orderUpdateValidationSchema, 
                searchOrderValidationSchema, 
                orderUpdateStatusSchema,
                bulkOrderUpdateStatusSchema,
                searcOrdersSchema
            };
