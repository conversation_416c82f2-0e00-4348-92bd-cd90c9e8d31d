const dbpool = require('../startup/db.js');
const CacheService = require('../misc/cache.js');
const Constants = require("../misc/constants.js");
const helpers = require('../misc/helpers.js');
const ccDB = process.env.DB_DATABASE || "channelconnector";



// function checkPermission(permission) {
//     return (req, res, next) => {
//       const user = req.user; // Assuming you attach the user object to the request
  
//       // Check if the user is a Super Admin
//       const isSuperAdmin = user.roles.includes('Super Admin');
  
//       // If the user is a Super Admin, bypass permission check
//       if (isSuperAdmin) {
//         return next();
//       }
  
//       // For regular users, check if they have the required permission
//       const hasPermission = user.permissions.includes(permission);
//       if (!hasPermission) {
//         return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
//       }
  
//       next();
//     };
//   }