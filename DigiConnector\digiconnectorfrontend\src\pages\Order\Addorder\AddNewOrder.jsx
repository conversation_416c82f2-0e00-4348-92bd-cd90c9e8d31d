import React, { useState, useEffect } from 'react';
import { Card, CardContent, Typography, Grid, TextField, Button, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Menu, MenuItem, Select, FormControl, InputLabel, Chip, Box } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import axios from 'axios';
import { NavBar } from '../../../components/Navbar/Navbar';
import { Sidebar } from '../../../components/SidePanel/Sidebar';
import { useParams, useNavigate } from 'react-router-dom';

export const AddNewOrder = () => {
    const navigate = useNavigate();
    const { orderNo } = useParams()
    const [products, setProducts] = useState([]);
    const [groupedProducts, setGroupedProducts] = useState([]);
    const [selectedProducts, setSelectedProducts] = useState([]);
    const [productQuantities, setProductQuantities] = useState({});
    const [formData, setFormData] = useState({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        address1: '',
        city: '',
        province: '',
        country: '',
        zip: '',
    })
    const [responseData, setResponseData] = useState([]);
    const [paymentStatus, setPaymentStatus] = useState('Pending');
    const [totalPayment, setTotalPayment] = useState(0);
    const [openDialog, setOpenDialog] = useState(false);
    const [openCustomerDialog, setOpenCustomerDialog] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);
    const [paymentMethod, setPaymentMethod] = useState('');
    const [customerAndShippingDetails, setCustomerAndShippingDetails] = useState(null);
    const [orderStatus, setOrderStatus] = useState('fulfilled');
    const [openTrackingDialog, setOpenTrackingDialog] = useState(false);
    const [channelOrderId, setChannelOrderId] = useState('');
    const [originalChannelOrderId, setOriginalChannelOrderId] = useState('');
    const [channelCode, setChannelCode] = useState('');
    const [skuEan, setSkuEan] = useState('');
    const [skuCode, setSkuCode] = useState('');
    const [tracking, setTracking] = useState('');
    const [shipmentDate, setShipmentDate] = useState('');
    const [carrier, setCarrier] = useState('');
    const [shipUrl, setShipUrl] = useState('');
    const [isReturn, setIsReturn] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            try {
                if (orderNo) {
                    // Fetch order details if orderNo exists
                    var storedGroupCode = localStorage.getItem("groupCode");
                    const resDetails = await axios.post(
                        `${process.env.REACT_APP_BASE_URL}/common/api/get_order_detail?fby_user_id=${storedGroupCode}`,
                        { order_no: orderNo },
                        {
                            headers: {
                            'Content-Type': 'application/json',
                            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `
                        }
                        }
                    );
                    if (resDetails.data.success) {
                        setResponseData(resDetails.data.success.data);
                        const responseData = resDetails.data.success.data.map(element => ({
                            id: element.id,
                            title: element.product_name,
                            image: element.image, // Assuming there's an image property in the response
                            price: element.item_total_price,
                            quantity: element.quantity_purchased,
                            total_tax: element.item_tax,
                            line_item_id: element.order_line_item_id,
                            order_status: element.order_status,
                            payment_status: element.payment_status
                        }));
                        if (responseData.length > 0) {
                            setSelectedProducts(responseData.map(item => item)); // Update here
                            setOrderStatus(responseData[0].order_status)
                            setPaymentStatus(responseData[0].payment_status)
                        }
                        const shipData = resDetails.data.success.data.map(element => ({
                            first_name: element.recipient_name,
                            last_name: '',
                            email: element.buyer_email,
                            phone: element.ship_phone_number,
                            address1: element.ship_address_1,
                            city: element.ship_city,
                            province: element.ship_state_code,
                            country: element.ship_country,
                            zip: element.ship_postal_code,
                        }));
                        // Set the customer and shipping details
                        setCustomerAndShippingDetails(shipData[0]);
                    }
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, [orderNo]);


    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleCheckboxChange = (product) => {
        setSelectedProducts(prevState => {
            const isSelected = prevState.some(item => item.id === product.id);
            if (!isSelected) {
                // Initialize quantity when product is selected
                setProductQuantities(prev => ({
                    ...prev,
                    [product.id]: 1
                }));
                return [...prevState, product];
            } else {
                // Remove quantity when product is deselected
                setProductQuantities(prev => {
                    const newQuantities = { ...prev };
                    delete newQuantities[product.id];
                    return newQuantities;
                });
                return prevState.filter(item => item.id !== product.id);
            }
        });
    };

    const handleQuantityChange = (productId, newQuantity) => {
        setProductQuantities(prev => ({
            ...prev,
            [productId]: newQuantity
        }));
    };

    const handleBrowse = async () => {
        try {
            setLoading(true);
            setError('');
            var storedGroupCode = localStorage.getItem("groupCode");

            const res = await axios.post(
                `${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`,
                {},
                {
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `
                    }
                }
            );
            if (res.data.success && res.data.success.data && res.data.success.data.length > 0) {
                const responseData = res.data.success.data.map(element => ({
                    id: element.id,
                    title: element.title,
                    body_html: element.description,
                    image: element.image,
                    price: element.price,
                    quantity: element.inventory_quantity,
                    sku: element.sku,
                    option_1_value: element.option_1_value || '',
                    option_2_value: element.option_2_value || '',
                }));

                // Group products by title
                const grouped = responseData.reduce((acc, product) => {
                    const existingGroup = acc.find(group => group.title === product.title);
                    if (existingGroup) {
                        existingGroup.variants.push(product);
                    } else {
                        acc.push({
                            title: product.title,
                            image: product.image,
                            body_html: product.body_html,
                            variants: [product]
                        });
                    }
                    return acc;
                }, []);

                setProducts(responseData);
                setGroupedProducts(grouped);
                setOpenDialog(true);
            } else {
                setError('No products found');
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            setError('Failed to load products. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Function to handle tracking submit
    const handleTrackingSubmit = async () => {
        try {
            // Make API call to update tracking
            const response = await axios.post(
                `${process.env.REACT_APP_BASE_URL}/common/api/push_tracking`,
                {
                    channelOrderId: channelOrderId,
                    originalChannelOrderId: originalChannelOrderId,
                    channelCode: channelCode,
                    skuEan: skuEan,
                    skuCode: skuCode,
                    tracking: tracking,
                    shipmentDate: shipmentDate,
                    carrier: carrier,
                    shipUrl: shipUrl,
                    isReturn: isReturn
                }
            );
            // Handle success response
            console.log(response.data);
        } catch (error) {
            // Handle error
            console.error('Error updating tracking:', error);
        }
    };


    const handleSaveSelectedProducts = () => {
        setSelectedProducts(selectedProducts);

        // You can save the selected products to a state or perform any other required action here
        // For example, you can update a state with the selected products
        const selectedProductsData = selectedProducts.map(productId => {
            return products.find(product => product.id === productId);
        });
        // Do something with the selected products data, for example:
        console.log('Selected products:', selectedProductsData);

        setOpenDialog(false);
    };

    const handleSaveCustomerDetails = () => {
        // Save the entered customer and shipping details
        const newCustomerAndShippingDetails = {
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            phone: formData.phone,
            address1: formData.address1,
            city: formData.city,
            province: formData.province,
            country: formData.country,
            zip: formData.zip,
        };

        // Set the customer and shipping details
        setCustomerAndShippingDetails(newCustomerAndShippingDetails);

        // Close the customer dialog
        setOpenCustomerDialog(false);
    };

    const handleSave = async () => {
        try {
            setLoading(true);
            setError('');

            // Validate that we have customer details
            if (!customerAndShippingDetails) {
                setError('Please add customer and shipping details before saving the order.');
                return;
            }

            // Validate that we have selected products
            if (selectedProducts.length === 0) {
                setError('Please select at least one product before saving the order.');
                return;
            }

            // Check inventory availability
            const inventoryCheck = selectedProducts.every(product => {
                const requestedQuantity = productQuantities[product.id] || 1;
                const availableQuantity = product.quantity || 0;
                return requestedQuantity <= availableQuantity;
            });

            if (!inventoryCheck) {
                setError('Some products do not have sufficient inventory. Please adjust quantities.');
                return;
            }

            // Prepare the data for the request
            const requestData = {
                line_items: selectedProducts.map(product => {
                    const quantity = productQuantities[product.id] || 1;
                    return {
                        variant_id: product.id,
                        quantity: quantity,
                        price: product.price || 0,
                        sku: product.sku,
                        title: product.title,
                    };
                }),
                customer: {
                    first_name: customerAndShippingDetails.first_name,
                    last_name: customerAndShippingDetails.last_name,
                    email: customerAndShippingDetails.email,
                },
                billing_address: {
                    first_name: customerAndShippingDetails.first_name,
                    last_name: customerAndShippingDetails.last_name,
                    address1: customerAndShippingDetails.address1,
                    city: customerAndShippingDetails.city,
                    province: customerAndShippingDetails.province,
                    country: customerAndShippingDetails.country,
                    zip: customerAndShippingDetails.zip,
                    phone: customerAndShippingDetails.phone,
                },
                shipping_address: {
                    first_name: customerAndShippingDetails.first_name,
                    last_name: customerAndShippingDetails.last_name,
                    address1: customerAndShippingDetails.address1,
                    city: customerAndShippingDetails.city,
                    province: customerAndShippingDetails.province,
                    country: customerAndShippingDetails.country,
                    zip: customerAndShippingDetails.zip,
                    phone: customerAndShippingDetails.phone,
                },
                email: customerAndShippingDetails.email,
                transactions: [
                    {
                        kind: "sale",
                        status: "success",
                        amount: totalPayment,
                    }
                ],
                financial_status: paymentStatus,
                total_price: totalPayment,
            };

            var storedGroupCode = localStorage.getItem("groupCode");

            // Make the POST request
            const res = await axios({
                url: `${process.env.REACT_APP_BASE_URL}/common/api/create_shopify_order/?fby_user_id=${storedGroupCode}`,
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`
                },
                data: requestData,
            });

            console.log(res);
            if (res.data.success) {
                // Update local inventory quantities
                const updatedProducts = products.map(product => {
                    const selectedProduct = selectedProducts.find(sp => sp.id === product.id);
                    if (selectedProduct) {
                        const usedQuantity = productQuantities[product.id] || 1;
                        return {
                            ...product,
                            quantity: Math.max(0, product.quantity - usedQuantity)
                        };
                    }
                    return product;
                });
                setProducts(updatedProducts);

                // Show success message and redirect
                alert('Order created successfully!');
                navigate('/orders');
            } else {
                setError('Failed to create order. Please try again.');
            }
        } catch (error) {
            console.error('Error saving Order:', error);
            setError('Error creating order. Please try again.');
        } finally {
            setLoading(false);
        }
    };


    const handleAddCustomerAndShipping = () => {
        setOpenCustomerDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
    };

    const handleCustomerCloseDialog = () => {
        setOpenCustomerDialog(false);
    };
    const handlePaymentClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handlePaymentClose = () => {
        setAnchorEl(null);
    };

    const handlePaymentMethod = (method) => {
        setPaymentMethod(method);
        setAnchorEl(null);
        if (method === 'Mark as Paid') {
            setPaymentStatus('Paid');
        }
    };

    const handleLineItemClick = (lineItemId) => {
        navigate(`/orderMaster/${orderNo}/orderDetails/${lineItemId}`);
    };


    useEffect(() => {
        // Calculate total payment with quantities
        let totalPrice = 0;
        for (const product of selectedProducts) {
            const quantity = productQuantities[product.id] || 1;
            const price = product.item_total_price || product.price || 0;
            totalPrice += price * quantity;
        }
        setTotalPayment(totalPrice);
    }, [selectedProducts, productQuantities]);

    const handleBack = () => {
        navigate(-1);
    };

    const handleButtonClick = () => {
        setOpenTrackingDialog(true);
    };

    // Function to handle tracking dialog close
    const handleTrackingDialogClose = () => {
        setOpenTrackingDialog(false);
    };

    return (
        <>
            <NavBar selectedSidebarItem="products" />
            <Sidebar />
            <Grid container spacing={3} style={{ marginTop: '45px', marginLeft: '255px' }}>
                <Grid item xs={12} md={6}>
                    <Typography variant="h5" gutterBottom style={{ display: 'flex', alignItems: 'center' }}>
                        <ArrowBackIcon onClick={handleBack} style={{ cursor: 'pointer' }} />
                        <span style={{ fontSize: '1.2rem', marginLeft: 8 }}>Order</span>
                        <Button
                            variant="contained"
                            color="primary"
                            style={{ marginLeft: 'auto' }}
                            onClick={handleSave}
                            disabled={loading}
                        >
                            {loading ? 'Saving...' : 'Save'}
                        </Button>
                    </Typography>
                    {error && (
                        <Typography variant="body2" color="error" style={{ marginBottom: '16px' }}>
                            {error}
                        </Typography>
                    )}
                    <Grid container spacing={3}>
                        <Grid item xs={12}>
                            {(!orderNo) && (
                                <Typography variant="h5" component="h4" style={{ marginBottom: 10 }}>Product</Typography>
                            )}
                            <div style={{ display: 'flex' }}>
                                {(!orderNo) && (
                                    <>
                                        <TextField
                                            label="Search Product"
                                            variant="outlined"
                                            fullWidth
                                            size="small"
                                            InputProps={{
                                                endAdornment: (
                                                    <IconButton color="primary" aria-label="search" size="small">
                                                        <SearchIcon />
                                                    </IconButton>
                                                ),
                                            }}
                                        />
                                        <Button
                                            variant="outlined"
                                            style={{ marginLeft: 10, fontSize: '0.8rem' }}
                                            size="small"
                                            onClick={handleBrowse}
                                            disabled={loading}
                                        >
                                            {loading ? 'Loading...' : 'Browse'}
                                        </Button>
                                    </>
                                )}
                            </div>
                        </Grid>
                        {(selectedProducts.length > 0) && (
                            <Grid item xs={12}>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableHead sx={{ background: '#f5f5f5' }}>
                                            <TableRow>
                                                <TableCell>Image</TableCell>
                                                <TableCell>Title</TableCell>
                                                <TableCell>Quantity</TableCell>
                                                <TableCell>Price</TableCell>
                                                {(orderNo) && (
                                                    <TableCell>Line Order Id</TableCell>
                                                )}
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {selectedProducts.map(newproduct => {
                                                const productQuantity = productQuantities[newproduct.id] || 1;
                                                return (
                                                    <TableRow key={newproduct.id}>
                                                        <TableCell><img src={(newproduct.image || '')} alt={newproduct.title} style={{ maxWidth: '100px' }} /></TableCell>
                                                        <TableCell>
                                                            <Typography variant="subtitle2">{newproduct.title || ''}</Typography>
                                                            {(newproduct.option_1_value || newproduct.option_2_value) && (
                                                                <Typography variant="caption" color="textSecondary">
                                                                    {newproduct.option_1_value} {newproduct.option_2_value && `/ ${newproduct.option_2_value}`}
                                                                </Typography>
                                                            )}
                                                            <Typography variant="caption" display="block" color="textSecondary">
                                                                SKU: {newproduct.sku}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                                <IconButton
                                                                    size="small"
                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.max(1, productQuantity - 1))}
                                                                    disabled={productQuantity <= 1}
                                                                >
                                                                    <RemoveIcon />
                                                                </IconButton>
                                                                <TextField
                                                                    size="small"
                                                                    type="number"
                                                                    value={productQuantity}
                                                                    onChange={(e) => handleQuantityChange(newproduct.id, Math.max(1, parseInt(e.target.value) || 1))}
                                                                    inputProps={{ min: 1, max: newproduct.quantity || 999, style: { textAlign: 'center', width: '60px' } }}
                                                                />
                                                                <IconButton
                                                                    size="small"
                                                                    onClick={() => handleQuantityChange(newproduct.id, Math.min((newproduct.quantity || 999), productQuantity + 1))}
                                                                    disabled={productQuantity >= (newproduct.quantity || 999)}
                                                                >
                                                                    <AddIcon />
                                                                </IconButton>
                                                            </Box>
                                                            <Typography variant="caption" color="textSecondary">
                                                                Available: {newproduct.quantity || 0}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell>${(newproduct.price || 0) * productQuantity}</TableCell>
                                                        {orderNo &&
                                                            (<TableCell>
                                                                <Button onClick={() => handleLineItemClick(newproduct.line_item_id)}>{newproduct.line_item_id}</Button>
                                                            </TableCell>)
                                                        }
                                                    </TableRow>
                                                );
                                            })}
                                        </TableBody>

                                    </Table>
                                </TableContainer>
                            </Grid>
                        )}

                    </Grid>
                    {/* Payment Information Card */}
                    <Grid container spacing={3} style={{ marginTop: '20px', marginLeft: '5px' }}>
                        <Card>
                            <CardContent>
                                <Typography variant="h6" component="h4">Payment Summary</Typography>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableBody>
                                            <TableRow>
                                                <TableCell>Payment Status:</TableCell>
                                                <TableCell>{paymentStatus}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell><strong>Total Amount:</strong></TableCell>
                                                <TableCell><strong>${totalPayment.toFixed(2)}</strong></TableCell>
                                            </TableRow>
                                            <TableRow>
                                                {orderStatus !== 'unfulfilled' && orderStatus !== 'partially fulfilled' ? (
                                                    <>
                                                        <TableCell>
                                                            <Button variant="outlined" color="primary" onClick={handlePaymentClick}>
                                                                Collect Payment
                                                            </Button>
                                                            <Menu
                                                                anchorEl={anchorEl}
                                                                open={Boolean(anchorEl)}
                                                                onClose={handlePaymentClose}
                                                            >
                                                                <MenuItem onClick={() => handlePaymentMethod('Enter Credit Card')}>Enter Credit Card</MenuItem>
                                                                <MenuItem onClick={() => handlePaymentMethod('Mark as Paid')}>Mark as Paid</MenuItem>
                                                            </Menu>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Button variant="outlined" color="primary">
                                                                Send Invoice
                                                            </Button>
                                                        </TableCell>
                                                    </>
                                                ) : (
                                                    <TableCell>
                                                        <Button variant="outlined" color="primary" onClick={handleButtonClick}>
                                                            Add Tracking
                                                        </Button>
                                                    </TableCell>
                                                )}
                                            </TableRow>

                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
                <Grid item xs={12} md={3} style={{ marginTop: '80px' }}>
                    {customerAndShippingDetails ? (
                        <Card>
                            <CardContent>
                                <Typography variant="h5" component="h2" style={{ fontSize: '1.2rem' }}>Customer and Shipping Details:</Typography>
                                <Typography variant="body1" gutterBottom>Name: {customerAndShippingDetails.first_name}</Typography>
                                <Typography variant="body1" gutterBottom>Email: {customerAndShippingDetails.email}</Typography>
                                <Typography variant="body1" gutterBottom>Phone: {customerAndShippingDetails.phone}</Typography>
                                <Typography variant="body1" gutterBottom>Address: {customerAndShippingDetails.address1}</Typography>
                                <Typography variant="body1" gutterBottom>City: {customerAndShippingDetails.city}</Typography>
                                <Typography variant="body1" gutterBottom>Province: {customerAndShippingDetails.province}</Typography>
                                <Typography variant="body1" gutterBottom>Country: {customerAndShippingDetails.country}</Typography>
                                <Typography variant="body1" gutterBottom>ZIP: {customerAndShippingDetails.zip}</Typography>
                            </CardContent>
                        </Card>
                    ) : (
                        <Card>
                            <CardContent>
                                <Typography variant="h5" component="h2" style={{ fontSize: '0.8rem' }}><h5>Add Customer and Shipping Details</h5>
                                    <Button variant="contained" color="primary" onClick={handleAddCustomerAndShipping} style={{ fontSize: '0.8rem', marginLeft: '5px' }}>Add</Button>
                                </Typography>
                            </CardContent>
                        </Card>
                    )}
                </Grid>
                <Dialog open={openCustomerDialog} onClose={handleCustomerCloseDialog}>
                    <DialogTitle>Customer and Shipping Details</DialogTitle>
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                                <Typography variant="h6" gutterBottom>Customer Data</Typography>
                                <TextField
                                    label="First Name"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.first_name}
                                    name="first_name"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Last Name"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.last_name}
                                    name="last_name"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Email"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.email}
                                    name="email"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Phone"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.phone}
                                    name="phone"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                {/* Add more input fields for customer data */}
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <Typography variant="h6" gutterBottom>Shipping Address</Typography>
                                <TextField
                                    label="First Name"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.shipping_first_name}
                                    name="shipping_first_name"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Last Name"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.shipping_last_name}
                                    name="shipping_last_name"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Address"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.address1}
                                    name="address1"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Phone"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.shipping_phone}
                                    name="shipping_phone"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="City"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.city}
                                    name="city"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Province"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.province}
                                    name="province"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="Country"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.country}
                                    name="country"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                <TextField
                                    label="ZIP"
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    value={formData.zip}
                                    name="zip"
                                    onChange={handleChange}
                                    style={{ marginBottom: '16px' }}
                                />
                                {/* Add more input fields for shipping address */}
                            </Grid>
                        </Grid>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleSaveCustomerDetails} color="primary">Save</Button>
                        <Button onClick={handleCustomerCloseDialog} color="primary">Close</Button>
                    </DialogActions>
                </Dialog>

                <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                    <DialogTitle sx={{ fontSize: '26px' }}>Select Products</DialogTitle>
                    <DialogContent>
                        <TableContainer component={Paper}>
                            <Table>
                                <TableHead sx={{ background: '#f5f5f5' }}>
                                    <TableRow>
                                        <TableCell>Image</TableCell>
                                        <TableCell>Product</TableCell>
                                        <TableCell>Variants</TableCell>
                                        <TableCell>Available Stock</TableCell>
                                        <TableCell>Price</TableCell>
                                        <TableCell>Select</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {groupedProducts.map((productGroup, groupIndex) => (
                                        <TableRow key={`group-${groupIndex}`}>
                                            <TableCell>
                                                <img src={productGroup.image || ''} alt={productGroup.title} style={{ maxWidth: '80px', height: '80px', objectFit: 'cover' }} />
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="subtitle1" fontWeight="bold">{productGroup.title}</Typography>
                                                <Typography variant="body2" color="textSecondary">{productGroup.body_html}</Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                                    {productGroup.variants.map((variant) => (
                                                        <Box key={variant.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                            <Chip
                                                                label={`${variant.option_1_value || 'Default'} ${variant.option_2_value ? '/ ' + variant.option_2_value : ''}`}
                                                                size="small"
                                                                variant="outlined"
                                                            />
                                                            <Typography variant="caption">SKU: {variant.sku}</Typography>
                                                        </Box>
                                                    ))}
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                                    {productGroup.variants.map((variant) => (
                                                        <Typography key={variant.id} variant="body2">
                                                            {variant.quantity || 0} units
                                                        </Typography>
                                                    ))}
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                                    {productGroup.variants.map((variant) => (
                                                        <Typography key={variant.id} variant="body2">
                                                            ${variant.price || 0}
                                                        </Typography>
                                                    ))}
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                                    {productGroup.variants.map((variant) => (
                                                        <input
                                                            key={variant.id}
                                                            type="checkbox"
                                                            checked={selectedProducts.some(item => item.id === variant.id)}
                                                            onChange={() => handleCheckboxChange(variant)}
                                                        />
                                                    ))}
                                                </Box>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleSaveSelectedProducts} color="primary">Save</Button>
                        <Button onClick={handleCloseDialog} color="primary">Close</Button>
                    </DialogActions>
                </Dialog>
                <Dialog open={openTrackingDialog} onClose={handleTrackingDialogClose}>
                    <DialogTitle>Add Tracking</DialogTitle>
                    <DialogContent>
                        <TextField
                            label="Channel Order Id"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={channelOrderId}
                            onChange={(e) => setChannelOrderId(e.target.value)}
                        />
                        <TextField
                            label="Original Channel Order Id"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={originalChannelOrderId}
                            onChange={(e) => setOriginalChannelOrderId(e.target.value)}
                        />
                        <TextField
                            label="Channel Code"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={channelCode}
                            onChange={(e) => setChannelCode(e.target.value)}
                        />
                        <TextField
                            label="SKU EAN"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={skuEan}
                            onChange={(e) => setSkuEan(e.target.value)}
                        />
                        <TextField
                            label="SKU Code"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={skuCode}
                            onChange={(e) => setSkuCode(e.target.value)}
                        />
                        <TextField
                            label="Tracking"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={tracking}
                            onChange={(e) => setTracking(e.target.value)}
                        />
                        <TextField
                            label="Shipment Date"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={shipmentDate}
                            onChange={(e) => setShipmentDate(e.target.value)}
                        />
                        <TextField
                            label="Carrier"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={carrier}
                            onChange={(e) => setCarrier(e.target.value)}
                        />
                        <TextField
                            label="Ship URL"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={shipUrl}
                            onChange={(e) => setShipUrl(e.target.value)}
                        />
                        <TextField
                            label="Is Return"
                            variant="outlined"
                            fullWidth
                            size="small"
                            value={isReturn}
                            onChange={(e) => setIsReturn(e.target.value)}
                        />
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleTrackingSubmit} color="primary">Submit</Button>
                        <Button onClick={handleTrackingDialogClose} color="primary">Cancel</Button>
                    </DialogActions>
                </Dialog>
            </Grid>
        </>
    );
};
