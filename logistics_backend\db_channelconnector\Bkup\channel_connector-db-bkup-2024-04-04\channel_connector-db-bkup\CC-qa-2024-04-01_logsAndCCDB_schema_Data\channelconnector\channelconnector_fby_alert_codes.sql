-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `fby_alert_codes`
--

DROP TABLE IF EXISTS `fby_alert_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fby_alert_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fby_alert_codes`
--

LOCK TABLES `fby_alert_codes` WRITE;
/*!40000 ALTER TABLE `fby_alert_codes` DISABLE KEYS */;
INSERT INTO `fby_alert_codes` VALUES (1,'Stock','STOCK','2022-01-28 17:10:50'),(2,'Order','ORDER','2022-01-28 17:10:50'),(3,'Missing','MISSING','2022-01-28 17:10:50'),(4,'Import','IMPORT','2022-01-28 17:10:50'),(5,'Export','EXPORT','2022-01-28 17:10:50'),(6,'Update','UPDATE','2022-01-28 17:10:50'),(7,'Address','ADDRESS','2022-01-28 17:10:50'),(8,'Unknown','UNKNOWN','2022-01-28 17:10:50'),(9,'Notify','NOTIFY','2022-01-28 17:10:50'),(10,'Order documents','ORDER DOCUMENTS','2022-01-28 17:10:50'),(11,'Post Insert Order Actions','POST INSERT ORDER ACTIONS','2022-01-28 17:10:50'),(12,'Shipment action','SHIPMENT','2022-01-28 17:10:50'),(13,'Returned action','RETURNED','2022-01-28 17:10:50'),(14,'Refund','REFUND','2022-01-28 17:10:51'),(15,'Iupiter','IUPITER','2022-01-28 17:10:51'),(16,'Missing configuration','MISSING CONFIGURATION','2022-01-28 17:10:51');
/*!40000 ALTER TABLE `fby_alert_codes` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:51:41
