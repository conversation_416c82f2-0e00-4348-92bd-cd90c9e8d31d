DROP PROCEDURE IF EXISTS channelconnector.updateOrderStatus;

DELIMITER $$
CREATE PROCEDURE channelconnector.`updateOrderStatus`(
	`in_fby_id` VARCHAR(128),
	`in_order_no` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME,
	`in_checktime` DATETIME,
	`in_isCheckafter48Hr` tinyint(4)
)
BEGIN
	/*
    SELECT * FROM 		order_details as o;
    
    call channelconnector.updateOrderStatus(8,'4667064942850','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW());
    
    call channelconnector.updateOrderStatus(8,'4666393428226','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW());
    
    */
	DECLARE is_updated TINYINT;
	DECLARE order_item_count_total INT;
	DECLARE order_item_count_sucess INT;
	DECLARE order_item_count_pending INT;
	
    SET is_updated = 0;
    SET order_item_count_total = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		
	) ;
     SET order_item_count_sucess = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 1
	) ;
     SET order_item_count_pending = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 0
	) ;
    
     
   #IF (order_item_count_pending IS NULL  OR order_item_count_pending = 0 )     
   #THEN  
		SET SQL_SAFE_UPDATES = 0;
		UPDATE order_masters 
		SET 
			fby_send_status = CASE WHEN in_isCheckafter48Hr = 1 THEN 0 ELSE 1 END,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = in_time,
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND fby_send_status = 0;
		 SET is_updated = 1;
		SET SQL_SAFE_UPDATES = 1;
 #END IF;
 
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		checked_at = in_checktime,
		IsCheckAfter48Hours = in_isCheckafter48Hr
	WHERE
		fby_user_id = in_fby_id
		AND order_no = in_order_no;
 
 SELECT 
    is_updated,
    order_item_count_total as order_items_total,
    order_item_count_sucess AS synced,
    order_item_count_pending AS pending;

 
END$$
DELIMITER ;
