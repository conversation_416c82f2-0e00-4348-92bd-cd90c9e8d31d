DROP PROCEDURE IF EXISTS channelconnector.getCanceledOrderDetails;

DELIMITER $$
CREATE PROCEDURE `getCanceledOrderDetails`(
	`in_fby_id` VARCHAR(128), 
	`in_acount_id` INT(11), 
	`in_chanel_code` VARCHAR(50), 
	`in_chanel` VARCHAR(100)

)
BEGIN
	SELECT 
		od.*
	FROM
		order_details AS od
	INNER JOIN _2_channel as ch
    on ch.channelId = od.fby_user_id 
    and ch.channelId = in_fby_id
	WHERE
		(od.payment_status = 'partially_refunded'
			OR od.payment_status = 'refunded'
            OR od.payment_status = '7'
			OR od.payment_status like '%cancel%'
            OR od.order_status like '%cancel%'
            OR od.order_status = 'partially_refunded'
			OR od.order_status = 'refunded'
            OR od.order_status = '7'
		)
		AND (od.is_canceled_fby = 0 OR od.is_canceled_fby IS NULL)
		-- AND od.channel = in_chanel
		-- AND od.channel_code = in_chanel_code
		-- AND od.account_id = in_acount_id
        AND od.purchase_date > ch.orderSyncStartDate
		AND od.fby_user_id = in_fby_id;
END$$
DELIMITER ;
