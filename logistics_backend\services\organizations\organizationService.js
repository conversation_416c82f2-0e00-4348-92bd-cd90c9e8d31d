const dbpool = require('../../startup/db.js');
const miscConstants = require("../../misc/constants.js");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers.js');
const logger = require('../../misc/logger.js');
const common = require("../../server/constants/common.js");
const ccDB = process.env.DB_DATABASE || "channelconnector";

class OrganizationService {
    /**
     * 
     * @param {*} organizationData 
     * @returns 
     */
    async createOrganization(organizationData) {
        try{
            const { name, domain, logoUrl, orderVolumes, themeColors } = organizationData;
        
            const query= `${ccDB}.CreateOrganization`;
            const [result] = await dbpool.executeProcedure(query,
                [name, domain, logoUrl, orderVolumes, JSON.stringify(themeColors)], 
                ccDB
            );
            const createdDetails = {
                id: result[0].organization_id,
                ...organizationData
            };

            await this.invalidateOrganizationCache();

            return createdDetails;

        } catch (error) {
            console.error('Error in Create Organization: ', error);
            logger.logError('Error in Create Organization: ', error);
            throw new Error(error.message || 'Organization registration failed');
        }     
    }

    /**
     * 
     * @param {*} id 
     * @returns 
     */
    async getOrganization(id) {
        try {
            const query= `${ccDB}.GetOrganization`;
            const [rows] = await dbpool.executeProcedure(query, [id], ccDB);
            return rows[0];
        } catch (error) {
            throw error;
        }    
    }

    /**
     * get All organizations
     * @returns 
     */
    async getAllOrganizations(pageNum, pageSizeNum) {
        try {
            const cacheKey = cache.generateKey(miscConstants.CACHE.ORGANIZATIONS.KEY, 'all');
            const CACHE_TTL = miscConstants.CACHE.ORGANIZATIONS.TTL;
            const cachedData = await cache.get(cacheKey);
            if (cachedData) {
                logger.logInfo(`Cache hit for organization`);
                return cachedData;
            }
            // If not in cache, get from database
            logger.logInfo(`Cache miss for all organizations, fetching from DB`);
            const query= `${ccDB}.GetAllOrganizations`;
            const rows = await dbpool.executeProcedure(query, [pageNum, pageSizeNum], ccDB);
            if (!rows[0] || rows[0].length === 0) {
                return null;
            }
            const organizations = rows[0];
            // Store in cache
            await cache.set( cacheKey, organizations, CACHE_TTL );
            return organizations;
        } catch (error) {
            throw error;
        }    
    }


    /**
     * 
     * @param {*} orgId 
     * @returns 
     */
    async getOrganizationData(orgId) {
        
        try {

            const cacheKey = cache.generateKey(miscConstants.CACHE.ORGANIZATIONS.KEY, orgId);
            const CACHE_TTL = miscConstants.CACHE.ORGANIZATIONS.TTL;
            const cachedData = await cache.get(cacheKey);

            // if (cachedData) {
            //     logger.logInfo(`Cache hit for organization ${orgId}`);
            //     return cachedData;
            // }

            // If not in cache, get from database
            logger.logInfo(`Cache miss for organization ${orgId}, fetching from DB`);
            
            const query= `${ccDB}.GetOrganizationData`;
            const [rows] = await dbpool.executeProcedure(query, [orgId], ccDB);
            if (!rows[0] || rows[0].length === 0) {
                return null;
            }

            const organizationData = this.transformOrganizationData(rows[0]);

            // Store in cache
            await cache.set( cacheKey, organizationData, CACHE_TTL );

            return organizationData;
        } catch (error) {
            logger.logError('Error fetching organization data:', error);
            throw new Error('Failed to fetch organization data');
        }
    }
    /**
     * 
     * @param {*} details 
     * @param {*} userId 
     * @returns 
     */
    async createOrgDetails(details, userId) {
        try {
            const query = `${ccDB}.CreateOrganizationDetails`;
    
            // Destructure `details` safely
            const {
                organizationId,
                gstNumber,
                aboutContent,
                privacyPolicy,
                contactPerson,
                support = {},
                contactAddress = {}, 
            } = details;
    
            // Destructure `support` and `contactAddress` with fallbacks
            const {
                email: supportEmail,
                phone: supportPhone,
                hours: supportHours
            } = support;
    
            const {
                addressLine1,
                addressLine2,
                city,
                state,
                zip,
                country
            } = contactAddress;
    
            // Execute the stored procedure
            const [result] = await dbpool.executeProcedure(query, [
                organizationId,
                gstNumber,
                aboutContent || null,
                privacyPolicy || null, 
                contactPerson,
                supportEmail,
                supportPhone,
                supportHours,
                addressLine1,
                addressLine2,
                city,
                state,
                zip,
                country,
                userId
            ]);
    
            if (!result || result.length === 0) {
                throw new Error('Failed to create organization details');
            }
    
            const createdDetails = {
                id: result[0].detail_id,
                ...details
            };
    
            // Prepare user data for the signup function
            const userData = {
                name: contactPerson,
                email: supportEmail,
                mobile: supportPhone,
                password: null,
                groupCode: process.env.ORGANIZATION_ADMIN_GROUP_CODE || 8,
                clientId: null,
                organizationId,
                roleId: process.env.ORGANIZATION_ADMIN_ROLE_ID || 3, // Role ID for organization admin
                createdBy: userId
            };
    
            // Register the organization admin using common.signup
            const signupResult = await common.signup(userData);
    
            if (signupResult?.error) {
                throw new Error(`Failed to create admin user: ${signupResult.error.data || signupResult.error}`);
            }
            
            return createdDetails;
        } catch (error) {
            console.error('Error in createOrgDetails:', error);
            throw new Error(error.message || 'Add organization details failed');
        }
    }

    async getOrgPage(organizationId, pageType) {
        
        try {
            const cacheKey = cache.generateKey(`${miscConstants.CACHE.ORG_PAGE.KEY}${organizationId}`, pageType);
            const CACHE_TTL = miscConstants.CACHE.ORG_PAGE.TTL;
            let page = await cache.get(cacheKey);

            if (!page) {
                const query= `${ccDB}.GetOrganizationPages`;
                const [rows] = await dbpool.executeProcedure(query, [organizationId, pageType], ccDB);
                page = rows[0];
                
                if (page) {
                    await cache.set(cacheKey, page, CACHE_TTL);
                }
            }

            return page;
        } catch (error) {
            logger.logError('Error fetching organization pages:', error);
            throw error;
        }    
    }

    async updateOrgPage(pageData) {
        try {
           
            const query= `${ccDB}.UpdateOrganizationPage`;
            const result = await dbpool.executeProcedure(query,
                [
                    pageData.organizationId,
                    pageData.pageType,
                    pageData.title,
                    pageData.content,
                    pageData.metaDescription,
                    pageData.isPublished
                ],
                ccDB
            );
    
            const updatedPage = {
                organizationId: pageData.organizationId,
                pageType: pageData.pageType,
                ...pageData
            };
    
            // Update cache
            const cacheKey = cache.generateKey(`${miscConstants.CACHE.ORG_PAGE.KEY}${pageData.organizationId}`, pageData.pageType);
            const CACHE_TTL = miscConstants.CACHE.ORG_PAGE.TTL;

            await cache.set(cacheKey, updatedPage, CACHE_TTL);
    
            return updatedPage;

        } catch (error) {
            throw error;
        }
       
    }

    transformOrganizationData(rawData) {
        // Transform the flat data into a structured object
        return {
            id: rawData.id,
            name: rawData.name,
            domain: rawData.domain,
            logoUrl: rawData.logo_url,
            isActive: rawData.is_active,
            createdAt: rawData.created_at,
            updatedAt: rawData.updated_at,
            details: {
                id: rawData.detail_id,
                gstNumber: rawData.gst_number,
                aboutContent: rawData.about_content,
                privacyPolicy: rawData.privacy_policy,
                support: {
                    email: rawData.support_email,
                    phone: rawData.support_phone,
                    hours: rawData.support_hours
                },
                address: {
                    line1: rawData.addressLine1,
                    line2: rawData.addressLine2,
                    city: rawData.city,
                    state: rawData.state,
                    zip: rawData.zip,
                    country: rawData.country
                },
                createdBy: rawData.created_by,
                createdAt: rawData.details_created_at,
                updatedBy: rawData.updated_by,
                updatedAt: rawData.details_updated_at
            }
        };
    }

    async invalidateOrganizationCache() {
        try {
            await cache.delete(cache.generateKey(miscConstants.CACHE.ORGANIZATIONS.KEY, 'all'));
            logger.logInfo(`Cache invalidated for organizations`);
        } catch (error) {
            logger.logError('Error invalidating organization cache:', error);
        }
    }

}

module.exports = new OrganizationService();