CREATE TABLE `products` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `fby_user_id` VARCHAR(128) COLLATE UTF8_BIN DEFAULT NULL,
    `channel` VARCHAR(20) COLLATE UTF8_BIN DEFAULT NULL,
    `domain` VARCHAR(64) COLLATE UTF8_BIN DEFAULT NULL,
    `owner_code` VARCHAR(20) COLLATE UTF8_BIN DEFAULT NULL,
    `sku` VARCHAR(128) COLLATE UTF8_BIN DEFAULT NULL,
    `barcode` VARCHAR(128) COLLATE UTF8_BIN DEFAULT NULL,
    `item_id` VARCHAR(127) COLLATE UTF8_BIN DEFAULT NULL,
    `title` VARCHAR(128) COLLATE UTF8_BIN DEFAULT NULL,
    `item_product_id` VARCHAR(127) COLLATE UTF8_BIN DEFAULT NULL,
    `inventory_item_id` VARCHAR(127) COLLATE UTF8_BIN DEFAULT NULL,
    `location_id` VARCHAR(256) COLLATE UTF8_BIN NOT NULL DEFAULT '0',
    `previous_inventory_quantity` INT(11) DEFAULT NULL,
    `inventory_quantity` INT(11) DEFAULT NULL,
    `image` TEXT COLLATE UTF8_BIN DEFAULT NULL,
    `price` DECIMAL(10 , 2 ) DEFAULT NULL,
    `count` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `fby_error_flag` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `status` TINYINT(4) NOT NULL DEFAULT 0,
    `cron_name` VARCHAR(60) COLLATE UTF8_BIN DEFAULT NULL,
    `cron_id` VARCHAR(100) COLLATE UTF8_BIN DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    `updated_at` DATETIME DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `fby_user_id` (`fby_user_id` , `sku` , `item_product_id` , `title` , `inventory_item_id` , `item_id`)
)  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE = UTF8_BIN;
/*
# DROP TABLE `channelconnector`.`prestashop_account`;

TRUNCATE `channelconnector`.`prestashop_account`;
*/

SELECT 
    *
FROM
    channelconnector.prestashop_account;
