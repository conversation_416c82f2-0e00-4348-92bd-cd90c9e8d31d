const helpers = require('../../../misc/helpers');
const miscConstants = require("../../../misc/constants");
const ClientsRatesService = require('../../../services/hcl/clientsRatesService.js');

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.getClientZonesRates = async (req, res, next) => {
    try {
        const { clientId, shippingProviderId = 1, orderType, mode } = req.query;
       
        if (!clientId || !orderType || !mode) {
            helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.BAD_REQUEST, 
                "ClientId, ordertype and mode are required!", 
                req.query
            );
        }
        const clientRates = await ClientsRatesService.getClientZonesRates(clientId, shippingProviderId, orderType, mode);
        helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            clientRates, 
            req.query
        ); 
    } catch (error) {
        helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.upsertClientZonesRates = async (req, res, next) => {
    try {
        const { clientId, shippingProviderId, mode, orderType, weightSlabId, rates } = req.body;
        if (!Array.isArray(rates) || rates.length === 0) {
            helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.BAD_REQUEST,
                "Invalid data format. 'rates' should be a non-empty array.", 
                req.body
            );
        }

        const result = await ClientsRatesService.upsertClientZonesRates(
            {clientId, shippingProviderId, mode, orderType, weightSlabId, rates}, 
            req.user.id
        );
       return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result
        ); 

    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */

exports.getCodRateByClient = async (req, res, next) => {
    try {
        const { clientId, shippingProviderId = 1 } = req.query;
        const codRates = await ClientsRatesService.getCodRateByClient(clientId, shippingProviderId);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            codRates
        ); 
    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query);
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.upsertClientCODRates = async (req, res, next) => {
    try {
        const { clientId, shippingProviderId, codAmount, codPercentage, flueSurcharge = 0, docketCharges = 0 } = req.body;
        const result = await ClientsRatesService.upsertClientCODRates(
            clientId, 
            shippingProviderId,
            codAmount,
            codPercentage, 
            flueSurcharge, 
            docketCharges,
            req.user.id);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result
        ); 
    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */

exports.getClientAdditionalRates = async (req, res, next) => {
    try {
        const { clientId, shippingProviderId, orderType} = req.query;
        const additionalRates = await ClientsRatesService.getClientAdditionalRates(clientId, shippingProviderId, orderType);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            additionalRates
        ); 
    } catch (error) {
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.query);
    }
    next();
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @param {*} next 
 */
exports.upsertClientAdditionalRates = async (req, res, next) => {
    try {
        
        const result = await ClientsRatesService.upsertClientAdditionalRates(req.body, req.user.id);

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result
        ); 

    } catch (error) {

        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );

    }
    next();
};


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @returns 
 */
exports.upsertClientProvider = async (req, res) => {
    try {
        const { clientId, providerId, orderType, isProviderRates, priceIncrement, isEnabled, effectiveDate } = req.body;
        const userid = req.user.id;
        // Validate request body
        if (!clientId || !providerId || !orderType) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.BAD_REQUEST,
                'client ID, Provider ID, orderType and price increment fields are required'
            );
        }

        const provider = await ClientsRatesService.upsertClientProvider(
            {   
                clientId,
                providerId,
                orderType,
                isProviderRates,
                priceIncrement,
                isEnabled,
                effectiveDate
            },
            userid
        );

        return helpers.sendSuccess(
            res,
            miscConstants.HTTPSTATUSCODES.OK,
            provider.message,
            provider.providerData
        );
    } catch (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
            error.message
        );
    }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getClientProviders = async (req, res) => {
    try {
        const {clientId, providerId} = req.query;
        const providers = await ClientsRatesService.getClientProviders(clientId, providerId);

        if(!providers || providers.length === 0) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.NOT_FOUND,
                miscConstants.ERRORCODES.NOT_FOUND,
                miscConstants.ERRORMESSAGES.NOT_FOUND,
                req.query
            );
        }
        return helpers.sendSuccess(
            res,
            miscConstants.HTTPSTATUSCODES.OK,
            miscConstants.SUCESSSMESSAGES.GET,
            providers
        );
    } catch (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
            error.message,
            req.query
        );
    }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getClientAllEnabledProviders = async (req, res) => {
    try {
        const {clientId} = req.query;
        const providers = await ClientsRatesService.getEnabledProviders(clientId);

        if(!providers || providers.length === 0) {
            return helpers.sendError(
                res,
                miscConstants.HTTPSTATUSCODES.NOT_FOUND,
                miscConstants.ERRORCODES.NOT_FOUND,
                miscConstants.ERRORMESSAGES.NOT_FOUND,
                req.query
            );
        }
        return helpers.sendSuccess(
            res,
            miscConstants.HTTPSTATUSCODES.OK,
            miscConstants.SUCESSSMESSAGES.GET,
            providers
        );
    } catch (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR,
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,
            error.message,
            req.query
        );
    }
};


