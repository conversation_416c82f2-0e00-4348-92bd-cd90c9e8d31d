DROP PROCEDURE IF EXISTS channelconnector._2_channel_Delete;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_2_channel_Delete`(
	`in_channelId` INT(11)
)
BEGIN
	/*
		call channelconnector._2_channel_Get(1002);
        
        call channelconnector.`getShopifyUser`(1002);
    
		call channelconnector.`_2_channel_Delete`(1002);
        
        SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 1,
                `isEnabled` = 1,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = 1002 ;
		SET SQL_SAFE_UPDATES = 1;
        
		
    */
    DECLARE isExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
   
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel  
        WHERE 
			`channelId` = `in_channelId` 
            AND `isActive` = 1 
			LIMIT 1
    );
    
    /*
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    */
    
    IF(isDeletedExists = 1)
    THEN
		DELETE FROM channelconnector._2_channel 
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0;
    END IF;
    
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
		UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 0,
                `isEnabled` = 0,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = `in_channelId` 
				AND `isActive` = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT 
				T1.`channelId` ,
				T1.`groupCode` ,
				T1.`currencyCode` ,
				T1.`ownerCode` ,
				T1.`channelCode`  ,
				T1.`channelName` ,
				T1.`domain`  ,
				T1.`username` ,
				T1.`password`  ,
				T1.`apiKey`  ,
				T1.`secret` ,
				T1.`token`  ,
				T1.`isActive`  ,
				T1.`isEnabled` ,
				CAST(T1.`createdOn` as CHAR) as createdOn,
				CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                1 as Deleted
		FROM
			`channelconnector`.`_2_channel` as T1
		WHERE
			T1.`channelId` = `in_channelId` 
		ORDER BY T1.`id` DESC
		LIMIT 1;
            
    END IF;
    
END$$
DELIMITER ;
