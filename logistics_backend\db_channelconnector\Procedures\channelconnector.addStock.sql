DROP PROCEDURE IF EXISTS channelconnector.addStock;

DE<PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector.`addStock`
(
		IN `in_skuid` VARCHAR(128) ,
        IN `in_skucode` VARCHAR(128),
        IN `in_ean` VARCHAR(128) ,
        IN `in_qntity` INT(11),
        IN `in_priority` INT(11),
        IN `in_crnid` VARCHAR(100) ,
        IN `in_fby_id` VARCHAR(100) 
)
BEGIN
	IF (
        SELECT 1 FROM temp_master_inventory AS T 
		WHERE 
			T.sku_id = `in_skuid` 
		LIMIT 1 
    ) = 1 
    THEN
    SET SQL_SAFE_UPDATES = 0;
		DELETE T FROM temp_master_inventory AS T 
        WHERE 
			T.sku_id = `in_skuid`; 
	SET SQL_SAFE_UPDATES = 1;
    END IF;
    
	INSERT IGNORE INTO temp_master_inventory(
		sku_id,skucode,barcode,quantity,priority,cron_id, 
        fby_user_id
	) 
    VALUES(
		in_skuid,in_skucode,in_ean,in_qntity,in_priority,in_crnid,
        in_fby_id
	);
    
END$$
DELIMITER ;
