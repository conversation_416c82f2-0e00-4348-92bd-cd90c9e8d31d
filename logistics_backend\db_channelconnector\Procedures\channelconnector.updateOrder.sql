DROP PROCEDURE IF EXISTS channelconnector.updateOrder;

DEL<PERSON>ITER $$
CREATE PROCEDURE channelconnector.updateOrder
(
	IN `in_crn_name` VARCHAR(60),
	IN `in_crnid` VARCHAR(100),
	IN `in_time` DATETIME,
	IN `in_tracking_id` VARCHAR(256),
	IN `in_carrier` VARCHAR(256),
	IN `in_url` TEXT,
	IN `in_channel_order_id` VARCHAR(256),
	IN `in_channel_code` VARCHAR(20),
	IN `in_barcode` VARCHAR(256),
    IN `in_fby_user_id` VARCHAR(256)
) 
BEGIN
	DECLARE var_channel_name varchar(256);
    SET var_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	/*
    
		call channelconnector.`updateOrder`(
		'get_track_number',
	  '9662f8d6-c031-4f15-bb6e-d0e00c74feab',
	  '2022-02-09 21:22:10',
	  'B12345678904231',
	  'SDA',
	  'https://www.swiship.it/t/D6zCc37CL',
	  'FSHREXMYR',
	  'SHIT',
	  '',
	  23
		)
    
    
    Select * from order_details od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
	 
        
   Select * from order_masters om
    WHERE
		om.order_no = in_channel_order_id;
      #*/ 
    IF(in_barcode = '' OR in_barcode IS NULL)    
    THEN
		SET in_barcode = null;
	ELSE
		SET in_barcode = trim(in_barcode);
    END IF;
    
    SET SQL_SAFE_UPDATES = 0;    
    /*
    SELECT in_channel_code,in_channel_order_id,in_barcode;
    
    SELECT * FROM order_details AS od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
    #*/    
    #SELECT in_tracking_id,in_carrier,in_url,in_barcode,in_channel_order_id as order_no,in_channel_code;
    
	UPDATE order_details AS od 
	SET 
		od.tracking_id = in_tracking_id,
		od.tracking_courier = in_carrier,
		od.tracking_url = in_url,
		od.is_trackable = 1,
		od.cron_name = in_crn_name,
		od.cron_id = in_crnid,
		od.updated_at = NOW(),
		od.barcode = CASE
        WHEN
            in_barcode IS NOT NULL
                AND in_barcode <> ''
        THEN
            TRIM(in_barcode)
        ELSE TRIM(od.barcode)
    END
	WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (
							TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
							OR (TRIM(od.order_no) = TRIM(in_channel_order_id))
						)
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL AND in_barcode<>'' THEN trim(in_barcode) ELSE trim(od.barcode) end;
		
    update order_masters om
    SET om.order_status = "fulfiled",
		om.cron_name = in_crn_name,
		om.cron_id = in_crnid,
		om.updated_at = NOW()
	WHERE
		om.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     
	SELECT * FROM order_details AS od
    WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
					OR (TRIM(od.order_no) = TRIM(in_channel_order_id)))
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
   /*     
	SELECT * FROM order_masters om
    WHERE
		od.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     */
	SET SQL_SAFE_UPDATES = 1;	
END$$
DELIMITER ;