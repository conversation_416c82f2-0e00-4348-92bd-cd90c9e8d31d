-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `createdproductvariants`
--

DROP TABLE IF EXISTS `createdproductvariants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `createdproductvariants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `channel` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `sku` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `item_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `title` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `item_product_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int DEFAULT NULL,
  `inventory_quantity` int DEFAULT NULL,
  `image` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int unsigned NOT NULL DEFAULT '0',
  `fby_error_flag` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `cron_name` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `isChanged` tinyint DEFAULT '0',
  `description` text,
  `specialPrice` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `createdproductvariants_fby_user_id` (`fby_user_id`,`sku`,`item_product_id`,`title`,`inventory_item_id`,`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=148 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `createdproductvariants`
--

LOCK TABLES `createdproductvariants` WRITE;
/*!40000 ALTER TABLE `createdproductvariants` DISABLE KEYS */;
INSERT INTO `createdproductvariants` VALUES (15,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFR14426_I1005_CF1','7612901669526','42957608747251','Binda Breil CF1','7731702661363','45053227270387','0',-1,0,'',221.00,0,0,0,'create_ProductVariant_Shopify','75ea188c-522c-4a1e-9daf-8adac0a84b7c','2022-07-07 08:45:18',NULL,0,NULL,NULL),(16,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFQ14479_S398_F9','7612901672311','42957608780019','MY PRODUCT','7731702628595','45053227303155','0',-1,0,'37708567019763',200.00,0,0,0,'create_ProductVariant_Shopify','75ea188c-522c-4a1e-9daf-8adac0a84b7c','2022-07-07 08:45:18',NULL,0,NULL,NULL),(17,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFS14421_S398_F9','7612901677255','42957608812787','Binda Breil 1','7731702595827','45053227335923','0',-1,0,'37708566692083',220.00,0,0,0,'create_ProductVariant_Shopify','75ea188c-522c-4a1e-9daf-8adac0a84b7c','2022-07-07 08:45:18',NULL,0,NULL,NULL),(18,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFR14426_I1005_CF2','7612901669533','42957608845555','Binda Breil CF2','7731702661363','45053227368691','0',-1,0,'',211.00,0,0,0,'create_ProductVariant_Shopify','75ea188c-522c-4a1e-9daf-8adac0a84b7c','2022-07-07 08:45:18',NULL,0,NULL,NULL),(19,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFR14426_I1005_CF0','7612901669519','42957608878323','Bina Breil CF0','7731702661363','45053227401459','0',-1,0,'37708566266099',201.00,0,0,0,'create_ProductVariant_Shopify','75ea188c-522c-4a1e-9daf-8adac0a84b7c','2022-07-07 08:45:19',NULL,0,NULL,NULL),(53,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','note 11','redmi-10','48165223891279','note 11','8874364469583','50263338647887','67998187763',-1,10,'53349627035983',21001.00,0,0,0,'push_Images_Shopify','b328a5ce-d21d-40f1-879f-0a9ed8ba57d7','2024-01-10 06:10:26','2024-03-06 09:28:09',0,'',21001),(54,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','note 10','redmi-10','48165269995855','note 10','8874364469583','50263384752463','67998187763',-1,11,'53349790548303',22001.00,0,0,0,'push_Images_Shopify','312c394b-ddc0-42d2-bc93-eef58c140eec','2024-01-10 06:26:18','2024-03-06 09:28:14',0,'',22001),(55,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','realmi 10','realmi-11','48165272420687','realmi 10','8874364502351','50263387144527','67998187763',-1,8,'53349800247631',22001.00,0,0,0,'push_Images_Shopify','312c394b-ddc0-42d2-bc93-eef58c140eec','2024-01-10 06:27:14','2024-02-05 10:12:16',0,'',22001),(57,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','swift-1','swift','48175958098255','swift-1','8877828276559','50274083537231','67998187763',-1,5,'53383108460879',45001.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','155b6d7d-95a9-473c-a63a-82d305d01eee','2024-01-12 03:42:30','2024-02-05 10:12:16',0,'',45001),(59,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','swift-2','swift','48175958131023','swift-2','8877828276559','50274083569999','67998187763',-1,9,'53383117373775',41001.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','155b6d7d-95a9-473c-a63a-82d305d01eee','2024-01-12 03:43:12','2024-02-05 10:12:16',0,'',41001),(93,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','promax-black','promaxblack##','48246786916687','promax-black','8904497725775','50344659681615','67998187763',-1,7,'53685650784591',119001.00,0,0,0,'push_Images_Shopify','953412e9-e7c7-4b96-9901-823236cc693d','2024-01-29 06:16:25','2024-03-04 05:50:33',0,'',119001),(95,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','promax-white','promaxwhite##','48246787244367','promax-white','8904497725775','50344660009295','67998187763',-1,4,'53685652259151',118001.00,0,0,0,'push_Images_Shopify','953412e9-e7c7-4b96-9901-823236cc693d','2024-01-29 06:16:32','2024-03-04 05:00:10',0,'',118001),(126,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','pro-white','problack##','48277576548687','pro-white','8904497758543','50375579992399','67998187763',-1,6,'53831928545615',110000.00,0,0,0,'push_Images_Shopify','66713380-a8cc-4859-865d-1315918d38b2','2024-02-06 09:00:52','2024-02-06 09:00:54',0,'',NULL);
/*!40000 ALTER TABLE `createdproductvariants` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:51:20
