
CREATE TABLE `prestashop_account` (
  `id` int(11) NOT NULL,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `api_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `api_password` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `currency_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `group_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL
   PRIMARY KEY (`id`),
   UNIQUE KEY `fby_user_id` (`fby_user_id`,`domain`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;


/*
# DROP TABLE `channelconnector`.`prestashop_account`;

TRUNCATE `channelconnector`.`prestashop_account`;
*/

INSERT INTO `prestashop_account` 
(
	`id`, 
	`fby_user_id`, 
	`domain`, `api_key`, 
	`api_password`, 
	`channel_code`, 
	`currency_code`, 
	`owner_code`, 
	`group_code`, 
	`created_at`, 
	`updated_at`
) VALUES
(
	1, 
	'1001', 
	'prestashop.yocabe.com', 
	'EHQ8PK3NQXWJ5SKXG3DSLYZ62HRRJA27', 
	NULL, 
	'XXX', 
	'USD', 
	'yt', 
	'AEU', 
	'2022-02-07 17:03:10', 
	NULL
)

SELECT 
    *
FROM
    channelconnector.prestashop_account;
