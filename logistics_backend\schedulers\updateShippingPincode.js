// schedulers/updatePincodes.js
const cron = require('node-cron');
const { getShippingService } = require('../services/shippingPartners/partnerFactory');

cron.schedule('0 2 * * *', async () => {
  const partners = ['DTDC', 'Delhivery']; // Add more as needed
  for (const partner of partners) {
    const service = getShippingService(partner);
    await service.updatePincodes(); // Assuming each service has an updatePincodes method
  }
});
