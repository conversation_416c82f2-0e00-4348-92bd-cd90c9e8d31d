-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Dumping events for database 'channelconnector'
--

--
-- Dumping routines for database 'channelconnector'
--
/*!50003 DROP PROCEDURE IF EXISTS `addAlertCodes` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addAlertCodes`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_alert_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addAlertDomains` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addAlertDomains`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_alert_domains(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addAuthUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addAuthUser`(
	IN `in_name` VARCHAR(128), 
	IN `in_email` VARCHAR(128),
    IN `in_password` VARCHAR(1024),
	IN `in_groupCode` VARCHAR(128)

)
BEGIN
	INSERT INTO channelconnector._4_user_login(name, email, password,groupCode) 
    values(in_name,in_email,in_password,in_groupCode);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCancelReason` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCancelReason`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_cancel_reason(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCarrierDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCarrierDetails`(
 IN `in_fby_id` VARCHAR(128),
 IN `in_mirakl_carrier_code` VARCHAR(128),
 IN `in_label` VARCHAR(128),
 IN `in_tracking_url` TEXT
 )
BEGIN

DECLARE in_channel_name varchar(256);
DECLARE in_fby_mapped_tracking_carrier_code varchar(256);
DECLARE var_isUpdated tinyint;
DECLARE var_isExists tinyint;

SET transaction isolation level READ uncommitted;

SET in_channel_name = 
					(
						SELECT lower(channelName) FROM channelconnector._2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
						limit 1
					);
SET SQL_SAFE_UPDATES = 0;

SET in_fby_mapped_tracking_carrier_code =
					(
						SELECT fby_mapped_tracking_carrier_code FROM channelconnector.tracking_carrier_master_mapping WHERE fby_user_id = in_fby_id and mirakl_carrier_code = in_mirakl_carrier_code
						limit 1
					);

	IF EXISTS (
        SELECT 1 FROM tracking_carrier_master AS C 
		WHERE 
			C.mirakl_carrier_code = in_mirakl_carrier_code
		LIMIT 1 
        FOR UPDATE
    ) = 1 
    THEN
		-- Select 'Updated' as isExists;
        
		Update tracking_carrier_master C
		set label = in_label,
		tracking_url = in_tracking_url,
        updated_at = now()
		Where C.mirakl_carrier_code = in_mirakl_carrier_code;
            
         SET var_isUpdated = 1; 
         SET var_isExists = 1;
    ELSE
		-- Select 'Inserted' as isExists;
    
		INSERT INTO tracking_carrier_master(
			fby_user_id,mirakl_carrier_code,label,tracking_url,fby_mapped_tracking_carrier_code, created_at,updated_at
		) 
		VALUES(
			in_fby_id,in_mirakl_carrier_code,in_label,in_tracking_url,in_fby_mapped_tracking_carrier_code,now(),now()
			);
		
         SET var_isExists = 0;
	END IF;
    
      SET SQL_SAFE_UPDATES = 1;
      Select var_isExists as isExists,in_channel_name,var_isUpdated; 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addChannelCodes` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addChannelCodes`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_channel_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCreatedProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCreatedProduct`(
	`in_fby_user_id` VARCHAR(128), 
	`in_chanel` VARCHAR(20), 
	`in_domain` VARCHAR(1000), 
	`in_ownr_code` VARCHAR(20), 
	`in_sku` VARCHAR(128), 
	`in_barcode` VARCHAR(128), 
	`in_item_id` VARCHAR(127), 
	`in_title` VARCHAR(1000), 
	`in_item_product_id` VARCHAR(127), 
	`in_inventory_item_id` VARCHAR(127), 
	`in_old_quantity` INT, 
	`in_new_quantity` INT, 
	`in_image` TEXT, 
	`in_price` DECIMAL(10,2), 
	`in_crn_name` VARCHAR(100), 
	`in_crnid` VARCHAR(100), 
	`in_location_Id` VARCHAR(100)
    ,`in_description` TEXT
)
BEGIN
	SET in_old_quantity = -1;
    SET in_chanel = (
		Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );
    
	INSERT INTO
		createdproducts(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id,
            `description`
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id,
                in_description
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			price = in_price,
			location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
			previous_inventory_quantity =	in_old_quantity,
			inventory_quantity = in_new_quantity,
			`description` = in_description,
            image = in_image
		;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCreatedProductVariant` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCreatedProductVariant`(
`in_fby_user_id` VARCHAR(128),
`in_chanel` VARCHAR(20), 
`in_domain` VARCHAR(1000), 
`in_ownr_code` VARCHAR(20),
`in_sku` VARCHAR(128), 
`in_barcode` VARCHAR(128), 
`in_item_id` VARCHAR(127), 
`in_title` VARCHAR(1000), 
`in_item_product_id` VARCHAR(127),
`in_inventory_item_id` VARCHAR(127), 
`in_old_quantity` INT, 
`in_new_quantity` INT, 
`in_image` TEXT,
`in_price` DECIMAL(10,2), 
`in_crn_name` VARCHAR(100), 
`in_crnid` VARCHAR(100), 
`in_location_Id` VARCHAR(100),
`in_description` TEXT
 )
BEGIN
	 DECLARE isExists tinyint;
      SET isExists = (
			SELECT 
				1 
			FROM
				channelconnector.createproductvariants as cp
			WHERE
				cp.fby_user_id = in_fby_user_id
				AND sku = in_sku
                AND (
					cp.title <> in_title
					OR cp.price <> in_price
                    OR cp.`description` <> in_description
                )
			LIMIT 1
    );
	
    SET in_old_quantity = -1;
    SET in_chanel = (
        Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );


    
    IF (in_location_Id = 0 or in_location_Id is null)
    Then
		set in_location_Id = (select location_id from channelconnector.products where fby_user_id = in_fby_user_id and location_id > 0 limit 1);
	End IF;
    
    IF in_location_Id is null
    Then 
		set in_location_Id = 0;
    End IF;
    
    SET SQL_SAFE_UPDATES = 0;
    update createproductvariants
    SET isChanged = case when isExists = 1 then 1 else 0 end
    where fby_user_id = in_fby_user_id
    AND sku = in_sku
    AND barcode = in_barcode;
    
    
    INSERT INTO
        createdproductvariants(
            fby_user_id,
            channel,
            domain,
            owner_code,
            sku,
            barcode,
            item_id,
            title,
            item_product_id,
            inventory_item_id,
            previous_inventory_quantity,
            inventory_quantity,
            image,
            price,
            cron_name,
            cron_id,
            location_Id,
            `description`
        )
    VALUES
        (
                in_fby_user_id,
                in_chanel,
                in_domain,
                in_ownr_code,
                in_sku,
                in_barcode,
                in_item_id,
                in_title,
                in_item_product_id,
                in_inventory_item_id,
                in_old_quantity,
                in_new_quantity,
                in_image,
                in_price,
                in_crn_name,
                in_crnid,
                in_location_Id,
                in_description
        )
        ON DUPLICATE KEY
        UPDATE
            domain = in_domain,
            barcode = in_barcode,
            item_id = in_item_id,
            title = in_title,
            inventory_item_id = in_inventory_item_id,
            price = in_price,
            location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
            previous_inventory_quantity =    in_old_quantity,
            inventory_quantity = in_new_quantity,
            `description` = in_description,
            image = in_image,
            updated_at = now()
        ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCreateProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCreateProduct`(
`in_fby_user_id` VARCHAR(128), `in_chanel` VARCHAR(100), `in_domain` VARCHAR(1000), `in_ownr_code` VARCHAR(20), `in_sku` VARCHAR(128), `in_barcode` VARCHAR(128), `in_item_id` VARCHAR(127), `in_title` VARCHAR(1000), `in_item_product_id` VARCHAR(127), `in_inventory_item_id` VARCHAR(127), `in_old_quantity` INT, `in_new_quantity` INT, `in_image` TEXT, `in_price` DECIMAL(10,2), `in_crn_name` VARCHAR(100), `in_crnid` VARCHAR(100), `in_location_Id` VARCHAR(100)
,`in_description` text
)
BEGIN
	
	DECLARE isExists tinyint;
    DECLARE var_title VARCHAR(1000);
    DECLARE var_price DECIMAL(10,2);
    
    SET isExists = 0;
    
	SET in_old_quantity = -1;
    SET in_chanel = (
		Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
       
        limit 1
    );
    
    SET isExists = (
			SELECT 
				1 
			FROM
				channelconnector.createdproducts as cp
			WHERE
				cp.fby_user_id = in_fby_user_id
				AND sku = in_sku
                AND (cp.title <> in_title
				OR cp.price <> in_price
                OR cp.`description` <> in_description
                )
			LIMIT 1
    );
    
    
    SELECT isExists, var_title, var_price,in_description;
    
	IF (in_location_Id = 0 or in_location_Id is null)
    Then
		set in_location_Id = (select location_id from channelconnector.products where fby_user_id = in_fby_user_id and location_id > 0 limit 1);
	End IF;
	
	IF in_location_Id is null
    Then 
		set in_location_Id = 0;
    End IF;
    
	INSERT INTO
		createproducts(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id,
            `description`
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id,
                in_description
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			-- price = in_price,
			location_Id = in_location_Id,
			previous_inventory_quantity =	in_old_quantity,
			inventory_quantity = in_new_quantity,
            image = in_image,
            `description` = in_description,
			updated_at = now(),
            isChanged = case when isExists = 1 then 1 else 0 end
		;
        
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCreateProductVariant` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCreateProductVariant`(
`in_fby_user_id` VARCHAR(128), 
`in_chanel` VARCHAR(20), 
`in_domain` VARCHAR(1000), 
`in_ownr_code` VARCHAR(20), 
`in_sku` VARCHAR(128), 
`in_barcode` VARCHAR(128), 
`in_item_id` VARCHAR(127), 
`in_title` VARCHAR(1000), 
`in_item_product_id` VARCHAR(127), 
`in_inventory_item_id` VARCHAR(127), 
`in_old_quantity` INT, 
`in_new_quantity` INT, 
`in_image` TEXT, 
`in_price` DECIMAL(10,2), 
`in_crn_name` VARCHAR(100), 
`in_crnid` VARCHAR(100), 
`in_location_Id` VARCHAR(100),
`in_description` TEXT
)
BEGIN
	 
     /*
     CALL addCreateProductVariant(8,"SHEU","shopping190.myshopify.com","YT","BN_BRL_WFQ14479_S398_F9","7612901672311",null,"MY PRODUCT",null,null,0,0,null,null,"get_Fby_Products","bd32d4da-9f47-43d9-a3a3-d9b697de09ec",0,'description')
     */
     DECLARE isExists tinyint;
     
     
    SET isExists = (
			SELECT 
				1 
			FROM
				channelconnector.createproductvariants as cp
			WHERE
				cp.fby_user_id = in_fby_user_id
				AND sku = in_sku
                AND (
					cp.title <> in_title
					OR cp.price <> in_price
                    OR cp.`description` <> in_description
                )
			LIMIT 1
    );
    
	SET in_old_quantity = -1;
    SET in_chanel = (
		Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );
    
    IF (in_location_Id = 0 or in_location_Id is null)
    Then
		set in_location_Id = (select location_id from channelconnector.products where fby_user_id = in_fby_user_id and location_id > 0 limit 1);
	End IF;
    
	
	IF in_location_Id is null
    Then 
		set in_location_Id = 0;
    End IF;
    
	INSERT INTO
		createproductvariants(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id,
            `description`
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id,
                in_description
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			-- price = in_price,
			location_Id = in_location_Id,
			previous_inventory_quantity =	in_old_quantity,
			inventory_quantity = in_new_quantity,
            image = in_image,    
            `description` = in_description,
            updated_at = now(),
            isChanged = case when isExists = 1 then 1 else 0 end
		;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addCurrencyCodes` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addCurrencyCodes`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_currency_codes(name,code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addImages` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addImages`(
`in_image` VARCHAR(200), 
`in_sku` VARCHAR(100), 
`in_skuFamily` VARCHAR(200), 
`in_fby_user_id` int, 
`in_channel_image_id` varchar(128),
`in_image_order` int
)
BEGIN
	/*
    
		CALL addImages('https://images.unsplash.com/photo-1503602642458-232111445657?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxzZWFyY2h8OXx8cHJvZHVjdHxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
			'BN_BRL_WFS14421_S398_F9',
			'BN_BRL_WFS14421',
			8,
            0,
            1
            
        );
    */
	 DECLARE isExists tinyint;
     SET isExists =  (
			SELECT 
				1
			FROM
				Images AS i
			WHERE
				i.image = in_image 
                AND i.sku = in_sku
				AND i.skuFamily = in_skuFamily
				AND i.fby_user_id = in_fby_user_id
                
                
     );
     
    IF (isExists is null) then 
    
	INSERT INTO
		Images(
			image,
			sku,
			skuFamily,
            fby_user_id,
            channel_image_id,
            imageOrder
		)
	VALUES
		(
				in_image,
				in_sku,
				in_skuFamily,
                in_fby_user_id,
                in_channel_image_id,
                in_image_order
		);
			
    ELSE
    
        SET SQL_SAFE_UPDATES = 0;
        UPDATE Images
            SET image = in_image,
            sku = in_sku,
            skuFamily = in_skuFamily,
            channel_image_id = in_channel_image_id,
            imageOrder = in_image_order
            WHERE
        image = in_image 
                    AND sku = in_sku
        AND skuFamily = in_skuFamily
        AND fby_user_id = in_fby_user_id;
            
        
     
     END IF;
     
	SELECT 
		*
	FROM
		Images AS i
	WHERE
			i.image = in_image
            AND i.sku = in_sku
			AND i.skuFamily = in_skuFamily
			AND i.fby_user_id = in_fby_user_id;
           
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addOptions` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addOptions`(
IN `in_fby_user_id` VARCHAR(128),
IN `in_sku` VARCHAR(128),
IN `in_option_1_name` VARCHAR(128),
IN `in_option_1_value` VARCHAR(128),
IN `in_option_2_name` VARCHAR(128),
IN `in_option_2_value` VARCHAR(128)
)
BEGIN
    DECLARE isExists INT;
    SET isExists = 0;
    SET isExists = (
        SELECT 
            COUNT(*) 
        FROM
            channelconnector.option_table as ot
        WHERE
            ot.fby_user_id = in_fby_user_id
            AND ot.sku = in_sku
    );
    
    IF (isExists > 0) THEN
        SET isExists = (
            SELECT 
                COUNT(*) 
            FROM
                channelconnector.option_table
            WHERE
                fby_user_id = in_fby_user_id
                AND sku = in_sku
                AND option_1_name = in_option_1_name
                AND option_1_value = in_option_1_value
                AND option_2_name = in_option_2_name
                AND option_2_value = in_option_2_value
        );
        IF (isExists = 0) THEN
            UPDATE option_table
            SET 
                option_1_name = in_option_1_name,
                option_1_value = in_option_1_value,
                option_2_name = in_option_2_name,
                option_2_value = in_option_2_value
            WHERE
                sku = in_sku;
        END IF;
    ELSE
        INSERT INTO option_table (
            fby_user_id,
            sku,
            option_1_name,
            option_1_value,
            option_2_name,
            option_2_value
        ) VALUES (
            in_fby_user_id,
            in_sku,
            in_option_1_name,
            in_option_1_value,
            in_option_2_name,
            in_option_2_value
        );
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addOrderDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addOrderDetails`(
	`in_chanl` VARCHAR(128),
	`in_channel_code` VARCHAR(128),
	`in_ownr_code` VARCHAR(128),
	`in_fby_id` VARCHAR(128),
	`in_account_id` INT,
	`in_order_no` VARCHAR(256),
	`in_location_id` VARCHAR(256),
	`in_selr_ordr_id` VARCHAR(100),
	`in_prchse_dt` DATETIME,
	`in_payment_time` DATETIME,
	`in_ordr_line_id` VARCHAR(256),
	`in_sku` VARCHAR(256),
	`in_barcode` VARCHAR(128),
	`in_ordr_itm_id` VARCHAR(128),
	`in_trnsactn_id` VARCHAR(128),
	`in_prod_nm` VARCHAR(256),
	`in_qntity_prchsed` INT,
	`in_curncy` VARCHAR(10),
	`in_exchng_rt` FLOAT,
	`in_itm_price` DECIMAL(10, 2),
	`in_line_itm_price` DECIMAL(10, 2),
	`in_itm_tx` DECIMAL(10, 2),
	`in_itm_totl_tx` DECIMAL(10, 2),
	`in_promotion_discount` DECIMAL(10, 2),
	`in_item_total_price` DECIMAL(10, 2),
	`in_item_total_ship_price` DECIMAL(10, 2),
	`in_crn_name` VARCHAR(60),
	`in_crn_id` VARCHAR(100),
	`in_financial_status` VARCHAR(128),
	`in_order_status` VARCHAR(128),
    `in_managedByChannel` TINYINT
)
BEGIN

	DECLARE var_channel_name VARCHAR(200);
    DECLARE is_Order_Exists BIT;
	DECLARE errno INT;
    
    SET in_location_id = IF(in_fby_id = 39, '***********', in_location_id);
    SET in_location_id = IF(in_fby_id = 40, '62527701097', in_location_id);
    
    /*
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
		-- GET CURRENT DIAGNOSTICS CONDITION 1 errno = MYSQL_ERRNO;
		-- SELECT errno AS MYSQL_ERROR;
		ROLLBACK;
    END;
    START TRANSACTION;
    */
    SET var_channel_name  = (
		select LOWER(ch.channelName) from channelconnector._2_channel as ch 
		Where  ch.channelId = in_fby_id
		AND ch.isActive = 1 
		limit 1
	);
    
    set in_barcode = IF(in_barcode is null, '',in_barcode);
    
    
    IF EXISTS (
		SELECT 
			1
		FROM
			channelconnector.order_details t
		WHERE
			t.fby_user_id = in_fby_id
			AND t.order_no = in_order_no
			AND t.seller_order_id = in_selr_ordr_id
			AND t.order_line_item_id = in_ordr_line_id
			AND t.order_item_id = in_ordr_itm_id
			AND t.sku = in_sku
            -- AND IF (t.barcode is null, '',t.barcode) = IF (t.barcode IS NOT null OR t.barcode <> '' , in_barcode,'')
            FOR UPDATE
		
    )
    THEN
		SET is_Order_Exists = 1;
    ELSE
		SET is_Order_Exists = NULL;
	END IF;
	
	IF(var_channel_name like '%woo%comm%' AND is_Order_Exists IS NULL)
	THEN
		SET in_order_no = replace(in_order_no,'wc_order_','order_');
	END IF;

	IF( is_Order_Exists IS NULL)
	THEN
		
		INSERT INTO order_details
		(
			`channel`,
			channel_code,
			owner_code,
			fby_user_id,
			account_id,
			order_no,
			location_id,
			seller_order_id,
			purchase_date,
			payment_time,
			order_line_item_id,
			sku,
			barcode,
			order_item_id,
			transaction_id,
			product_name,
			quantity_purchased,
			currency,
			exchange_rate,
			item_price,
			line_item_price,
			item_tax,
			item_total_tax,
			promotion_discount,
			item_total_price,
			item_total_ship_price,
			payment_status,
			order_status,
			cron_name,
			cron_id,
            managedByChannel
		)
		VALUES
		(
			in_chanl,
			in_channel_code,
			in_ownr_code,
			in_fby_id,
			in_account_id,
			in_order_no,
			in_location_id,
			in_selr_ordr_id,
			in_prchse_dt,
			in_payment_time,
			in_ordr_line_id,
			in_sku,
			in_barcode,
			in_ordr_itm_id,
			in_trnsactn_id,
			in_prod_nm,
			in_qntity_prchsed,
			in_curncy,
			in_exchng_rt,
			in_itm_price,
			in_line_itm_price,
			in_itm_tx,
			in_itm_totl_tx,
			in_promotion_discount,
			in_item_total_price,
			in_item_total_ship_price,
			in_financial_status,
			in_order_status,
			in_crn_name,
			in_crn_id,
            in_managedByChannel
		) 
		ON DUPLICATE KEY
		UPDATE
			`channel` = 	in_chanl,
			channel_code = 	in_channel_code,
			owner_code = 	in_ownr_code,
			-- fby_user_id = 	in_fby_id,
			account_id = 	in_account_id,
			location_id = 	in_location_id,
			seller_order_id = 	in_selr_ordr_id,
			purchase_date = 	in_prchse_dt,
			payment_time = 	in_payment_time,
			-- order_line_item_id = 	in_ordr_line_id,
			-- sku = 	in_sku,
			barcode = 	in_barcode,
			order_item_id = 	in_ordr_itm_id,
			transaction_id = 	in_trnsactn_id,
			product_name = 	in_prod_nm,
			quantity_purchased = 	in_qntity_prchsed,
			currency = 	in_curncy,
			exchange_rate = 	in_exchng_rt,
			item_price = 	in_itm_price,
			line_item_price = 	in_line_itm_price,
			item_tax = 	in_itm_tx,
			item_total_tax = 	in_itm_totl_tx,
			promotion_discount = 	in_promotion_discount,
			item_total_price = 	in_item_total_price,
			item_total_ship_price = 	in_item_total_ship_price,
			payment_status = 	in_financial_status,
			order_status = 	in_order_status,
			cron_name = 	in_crn_name,
			cron_id	= in_crn_id;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
		
        IF EXISTS (
        Select 1 from channelconnector.order_details 
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND seller_order_id = in_selr_ordr_id
			AND order_line_item_id = in_ordr_line_id
			AND order_item_id = in_ordr_itm_id
			AND sku = in_sku
			-- AND IF (barcode is null, '',barcode) = IF (barcode IS NOT null OR barcode <> '' , in_barcode,'')
            limit 1
        )
        THEN 
        
			UPDATE order_details
			SET `channel` = 	in_chanl,
				channel_code = 	in_channel_code,
				owner_code = 	in_ownr_code,
				-- fby_user_id = 	in_fby_id,
				account_id = 	in_account_id,
				location_id = 	in_location_id,
				-- seller_order_id = 	in_selr_ordr_id,
				purchase_date = 	in_prchse_dt,
				payment_time = 	in_payment_time,
				-- order_line_item_id = 	in_ordr_line_id,
				-- sku = 	in_sku,
				barcode = 	in_barcode,
				order_item_id = 	in_ordr_itm_id,
				transaction_id = 	in_trnsactn_id,
				product_name = 	in_prod_nm,
				quantity_purchased = 	in_qntity_prchsed,
				currency = 	in_curncy,
				exchange_rate = 	in_exchng_rt,
				item_price = 	in_itm_price,
				line_item_price = 	in_line_itm_price,
				item_tax = 	in_itm_tx,
				item_total_tax = 	in_itm_totl_tx,
				promotion_discount = 	in_promotion_discount,
				item_total_price = 	in_item_total_price,
				item_total_ship_price = 	in_item_total_ship_price,
				payment_status = 	in_financial_status,
				order_status = 	in_order_status,
				cron_name = 	in_crn_name,
				cron_id	= in_crn_id,
				updated_at = now()
			WHERE
				fby_user_id = in_fby_id
				AND order_no = in_order_no
				AND seller_order_id = in_selr_ordr_id
				AND order_line_item_id = in_ordr_line_id
				AND order_item_id = in_ordr_itm_id
				AND sku = in_sku
				-- AND IF (barcode is null, '',barcode) = IF (barcode IS NOT null OR barcode <> '' , in_barcode,'')
			;
 
		END IF;
	END IF;
	

    
    SET SQL_SAFE_UPDATES = 0;
    
    IF( var_channel_name like '%storeden%')
    THEN
		IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
        WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			
			AND o.order_item_id = p.item_id
            limit 1
        )
        THEN 
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			
			AND o.order_item_id = p.item_id
			;
		END IF;
    ELSE
		IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
		WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			AND o.order_item_id = p.item_id
			AND p.location_id <> '0'
            AND p.location_id <> ''
            AND p.location_id IS NOT NULL
            AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0')
            AND o.order_no = in_order_no
            limit 1
        )
        THEN
			UPDATE channelconnector.order_details AS o,
			channelconnector.products AS p 
			SET 
				o.location_id = p.location_id
			WHERE
				o.fby_user_id = p.fby_user_id
				AND o.sku = p.sku 
				AND o.order_item_id = p.item_id
				AND p.location_id <> '0'
				AND p.location_id <> ''
				AND p.location_id IS NOT NULL
				AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0')
				AND o.order_no = in_order_no;
        END IF;
        
        IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
		WHERE
			o.fby_user_id = p.fby_user_id
			AND trim(o.sku) = trim(p.sku) 
			AND (o.order_item_id = p.item_id OR o.order_item_id = p.item_product_id)
			AND ( p.barcode <> '0'	AND p.barcode <> ''	AND p.barcode IS NOT NULL)
			AND ( o.barcode IS NULL  OR o.barcode = '' OR o.barcode ='0')
			and o.order_no = in_order_no
			limit 1
        )
        THEN
			UPDATE channelconnector.order_details AS o,
			channelconnector.products AS p 
			SET 
				o.barcode = p.barcode
			WHERE
				o.fby_user_id = p.fby_user_id
				AND trim(o.sku) = trim(p.sku) 
				AND (o.order_item_id = p.item_id OR o.order_item_id = p.item_product_id)
				AND ( p.barcode <> '0'	AND p.barcode <> ''	AND p.barcode IS NOT NULL)
				AND ( o.barcode IS NULL  OR o.barcode = '' OR o.barcode ='0')
				and o.order_no = in_order_no
				;
        END IF;    
	END IF;    
	SET SQL_SAFE_UPDATES = 1;

	SELECT t.* from channelconnector.order_details t
    Where t.fby_user_id = in_fby_id
    AND t.order_no = in_order_no
    AND t.seller_order_id = in_selr_ordr_id
    AND t.order_line_item_id = 	in_ordr_line_id
    AND t.order_item_id = 	in_ordr_itm_id
    AND t.sku = 	in_sku
	AND IF (t.barcode is null, '',t.barcode) = IF (t.barcode IS NOT null OR t.barcode <> '' , in_barcode,'')
    limit 1
    ;
    -- COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addOrderDetailsV1` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addOrderDetailsV1`(
	`in_chanl` VARCHAR(128),
	`in_channel_code` VARCHAR(128),
	`in_ownr_code` VARCHAR(128),
	`in_fby_id` VARCHAR(128),
	`in_account_id` INT,
	`in_order_no` VARCHAR(256),
	`in_location_id` VARCHAR(256),
	`in_selr_ordr_id` VARCHAR(100),
	`in_prchse_dt` DATETIME,
	`in_payment_time` DATETIME,
	`in_ordr_line_id` VARCHAR(256),
	`in_sku` VARCHAR(256),
	`in_barcode` VARCHAR(128),
	`in_ordr_itm_id` VARCHAR(128),
	`in_trnsactn_id` VARCHAR(128),
	`in_prod_nm` VARCHAR(256),
	`in_qntity_prchsed` INT,
	`in_curncy` VARCHAR(10),
	`in_exchng_rt` FLOAT,
	`in_itm_price` DECIMAL(10, 2),
	`in_line_itm_price` DECIMAL(10, 2),
	`in_itm_tx` DECIMAL(10, 2),
	`in_itm_totl_tx` DECIMAL(10, 2),
	`in_promotion_discount` DECIMAL(10, 2),
	`in_item_total_price` DECIMAL(10, 2),
	`in_item_total_ship_price` DECIMAL(10, 2),
	`in_crn_name` VARCHAR(60),
	`in_crn_id` VARCHAR(100),
	`in_financial_status` VARCHAR(128),
	`in_order_status` VARCHAR(128),
    `in_managedByChannel`TINYINT,
    `in_fulfillment_order_id` VARCHAR(128),
    `in_fulfillment_order_line_item_id` VARCHAR(128)
)
BEGIN

	DECLARE var_channel_name VARCHAR(200);
    DECLARE is_Order_Exists BIT;
	DECLARE errno INT;
    
    SET in_location_id = IF(in_fby_id = 39, '***********', in_location_id);
    SET in_location_id = IF(in_fby_id = 40, '62527701097', in_location_id);
    
    /*
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
		-- GET CURRENT DIAGNOSTICS CONDITION 1 errno = MYSQL_ERRNO;
		-- SELECT errno AS MYSQL_ERROR;
		ROLLBACK;
    END;
    START TRANSACTION;
    */
    SET var_channel_name  = (
		select LOWER(ch.channelName) from channelconnector._2_channel as ch 
		Where  ch.channelId = in_fby_id
		AND ch.isActive = 1 
		limit 1
	);
    
    set in_barcode = IF(in_barcode is null, '',in_barcode);
    
    
    IF EXISTS (
		SELECT 
			1
		FROM
			channelconnector.order_details t
		WHERE
			t.fby_user_id = in_fby_id
			AND t.order_no = in_order_no
			AND t.seller_order_id = in_selr_ordr_id
			AND t.order_line_item_id = in_ordr_line_id
			AND t.order_item_id = in_ordr_itm_id
			AND t.sku = in_sku
            -- AND IF (t.barcode is null, '',t.barcode) = IF (t.barcode IS NOT null OR t.barcode <> '' , in_barcode,'')
            FOR UPDATE
		
    )
    THEN
		SET is_Order_Exists = 1;
    ELSE
		SET is_Order_Exists = NULL;
	END IF;
	
	IF(var_channel_name like '%woo%comm%' AND is_Order_Exists IS NULL)
	THEN
		SET in_order_no = replace(in_order_no,'wc_order_','order_');
	END IF;

	IF( is_Order_Exists IS NULL)
	THEN
		
		INSERT INTO order_details
		(
			`channel`,
			channel_code,
			owner_code,
			fby_user_id,
			account_id,
			order_no,
			location_id,
			seller_order_id,
			purchase_date,
			payment_time,
			order_line_item_id,
			sku,
			barcode,
			order_item_id,
			transaction_id,
			product_name,
			quantity_purchased,
			currency,
			exchange_rate,
			item_price,
			line_item_price,
			item_tax,
			item_total_tax,
			promotion_discount,
			item_total_price,
			item_total_ship_price,
			payment_status,
			order_status,
			cron_name,
			cron_id,
            managedByChannel,
			fulfillment_order_id,
			fulfillment_order_line_item_id
		)
		VALUES
		(
			in_chanl,
			in_channel_code,
			in_ownr_code,
			in_fby_id,
			in_account_id,
			in_order_no,
			in_location_id,
			in_selr_ordr_id,
			in_prchse_dt,
			in_payment_time,
			in_ordr_line_id,
			in_sku,
			in_barcode,
			in_ordr_itm_id,
			in_trnsactn_id,
			in_prod_nm,
			in_qntity_prchsed,
			in_curncy,
			in_exchng_rt,
			in_itm_price,
			in_line_itm_price,
			in_itm_tx,
			in_itm_totl_tx,
			in_promotion_discount,
			in_item_total_price,
			in_item_total_ship_price,
			in_financial_status,
			in_order_status,
			in_crn_name,
			in_crn_id,
            in_managedByChannel,
            in_fulfillment_order_id,
			in_fulfillment_order_line_item_id
		) 
		ON DUPLICATE KEY
		UPDATE
			`channel` = 	in_chanl,
			channel_code = 	in_channel_code,
			owner_code = 	in_ownr_code,
			-- fby_user_id = 	in_fby_id,
			account_id = 	in_account_id,
			location_id = 	in_location_id,
			seller_order_id = 	in_selr_ordr_id,
			purchase_date = 	in_prchse_dt,
			payment_time = 	in_payment_time,
			-- order_line_item_id = 	in_ordr_line_id,
			-- sku = 	in_sku,
			barcode = 	in_barcode,
			order_item_id = 	in_ordr_itm_id,
			transaction_id = 	in_trnsactn_id,
			product_name = 	in_prod_nm,
			quantity_purchased = 	in_qntity_prchsed,
			currency = 	in_curncy,
			exchange_rate = 	in_exchng_rt,
			item_price = 	in_itm_price,
			line_item_price = 	in_line_itm_price,
			item_tax = 	in_itm_tx,
			item_total_tax = 	in_itm_totl_tx,
			promotion_discount = 	in_promotion_discount,
			item_total_price = 	in_item_total_price,
			item_total_ship_price = 	in_item_total_ship_price,
			payment_status = 	in_financial_status,
			order_status = 	in_order_status,
			cron_name = 	in_crn_name,
			cron_id	= in_crn_id,
            fulfillment_order_id = in_fulfillment_order_id,
			fulfillment_order_line_item_id = in_fulfillment_order_line_item_id
            ;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
		
        IF EXISTS (
        Select 1 from channelconnector.order_details 
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND seller_order_id = in_selr_ordr_id
			AND order_line_item_id = in_ordr_line_id
			AND order_item_id = in_ordr_itm_id
			AND sku = in_sku
			-- AND IF (barcode is null, '',barcode) = IF (barcode IS NOT null OR barcode <> '' , in_barcode,'')
            limit 1
        )
        THEN 
        
			UPDATE order_details
			SET `channel` = 	in_chanl,
				channel_code = 	in_channel_code,
				owner_code = 	in_ownr_code,
				-- fby_user_id = 	in_fby_id,
				account_id = 	in_account_id,
				location_id = 	in_location_id,
				-- seller_order_id = 	in_selr_ordr_id,
				purchase_date = 	in_prchse_dt,
				payment_time = 	in_payment_time,
				-- order_line_item_id = 	in_ordr_line_id,
				-- sku = 	in_sku,
				barcode = 	in_barcode,
				order_item_id = 	in_ordr_itm_id,
				transaction_id = 	in_trnsactn_id,
				product_name = 	in_prod_nm,
				quantity_purchased = 	in_qntity_prchsed,
				currency = 	in_curncy,
				exchange_rate = 	in_exchng_rt,
				item_price = 	in_itm_price,
				line_item_price = 	in_line_itm_price,
				item_tax = 	in_itm_tx,
				item_total_tax = 	in_itm_totl_tx,
				promotion_discount = 	in_promotion_discount,
				item_total_price = 	in_item_total_price,
				item_total_ship_price = 	in_item_total_ship_price,
				payment_status = 	in_financial_status,
				order_status = 	in_order_status,
				cron_name = 	in_crn_name,
				cron_id	= in_crn_id,
                fulfillment_order_id = in_fulfillment_order_id,
				fulfillment_order_line_item_id = in_fulfillment_order_line_item_id,
				updated_at = now()
			WHERE
				fby_user_id = in_fby_id
				AND order_no = in_order_no
				AND seller_order_id = in_selr_ordr_id
				AND order_line_item_id = in_ordr_line_id
				AND order_item_id = in_ordr_itm_id
				AND sku = in_sku
				-- AND IF (barcode is null, '',barcode) = IF (barcode IS NOT null OR barcode <> '' , in_barcode,'')
			;
 
		END IF;
	END IF;
	

    
    SET SQL_SAFE_UPDATES = 0;
    
    IF( var_channel_name like '%storeden%')
    THEN
		IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
        WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			
			AND o.order_item_id = p.item_id
            limit 1
        )
        THEN 
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			
			AND o.order_item_id = p.item_id
			;
		END IF;
    ELSE
		IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
		WHERE
			o.fby_user_id = p.fby_user_id
			AND o.sku = p.sku 
			AND o.order_item_id = p.item_id
			AND p.location_id <> '0'
            AND p.location_id <> ''
            AND p.location_id IS NOT NULL
            AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0')
            AND o.order_no = in_order_no
            limit 1
        )
        THEN
			UPDATE channelconnector.order_details AS o,
			channelconnector.products AS p 
			SET 
				o.location_id = p.location_id
			WHERE
				o.fby_user_id = p.fby_user_id
				AND o.sku = p.sku 
				AND o.order_item_id = p.item_id
				AND p.location_id <> '0'
				AND p.location_id <> ''
				AND p.location_id IS NOT NULL
				AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0')
				AND o.order_no = in_order_no;
        END IF;
        
        IF EXISTS (
        Select 1 from channelconnector.order_details AS o,channelconnector.products AS p 
		WHERE
			o.fby_user_id = p.fby_user_id
			AND trim(o.sku) = trim(p.sku) 
			AND (o.order_item_id = p.item_id OR o.order_item_id = p.item_product_id)
			AND ( p.barcode <> '0'	AND p.barcode <> ''	AND p.barcode IS NOT NULL)
			AND ( o.barcode IS NULL  OR o.barcode = '' OR o.barcode ='0')
			and o.order_no = in_order_no
			limit 1
        )
        THEN
			UPDATE channelconnector.order_details AS o,
			channelconnector.products AS p 
			SET 
				o.barcode = p.barcode
			WHERE
				o.fby_user_id = p.fby_user_id
				AND trim(o.sku) = trim(p.sku) 
				AND (o.order_item_id = p.item_id OR o.order_item_id = p.item_product_id)
				AND ( p.barcode <> '0'	AND p.barcode <> ''	AND p.barcode IS NOT NULL)
				AND ( o.barcode IS NULL  OR o.barcode = '' OR o.barcode ='0')
				and o.order_no = in_order_no
				;
        END IF;    
	END IF;    
	SET SQL_SAFE_UPDATES = 1;

	SELECT t.* from channelconnector.order_details t
    Where t.fby_user_id = in_fby_id
    AND t.order_no = in_order_no
    AND t.seller_order_id = in_selr_ordr_id
    AND t.order_line_item_id = 	in_ordr_line_id
    AND t.order_item_id = 	in_ordr_itm_id
    AND t.sku = 	in_sku
	AND IF (t.barcode is null, '',t.barcode) = IF (t.barcode IS NOT null OR t.barcode <> '' , in_barcode,'')
    limit 1
    ;
    -- COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addOrderMaster` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addOrderMaster`(
 `in_channel` VARCHAR(256),
 `in_channel_code` VARCHAR(20),
 `in_ownr_code` VARCHAR(20),
 `in_fby_id` VARCHAR(128),
 `in_account_id` INT,
 `in_order_no` VARCHAR(256),
 `in_selr_ordr_id` VARCHAR(100),
 `in_prchse_dt` DATETIME,
 `in_payment_time` DATETIME,
 `in_recipient_nm` VARCHAR(256),
 `in_company` VARCHAR(256),
 `in_shiper_adr1` VARCHAR(256),
 `in_shiper_adr2` VARCHAR(256),
 `in_shiper_city` VARCHAR(256),
 `in_shiper_state` VARCHAR(64),
 `in_shiper_state_code` VARCHAR(4),
 `in_shiper_zip` VARCHAR(256),
 `in_shiper_country` VARCHAR(256),
 `in_shiper_country_code` VARCHAR(20),
 `in_shiper_phone` VARCHAR(256),
 `in_total_order` DECIMAL(10,2),
 `in_total_items` INT,
 `in_total_item_price` DECIMAL(10,2),
 `in_total_ship_price` DECIMAL(10,2),
 `in_total_tax` DECIMAL(10,2),
 `in_total_discount` DECIMAL(10,2),
 `in_payment_id` VARCHAR(256),
 `in_payment_method` VARCHAR(256),
 `in_currency_code` VARCHAR(20),
 `in_buyer_id` VARCHAR(128),
 `in_buyer_email` VARCHAR(256),
 `in_buyer_name` VARCHAR(256),
 `in_sales_record_no` VARCHAR(128),
 `in_payment_status` VARCHAR(128),
 `in_order_status` VARCHAR(128),
 `in_crn_name` VARCHAR(60),
 `in_crn_id` VARCHAR(100),
 `in_managedByChannel` TINYINT,
 `in_bill_generator_nm` VARCHAR(256),
 `in_bill_company` VARCHAR(256),
 `in_bill_adr1` VARCHAR(256),
 `in_bill_adr2` VARCHAR(256),
 `in_bill_city` VARCHAR(256),
 `in_bill_state` VARCHAR(64),
 `in_bill_state_code` VARCHAR(4),
 `in_bill_zip` VARCHAR(256),
 `in_bill_country` VARCHAR(256),
 `in_bill_country_code` VARCHAR(20),
 `in_bill_phone` VARCHAR(256)
)
BEGIN
	/*
    
		call  channelconnector.`addOrderMaster`(
        
			'woocommerce IT',		#	in_channel,
			'WCIT',					#	in_channel_code,
			'in_ownr_code',			#	in_ownr_code,
			'27',					#	in_fby_id,
			'123456',				#	in_account_id,
			'wc_order_KK6GN241vQJU9',			#	in_order_no,
			'in_selr_ordr_id',		#	in_selr_ordr_id,
			 NOW(),					#	in_prchse_dt,
			 NOW(),					#	in_payment_time,
			'in_recipient_nm',		#	in_recipient_nm,
			'in_company',			#	in_company,
			'in_shiper_adr1',		#	in_shiper_adr1,
			'in_shiper_adr2',		#	in_shiper_adr2,
			'in_shiper_city',		#	in_shiper_city,
			'in_shiper_state',		#	in_shiper_state,
			'1',					#	in_shiper_state_code,
			'in_shiper_zip',		#	in_shiper_zip,
			'in_shiper_country',	#	in_shiper_country,
			'1',					#	in_shiper_country_code,
			'in_shiper_phone',		#	in_shiper_phone,
			'1',					#	in_total_order,
			'1',					#	in_total_items,
			'10',					#	in_total_item_price,
			'10',					#	in_total_ship_price,
			'10',					#	in_total_tax,
			'0',					#	in_total_discount,
			'1',					#	in_payment_id,
			'cash',					#	in_payment_method,
			'EUR',					#	in_currency_code,
			'in_buyer_id',			#	in_buyer_id,
			'in_buyer_email',		#	in_buyer_email,
			'in_buyer_name',		#	in_buyer_name,
			'in_sales_record_no',	#	in_sales_record_no,
			'in_payment_status',	#	in_payment_status,
			'in_order_status',		#	in_order_status,
			'in_crn_name',			#	in_crn_name,
			'in_crn_id'				#	in_crn_id,
			'in_managedByChannel'   #   in_managedByChannel,
		     'in_bill_generator_nm' #   in_bill_generator_nm,
             'in_bill_company',		#	in_bill_company,
			 'in_bill_adr1'         #   in_bill_adr1,
             'in_bill_adr2',        #   in_bill_adr2,
	         'in_bill_city',        #   in_bill_city,
             'in_bill_state',       #   in_bill_state,
             'in_bill_state_code',  #   in_bill_state_code,
             'in_bill_zip',         #   in_bill_zip,
             'in_bill_country',     #    in_bill_country,
             'in_bill_country_code',#    in_bill_country_code,
	         'in_bill_phone'        #    in_bill_phone


		);
        
        Select * from channelconnector.order_masters
        order by 1 DESC
        limit 10;
        
        
			SET SQL_SAFE_UPDATES = 0;    
			Delete from channelconnector.order_masters
			Where id = 700;
			
			SET SQL_SAFE_UPDATES = 1;    
        
        
    */
	
    DECLARE var_channel_name VARCHAR(200);
    DECLARE is_Order_Exists BIT;
	
    SET in_shiper_country_code = UPPER(LEFT(in_shiper_country_code , 2));
    SET in_bill_country_code = UPPER(LEFT(in_bill_country_code , 2));
    SET in_shiper_state_code = UPPER(LEFT(in_shiper_state_code , 2));
    SET in_bill_state_code = UPPER(LEFT(in_bill_state_code , 2));
	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_fby_id
		AND ch.isActive = 1 
		limit 1
	);
    
    SET is_Order_Exists = (
	SELECT 
		1
	FROM
		order_masters
	WHERE
		order_no = in_order_no
		AND fby_user_id = in_fby_id
		AND `channel` = in_channel
		AND channel_code = in_channel_code
		AND owner_code = in_ownr_code
		AND account_id = in_account_id
		AND seller_order_id = in_selr_ordr_id
        limit 1
    );

	
	IF(var_channel_name like '%woo%comm%' AND is_Order_Exists IS NULL)
	THEN
		SET in_order_no = replace(in_order_no,'wc_order_','order_');
	END IF;
	-- Select is_Order_Exists, var_channel_name,in_order_no;
    
	INSERT IGNORE INTO order_masters(
		`channel`,	
		channel_code,	
		owner_code	,
		fby_user_id,	
		account_id,	
		order_no,	
		seller_order_id,	
		purchase_date,	
		payment_date,	
		recipient_name,	
		ship_company,	
		ship_address_1,	
		ship_address_2,	
		ship_city,	
		ship_state,	
		ship_state_code,	
		ship_postal_code,	
		ship_country,	
		ship_country_code,	
		ship_phone_number,	
		total_order,	
		total_items,	
		total_items_price,	
		total_shipping_price,	
		total_tax,	
		total_discount,	
		payment_transaction_id,	
		payment_method,	
		currency_code,	
		buyer_id,	
		buyer_email,	
		buyer_name,	
		sales_record_no,	
		payment_status,	
		order_status,	
		cron_name,	
		cron_id,
        managedByChannel,
		bill_generator_name,
        bill_company,
		bill_address_1,
		bill_address_2,
		bill_city,
		bill_state,
		bill_state_code,
		bill_postal_code,
		bill_country,
		bill_country_code,
		bill_phone_number
)
VALUES(
	in_channel,	
	in_channel_code,	
	in_ownr_code,	
	in_fby_id,	
	in_account_id,	
	in_order_no,	
	in_selr_ordr_id,	
	in_prchse_dt,	
	in_payment_time,	
	in_recipient_nm,	
	in_company,	
	in_shiper_adr1,	
	in_shiper_adr2,	
	in_shiper_city,	
	in_shiper_state,	
	in_shiper_state_code,	
	in_shiper_zip,	
	in_shiper_country,	
	in_shiper_country_code,	
	in_shiper_phone,	
	in_total_order,	
	in_total_items,	
	in_total_item_price,	
	in_total_ship_price,	
	in_total_tax,	
	in_total_discount,	
	in_payment_id,	
	in_payment_method,	
	in_currency_code,	
	in_buyer_id,	
	in_buyer_email,	
	in_buyer_name,	
	in_sales_record_no,	
	in_payment_status,	
	in_order_status,	
	in_crn_name,	
	in_crn_id,
    in_managedByChannel,
	in_bill_generator_nm,
    in_bill_company,
    in_bill_adr1,
    in_bill_adr2,
	in_bill_city,
    in_bill_state,
    in_bill_state_code,
    in_bill_zip,
    in_bill_country,
    in_bill_country_code,
	in_bill_phone
)
ON DUPLICATE KEY
	UPDATE
	
	purchase_date       =       			in_prchse_dt,
	payment_date       =       			IF(payment_date is not null, payment_date, in_payment_time),
	recipient_name       =       			in_recipient_nm,
	ship_company       =       			in_company,
	ship_address_1       =       			in_shiper_adr1,
	ship_address_2       =       			in_shiper_adr2,
	ship_city       =       			in_shiper_city,
	ship_state       =       			in_shiper_state,
	ship_state_code       =       			in_shiper_state_code,
	ship_postal_code       =       			in_shiper_zip,
	ship_country       =       			in_shiper_country,
	ship_country_code       =       			in_shiper_country_code,
	ship_phone_number       =       			in_shiper_phone,
	total_order       =       			in_total_order,
	total_items       =       			in_total_items,
	total_items_price       =       			in_total_item_price,
	total_shipping_price       =       			in_total_ship_price,
	total_tax       =       			in_total_tax,
	total_discount       =       			in_total_discount,
	payment_transaction_id       =       			in_payment_id,
	payment_method       =       			in_payment_method,
	currency_code       =       			in_currency_code,
	buyer_id       =       			in_buyer_id,
	buyer_email       =       			in_buyer_email,
	buyer_name       =       			in_buyer_name,
	sales_record_no       =       			in_sales_record_no,
	payment_status       =       			in_payment_status,
	order_status       =       			in_order_status,
	cron_name       =       			in_crn_name,
	cron_id			=      in_crn_id,
	bill_generator_name       =     in_bill_generator_nm,
    bill_company                =       in_bill_company,
    bill_address_1                 =      in_bill_adr1,
	bill_address_2                  =      in_bill_adr2,
	bill_city                        =      in_bill_city,
	bill_state                   =      in_bill_state,
	bill_state_code            =      in_bill_state_code,
	bill_postal_code   =     in_bill_zip,
	bill_country         =      in_bill_country,
	bill_country_code      =         in_bill_country_code,
	bill_phone_number      =          in_bill_phone;

    
	SELECT 
		*
	FROM
		order_masters
	WHERE
		order_no = in_order_no
		AND fby_user_id = in_fby_id
		AND `channel` = in_channel
		AND channel_code = in_channel_code
		AND owner_code = in_ownr_code
		AND account_id = in_account_id
		AND seller_order_id = in_selr_ordr_id;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addOrderTracking` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addOrderTracking`(IN `channel_order_id` VARCHAR(256), IN `channel_line_order_id` VARCHAR(256), IN `owner_code` VARCHAR(25), IN `channel_code` VARCHAR(20), IN `provider_order_id` VARCHAR(256), IN `provider_line_order_id` VARCHAR(256), IN `order_date` DATETIME, IN `sku` VARCHAR(256), IN `barcode` VARCHAR(256), IN `tracking_id` VARCHAR(256), IN `ship_date` DATETIME, IN `carrier` VARCHAR(20), IN `url` TEXT, IN `is_return` VARCHAR(20), IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100))
BEGIN
	INSERT IGNORE INTO temp_order_inventory(channel_order_id,channel_line_order_id,owner_code,channel_code,provider_order_id,provider_line_order_id,order_date,sku,barcode,tracking_id,ship_date,carrier,url,is_return,cron_name,cron_id) VALUES(channel_order_id,channel_line_order_id,owner_code,channel_code,provider_order_id,provider_line_order_id,order_date,sku,barcode,tracking_id,ship_date,carrier,url,is_return,crn_name,crn_id);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addPaymentMethod` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addPaymentMethod`(IN `method_name` VARCHAR(128), IN `method_code` VARCHAR(128))
BEGIN
INSERT INTO fby_payment_method(name,payment_code)VALUES(method_name,method_code)ON DUPLICATE KEY UPDATE payment_code=method_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addPriceDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addPriceDetails`(
 IN `in_skuid` VARCHAR(128),
 IN `in_skucode` VARCHAR(128),
 IN `in_barcode` VARCHAR(128),
 IN `in_fullPrice` INT(11),
 IN `in_specialPrice` INT(11),
 IN `in_priority` INT(11),
 IN `in_source` VARCHAR(128),
 IN `in_currency` VARCHAR(128),
 IN `in_itemId` VARCHAR(128),
 IN `in_crnid` VARCHAR(100),
 IN `in_fby_id` VARCHAR(100)
)
BEGIN

DECLARE in_channel_name varchar(256);
DECLARE var_isUpdated tinyint;
DECLARE var_isExists tinyint;
/*
DECLARE EXIT HANDLER FOR SQLEXCEPTION 
BEGIN
    ROLLBACK;
END;

-- START TRANSACTION;
-- */

SET transaction isolation level READ uncommitted;

SET in_channel_name = 
					(
						SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
						limit 1
					);
SET SQL_SAFE_UPDATES = 0;


	
       
	IF EXISTS (
        SELECT 1 FROM temp_master_price AS T 
		WHERE 
			T.skucode = in_skucode AND
            T.fby_user_id = in_fby_id
		LIMIT 1 
        FOR UPDATE
    ) = 1 
    THEN
		-- Select 'Updated' as isExists;
        
		Update temp_master_price
		set barcode = in_barcode,
		fullPrice = in_fullPrice,
        specialPrice = in_specialPrice,
		priority = in_priority,
        source = in_source,
        currency = in_currency,
        itemId = in_itemId,
		cron_id = in_crnid,
		updated_at = now()
		Where fby_user_id = in_fby_id and skucode = in_skucode ;
            
         SET var_isUpdated = 1; 
         SET var_isExists = 1;
    ELSE
		-- Select 'Inserted' as isExists;
    
		INSERT INTO temp_master_price(
			sku_id,skucode,barcode,fullPrice,specialPrice,priority,source,currency,itemId,cron_id, 
			created_at,fby_user_id,updated_at
		) 
		VALUES(
			in_skuid,in_skucode,in_barcode,in_fullPrice,in_specialPrice,in_priority,in_source,in_currency,in_itemId,in_crnid,
			now(),in_fby_id,now()
			);
		
         
         SET var_isExists = 0;
         
    END IF;
   

  SET SQL_SAFE_UPDATES = 1;
      Select var_isExists as isExists, in_channel_name,var_isUpdated;    
  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addProduct`(
	`in_fby_user_id` VARCHAR(128),
	`in_chanel` VARCHAR(20),
	`in_domain` VARCHAR(64),
	`in_ownr_code` VARCHAR(20),
	`in_sku` VARCHAR(128),
	`in_barcode` VARCHAR(128),
	`in_item_id` VARCHAR(127),
	`in_title` VARCHAR(300),
	`in_item_product_id` VARCHAR(127),
	`in_inventory_item_id` VARCHAR(127),
	`in_old_quantity` INT,
	`in_new_quantity` INT,
	`in_image` TEXT,
	`in_price` DECIMAL(10, 2),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
    `in_location_Id` VARCHAR(100)
)
BEGIN
	DECLARE var_channel_name varchar(200);
    DECLARE isExists TINYINT;
    DECLARE errno INT;
    
    
    
    -- /*
   
    -- */
     SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET in_location_Id = IF(in_fby_user_id = 39, '***********', in_location_Id);
    SET in_location_Id = IF(in_fby_user_id = 40, '62527701097', in_location_Id);
    
    SET var_channel_name  = (
	select LOWER(ch.channelName) from _2_channel as ch 
    Where  ch.channelId = in_fby_user_id
    AND ch.isActive = 1 
    limit 1
	);
    
	SET SQL_SAFE_UPDATES = 0;
	-- SET in_old_quantity = -1;
     SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET in_chanel = (
		Select lower(channelName) from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );
    IF(in_inventory_item_id is null) 
    then
		SET in_inventory_item_id = 0;
    END IF;
    
    IF(in_old_quantity is null) 
    then
		SET in_old_quantity = 0;
    END IF;
    
     IF(in_new_quantity is null) 
    then
		SET in_new_quantity = 0;
    END IF;
    
    SET in_barcode = IF(in_barcode is null, '', in_barcode);
     SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET isExists = (
		Select 1 from products
		Where concat(
					IF(fby_user_id is null, 0, fby_user_id) 
					,'-'
					, IF(sku is null, '', sku)  
					,'-'
					, IF(item_product_id is null, '', item_product_id)
					,'-'
					, IF(inventory_item_id is null, '', inventory_item_id)  
					,'-'
					, IF(item_id is null, '', item_id)   
					,'-'
					, IF(barcode is null, '', barcode)    
			) =
			concat(
					IF(in_fby_user_id is null, 0, in_fby_user_id) 
					,'-'
					, IF(in_sku is null, '', in_sku)  
					,'-'
					, IF(in_item_product_id is null, '', in_item_product_id)
					,'-'
					, IF(in_inventory_item_id is null, '', in_inventory_item_id)  
					,'-'
					, IF(in_item_id is null, '', in_item_id)   
					,'-'
					, IF(in_barcode is null, '', in_barcode)    
				)
         limit 1 
         FOR UPDATE
     );
     
	IF (in_sku IS NOT NULL AND  in_sku <> '' and isExists is null) THEN
    
     SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
     
	INSERT INTO
		products(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			price = in_price,
			location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
			previous_inventory_quantity =	case  when 
												var_channel_name like '%woo%comm%'  
                                                OR var_channel_name like '%mirak%'  
											then 
												previous_inventory_quantity 
                                            ELSE
												in_old_quantity
                                            END
                                            ,
			inventory_quantity =  case  when var_channel_name like '%woo%comm%'  then inventory_quantity else in_new_quantity end,
            image = in_image
		;
    ELSE   
		 SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
        UPDATE products
		SET domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			price = in_price,
			location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
			previous_inventory_quantity =	case  when 
												var_channel_name like '%woo%comm%'  
                                                OR var_channel_name like '%mirak%'  
											then 
												previous_inventory_quantity 
                                            ELSE
												in_old_quantity
                                            END
                                            ,
			inventory_quantity =  case  when var_channel_name like '%woo%comm%'  then inventory_quantity else in_new_quantity end,
            image = in_image
        Where concat(
					IF(fby_user_id is null, 0, fby_user_id) 
					,'-'
					, IF(sku is null, '', sku)  
					,'-'
					, IF(item_product_id is null, '', item_product_id)
					,'-'
					, IF(inventory_item_id is null, '', inventory_item_id)  
					,'-'
					, IF(item_id is null, '', item_id)   
					,'-'
					, IF(barcode is null, '', barcode)    
			) =
			concat(
					IF(in_fby_user_id is null, 0, in_fby_user_id) 
					,'-'
					, IF(in_sku is null, '', in_sku)  
					,'-'
					, IF(in_item_product_id is null, '', in_item_product_id)
					,'-'
					, IF(in_inventory_item_id is null, '', in_inventory_item_id)  
					,'-'
					, IF(in_item_id is null, '', in_item_id)   
					,'-'
					, IF(in_barcode is null, '', in_barcode)    
				);
			
    END IF;
    
    
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    Select * from products
    Where fby_user_id = in_fby_user_id
    and sku = in_sku
    order by id desc -- limit 1 
    ;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addProductPostOpeartion` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addProductPostOpeartion`(
	`in_fby_user_id` VARCHAR(128)
)
BEGIN
		DECLARE var_channel_name varchar(200);
        
		SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_fby_user_id
		AND ch.isActive = 1 
		limit 1
		);
        
        IF(var_channel_name like '%woo%comm%')
        THEN
        
			SET SQL_SAFE_UPDATES = 0;
            
			CREATE TEMPORARY TABLE IF NOT EXISTS channelconnector.temp_woocomm_product_update 
            (
            seq INT,
            id INT,
            channel varchar(256),
            sku varchar(128),
            item_id varchar(128), item_product_id varchar(128), parentId varchar(128), inventory_quantity INT,familty_qty_sum INT
            );
            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
            CREATE TEMPORARY TABLE IF NOT EXISTS channelconnector.temp_woocomm_product_update1  ENGINE=MEMORY as
            (
				SELECT 
					fby_User_id,
					item_product_id,
					`channel`,
					SUM(inventory_quantity) AS familty_qty_sum
				FROM
					channelconnector.products AS p
				WHERE
					fby_User_id = in_fby_user_id
						AND item_id <> item_product_id
						AND barcode <> ''
						AND LOWER(channel) LIKE 'woo%comm%'
				GROUP BY fby_User_id ,  item_product_id, channel 
            );
            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
            INSERT INTO temp_woocomm_product_update
            (
             seq ,	id , 	channel ,	sku ,	item_id ,
             item_product_id , parentId , inventory_quantity ,familty_qty_sum 
            )
			-- Select * from (
			Select ROW_NUMBER() over() as seq, 
			p1.id,p1.channel,
			p1.Sku,p1.item_id , p1.item_product_id,p2.item_product_id as parentId,p1.inventory_quantity,p2.familty_qty_sum
			from channelconnector.products as p1 
			Left join channelconnector.temp_woocomm_product_update1 as p2
			 on p2.item_product_id = p1.item_id
			 Where 
				p1.fby_User_id = in_fby_user_id
				and	lower(p1.channel) like 'woo%comm%'
				and p1.item_id = p1.item_product_id
				and p1.barcode = ''
			  
				and p1.inventory_quantity <> p2.familty_qty_sum;
			  -- ) as p6;
		  
          SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
          IF EXISTS (
          Select * from channelconnector.temp_woocomm_product_update
          limit 1
          )
          THEN
				Update channelconnector.products as p5, temp_woocomm_product_update as p6
				Set p5.inventory_quantity = p6.familty_qty_sum
				Where p5.id = p6.id and p5.inventory_quantity <> p6.familty_qty_sum;
          END IF;
	  END IF;
	-- COMMIT;
      DROP TEMPORARY TABLE IF  EXISTS channelconnector.temp_woocomm_product_update;
	  DROP TEMPORARY TABLE IF  EXISTS  channelconnector.temp_woocomm_product_update1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addProductUnit` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addProductUnit`(
IN `in_fby_user_id` VARCHAR(128), 
IN `in_sku` VARCHAR(128),
IN `in_product_type` VARCHAR(128),
IN `in_brand` VARCHAR(128),
IN `in_weight_unit` VARCHAR(128),
IN `in_weight_value` VARCHAR(128),
IN `in_dimensions_unit` VARCHAR(128),
IN `in_dimensions_width` VARCHAR(128),
IN `in_dimensions_height` VARCHAR(128),
IN `in_dimensions_length` VARCHAR(128),
IN `in_tags` VARCHAR(128),
IN `in_category` VARCHAR(128),
IN `in_asin` VARCHAR(128)
)
BEGIN
    DECLARE isExists tinyint;
    SET isExists = 0;
    SET isExists = (
			SELECT 
				1 
			FROM
				channelconnector.product_units as cp
			WHERE
				cp.fby_user_id = in_fby_user_id
				AND sku = in_sku
			LIMIT 1
    );
    
    IF (isExists) 
	THEN
		UPDATE product_units
		SET product_type = in_product_type AND
        brand =  in_brand AND
		weight_unit = in_weight_unit AND
        weight_value = in_weight_value AND
        dimensions_unit = in_dimensions_unit AND
		dimensions_width = in_dimensions_width AND 
        dimensions_height = in_dimensions_height AND
		dimensions_length = in_dimensions_length AND
		tags = in_tags AND
        category = in_category AND
		asin = in_asin;
    ELSE 
	INSERT INTO
		product_units(
        fby_user_id,
        sku,
        product_type,
        brand,
		weight_unit,
        weight_value,
        dimensions_unit,
		dimensions_width,
        dimensions_height,
		dimensions_length,
		tags,
        category,
		asin
        )
		VALUES
		(
		in_fby_user_id,
        in_sku,
        in_product_type,
        in_brand,
		in_weight_unit,
        in_weight_value,
        in_dimensions_unit,
		in_dimensions_width,
        in_dimensions_height,
		in_dimensions_length,
		in_tags,
        in_category,
		in_asin
        )
	ON DUPLICATE KEY
		UPDATE
        sku = in_sku,
        product_type = in_product_type AND
		brand =  in_brand AND
		weight_unit = in_weight_unit AND
        weight_value = in_weight_value AND
        dimensions_unit = in_dimensions_unit AND
		dimensions_width = in_dimensions_width AND 
        dimensions_height = in_dimensions_height AND
		dimensions_length = in_dimensions_length AND
		tags = in_tags AND
        category = in_category AND
		asin = in_asin;
   END IF;     
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addReport` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addReport`(`in_reportID` VARCHAR(200), `in_fby_user_id` int)
BEGIN
	 
	INSERT INTO
		Amazon_Reports(
			reportID,
            fby_user_id
		)
	VALUES
		(
				in_reportID,
                in_fby_user_id
		);
			
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addSkuFamily` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addSkuFamily`(
IN `in_fby_user_id` VARCHAR(128),
IN `in_sku` VARCHAR(128),
IN `in_skuFamily` VARCHAR(128)
)
BEGIN
 DECLARE isExists INT;
    
    SELECT 
        COUNT(*) INTO isExists
    FROM
        channelconnector.sku_family as sf
    WHERE
        sf.fby_user_id = in_fby_user_id
        AND sf.sku = in_sku
    LIMIT 1;
    
    IF (isExists = 0) THEN
        INSERT INTO sku_family (
            fby_user_id,
            sku,
            skuFamily
        )
        VALUES (
            in_fby_user_id,
            in_sku,
            in_skuFamily
        );
    END IF;  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `addStock` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `addStock`(
 IN `in_skuid` VARCHAR(128),
 IN `in_skucode` VARCHAR(128),
 IN `in_ean` VARCHAR(128),
 IN `in_qntity` INT(11),
 IN `in_priority` INT(11),
 IN `in_crnid` VARCHAR(100),
 IN `in_fby_id` VARCHAR(100)
)
BEGIN
-- CALL addStock('323451adasdasdsadasd','AD168','8436548246389',0,90,'f74c0dad-d909-47ee-a64d-2a3b695155fe',34);
-- CALL addStock(326706,"BP803","8432959878775",5,10,"8ce32f38-1bfb-4387-b94c-4f74a14a019e",39);
DECLARE in_channel_name varchar(256);
DECLARE var_isUpdated tinyint;
DECLARE var_isExists tinyint;
/*
DECLARE EXIT HANDLER FOR SQLEXCEPTION 
BEGIN
    ROLLBACK;
END;

-- START TRANSACTION;
-- */

SET in_channel_name = 
					(
						SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
						limit 1
                        FOR UPDATE
					);
SET SQL_SAFE_UPDATES = 0;


	
       
	IF EXISTS (
        SELECT 1 FROM temp_master_inventory AS T 
		WHERE 
			T.skucode = in_skucode AND
            T.fby_user_id = in_fby_id
		LIMIT 1 
        FOR UPDATE
    ) = 1 
    THEN
		-- Select 'Updated' as isExists;
        
		Update temp_master_inventory
		set barcode = in_ean,
		quantity = in_qntity,
		priority = in_priority,
		cron_id = in_crnid,
		updated_at = now()
		Where fby_user_id = in_fby_id and skucode = in_skucode ;
            
         SET var_isUpdated = 1; 
         SET var_isExists = 1;
    ELSE
		-- Select 'Inserted' as isExists;
    
		INSERT INTO temp_master_inventory(
			sku_id,skucode,barcode,quantity,priority,cron_id, 
			fby_user_id
		) 
		VALUES(
			in_skuid,in_skucode,in_ean,in_qntity,in_priority,in_crnid,
			in_fby_id
			);
		
         
         SET var_isExists = 0;
         
    END IF;
   
   /*
   IF(in_channel_name like '%shopify%')
   THEN
		
		IF EXISTS (
			SELECT 1  FROM products T
			WHere fby_user_id = in_fby_id and sku = in_skucode 
			and 1 = case when barcode is not null and barcode <> '' then case when barcode = in_ean then 1 else 0  end else 1 end
			limit 1
			FOR UPDATE
		)
		THEN
			-- Select 'shopify update' as isExists;	
			Update products
			set inventory_quantity = in_qntity , updated_at = NOW()
			WHere fby_user_id = in_fby_id and sku = in_skucode 
			and 1 = case when barcode is not null and barcode <> '' then case when barcode = in_ean then 1 else 0  end else 1 end
			; 
		
		SET var_isUpdated = 1;
		END IF;
   END IF; 
   */
 
   
  -- COMMIT;   

  SET SQL_SAFE_UPDATES = 1;
	/*   
    
    */
       /*
    SELECT DISTINCTROW
		TI.*,P.id
	FROM channelconnector.temp_master_inventory AS TI 
		
	LEFT JOIN channelconnector.products AS P
		ON  TI.skucode = P.sku
		AND TI.fby_user_id = P.fby_user_id
        -- AND P.barcode = TI.barcode
	WHERE
		TI.skucode = in_skucode AND
		TI.fby_user_id = in_fby_id;
        */
      Select var_isExists as isExists, in_channel_name,var_isUpdated;    
  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `assign_bin` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `assign_bin`(
    IN p_fby_user_id VARCHAR(128), 
    IN p_barcode VARCHAR(128), 
    IN p_bin_number VARCHAR(128),
    IN p_order_no VARCHAR(128),
    IN p_order_line_item_id VARCHAR(128),
    IN p_status VARCHAR(128)
)
BEGIN
	IF NOT EXISTS (
        SELECT 1 FROM bin_master
		WHERE 
		fby_user_id = p_fby_user_id
        AND order_line_item_id = p_order_line_item_id
		LIMIT 1 
    ) = 1 
     THEN
		INSERT INTO bin_master (fby_user_id, barcode, bin_number, order_no, order_line_item_id, status)
		VALUES (p_fby_user_id, p_barcode, p_bin_number, p_order_no, p_order_line_item_id, p_status);
	END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `CheckOrderNotified` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `CheckOrderNotified`(
    IN `in_order_no` VARCHAR(256),
    IN `in_fby_user_id` VARCHAR(256)
)
BEGIN
	IF EXISTS(select 1 from channelconnector.order_details where order_no = in_order_no and fby_user_id = in_fby_user_id)
    THEN
    BEGIN
		Select IsNotifiedFBY from channelconnector.order_details where order_no = in_order_no and fby_user_id = in_fby_user_id;
    END;
    ELSE
    BEGIN
		Select 1;
    END;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `createNewCronLogs` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `createNewCronLogs`(
   IN in_fby_user_id INT,
   IN in_cc_operation VARCHAR(256),
   IN in_cron_schedule VARCHAR(256),
   In in_url VARCHAR(256)
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;

INSERT INTO
	_cron(
		fby_user_id,
		cc_operation,
        cron_schedule,
        url,
        createdOn
        )
	VALUES
		(
        in_fby_user_id,
		in_cc_operation,
        in_cron_schedule,
        in_url,
		NOW()
        )
	ON DUPLICATE KEY
		UPDATE
		cc_operation = in_cc_operation,
        cron_schedule = in_cron_schedule,
        url = in_url;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `cronErrorLog` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `cronErrorLog`(
	IN `crn_name` VARCHAR(60), 
	IN `crnid` VARCHAR(100), 
	IN `err_type` VARCHAR(100), 
	IN `err_msg` TEXT, 
	IN `fby_id` VARCHAR(128)
)
BEGIN

	IF(CHAR_LENGTH(err_msg) > 2048) THEN
		set err_msg = SUBSTRING(err_msg,2048);
    END IF;
    
	INSERT INTO cron_error_log(fby_user_id,cron_name,cron_id,type_error,error_message) VALUES(fby_id,crn_name,crnid,err_type,err_msg);
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `deleteCronLogs` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `deleteCronLogs`(
   IN in_fby_user_id INT,
   IN in_cc_operation VARCHAR(256)
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    DELETE from _cron
    where fby_user_id = in_fby_user_id 
    and cc_operation = in_cc_operation;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `deleteProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `deleteProduct`(
`in_sku` VARCHAR(100)
)
BEGIN
SET SQL_SAFE_UPDATES = 0;
	IF EXISTS (
		SELECT 1 from channelconnector.createdproducts where sku = in_sku
    )
    THEN
		Delete from channelconnector.createdproducts where sku = in_sku;
     END IF; 
     
	IF EXISTS (
		SELECT 1 from channelconnector.createproducts where sku = in_sku
    )
    THEN
		Delete from channelconnector.createproducts where sku = in_sku;
        Delete from channelconnector.productType where sku = in_sku;
     END IF; 
     
	IF EXISTS (
		SELECT 1 from channelconnector.createdproductvariants where sku = in_sku
    )
    THEN
		Delete from channelconnector.createdproductvariants where sku = in_sku;
     END IF;   
     
	IF EXISTS (
		SELECT 1 from channelconnector.createproductvariants where sku = in_sku
    )
    THEN
		Delete from channelconnector.createproductvariants where sku = in_sku;
        Delete from channelconnector.Images where sku = in_sku;
     END IF;  

SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `deleteReports` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `deleteReports`(`in_fby_user_id` INT, `in_reportID` varchar(200))
BEGIN
SET SQL_SAFE_UPDATES = 0;
Delete 
FROM
    Amazon_Reports amr
WHERE
		amr.fby_user_id = in_fby_user_id
        AND
        amr.reportID = in_reportID;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `deleteStock` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `deleteStock`(
 IN `in_fby_user_id` INT
)
BEGIN
  -- COMMIT;   
	DECLARE lastNdays DATETIME;
    SET lastNdays = date_add(cast(NOW() as DATE), INTERVAL -3 DAY);
	IF EXISTS (
    SELECT 1 from temp_master_inventory where fby_user_id = in_fby_user_id
    -- and created_at < lastNdays
    limit 1
    )
    THEN
	SET SQL_SAFE_UPDATES = 0;
		Delete from temp_master_inventory where fby_user_id = in_fby_user_id;
        -- and created_at < lastNdays;
        update channelconnector.products Set previous_inventory_quantity = -1 Where fby_user_id= in_fby_user_id ;
        
	SET SQL_SAFE_UPDATES = 1;
	END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `fbyCanclOrderErrorManage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `fbyCanclOrderErrorManage`(IN `fby_id` VARCHAR(128), IN `ordr_number` VARCHAR(256), IN `skus` VARCHAR(256), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    IF exist = 1 THEN
      UPDATE order_details AS od SET od.count=od.count+1,od.updated_at=time WHERE od.cron_id=crn_id AND od.order_no=ordr_number AND od.sku=skus;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE order_details AS od SET od.fby_error_flag=1,od.count=1,od.cron_name=crn_name,od.cron_id=crn_id,od.updated_at=time WHERE od.fby_user_id=fby_id AND od.order_no=ordr_number AND od.sku=skus;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `fbyCronErrorManage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `fbyCronErrorManage`(`in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_err_type` VARCHAR(100), `in_err_msg` TEXT, `in_fby_id` VARCHAR(128), `in_exist` TINYINT(4) UNSIGNED)
BEGIN
	/*
	call channelconnector.fbyCronErrorManage
    (
    'in_crn_name',
    'in_crnid',
    'in_err_type',
    'in_err_msg',
    '14',
    '0'
    );
    Select * from bulk_process_error_log;
    */
	SET SQL_SAFE_UPDATES = 0;
    IF in_exist = 1 THEN
		UPDATE bulk_process_error_log AS cl 
		SET 
			cl.count = cl.count + 1,
			cl.type_error = in_err_type,
			cl.error_message = in_err_msg
		WHERE
			cl.cron_id = in_crnid
			AND cl.cron_name = in_crn_name;
    ELSE
		
		INSERT INTO bulk_process_error_log
        (
			fby_user_id,
			cron_name,
			cron_id,
			type_error,
			error_message
        ) 
        VALUES
        (
			in_fby_id,
            in_crn_name,
            in_crnid,
            in_err_type,
            in_err_msg
		);
        
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `fbyOrderErrorManage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `fbyOrderErrorManage`(IN `fby_id` VARCHAR(128), IN `ordr_no` VARCHAR(256), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)
BEGIN
    SET SQL_SAFE_UPDATES = 0;
	IF exist = 1 THEN
      UPDATE order_masters AS om SET om.count=om.count+1,om.updated_at=time WHERE om.cron_id=crn_id AND om.order_no=ordr_no;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE order_masters AS om SET om.fby_error_flag=1,om.count=1,om.cron_name=crn_name,om.cron_id=crn_id,om.updated_at=time WHERE om.fby_user_id=fby_id AND om.order_no=ordr_no;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `fbyOrderTrackErrorManage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `fbyOrderTrackErrorManage`(`in_fby_id` VARCHAR(128), `in_ordr_number` VARCHAR(256), `in_exist` TINYINT(4) UNSIGNED, `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100), `in_error_type` VARCHAR(60), `in_error_msg` TEXT, `in_time` DATETIME)
BEGIN
	/*
		
   CALL fbyOrderErrorManage('8','4667998896386',1,'send_Orders_Fby','809d8c84-1ea4-43bc-9a80-72221a89adc5','catch error','{\"4667998896386\":{\"ownerCode\":\"lowner con codice \\\"FDM\\\" non esiste.\"}}','2022-03-03 12:22:02');

	*/
    SET SQL_SAFE_UPDATES = 0;
    
    IF in_exist = 1 
    THEN
   
		UPDATE order_details AS od 
		SET 
			od.count = od.count + 1,
			od.updated_at = NOW()
		WHERE
			od.cron_id = in_crn_id
				AND od.order_no = in_ordr_number
				AND od.is_trackable = 1;
     
		UPDATE cron_error_log AS cl 
		SET 
			cl.type_error = in_error_type,
			cl.error_message = in_error_msg
		WHERE
			cl.cron_id = in_crn_id;
		
	ELSE
    
		UPDATE order_details AS od 
		SET 
			od.fby_error_flag = 1,
			od.count = 1,
			od.cron_name = in_crn_name,
			od.cron_id = in_crn_id,
			od.updated_at = NOW()
		WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_trackable = 1;
	END IF;
    
   
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `fbyProductErrorManage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `fbyProductErrorManage`(IN `fby_id` VARCHAR(128), IN `sku` VARCHAR(128), IN `exist` TINYINT(4) UNSIGNED, IN `crn_name` VARCHAR(60), IN `crn_id` VARCHAR(100), IN `error_type` VARCHAR(60), IN `error_msg` TEXT, IN `time` DATETIME)
BEGIN
SET SQL_SAFE_UPDATES = 0;
    IF exist = 1 THEN
      UPDATE products AS p SET p.count=p.count+1,p.updated_at=time WHERE p.cron_id=crn_id AND p.sku=sku;
      
      UPDATE cron_error_log AS cl SET cl.type_error=error_type,cl.error_message=error_msg WHERE cl.cron_id=crn_id;
    ELSE
     UPDATE products AS p SET p.fby_error_flag=1,p.count=1,p.cron_name=crn_name,p.cron_id=crn_id,p.updated_at=time WHERE p.fby_user_id=fby_id AND p.sku=sku;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getAlertCode` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getAlertCode`(IN `alert` VARCHAR(100), IN `chanel` VARCHAR(50))
BEGIN
SELECT * FROM map_channel_alert_code AS mca WHERE mca.ch_alert_code=alert and mca.channel=chanel;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getAuthUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getAuthUser`(
`in_email` VARCHAR(1024)
)
BEGIN
	SELECT * FROM channelconnector._4_user_login
    where email = in_email;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getBin` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getBin`(
	IN p_fby_user_id VARCHAR(128), 
    IN p_order_no VARCHAR(128)
)
BEGIN
	SELECT * FROM channelconnector.bin_master
    where fby_user_id = p_fby_user_id
    AND order_no = p_order_no;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getBlukCronByFlag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getBlukCronByFlag`(IN `casename` VARCHAR(60), IN `dt` DATETIME)
BEGIN
SELECT * FROM bulk_process_error_log WHERE cron_name=casename AND CAST(created_at AS DATE) = CAST(dt AS DATE) AND flag=1 AND count=1 ORDER BY id DESC LIMIT 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getBulkCronLog` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getBulkCronLog`(IN `fby_id` VARCHAR(128), IN `crn_name` VARCHAR(60), IN `dt` DATETIME)
BEGIN
SELECT * FROM bulk_process_error_log WHERE fby_user_id=fby_id AND cron_name=crn_name AND CAST(created_at AS DATE) = CAST(dt AS DATE) ORDER BY id DESC LIMIT 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCanceledOrderDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCanceledOrderDetails`(
	`in_fby_id` VARCHAR(128), 
	`in_acount_id` INT, 
	`in_chanel_code` VARCHAR(50), 
	`in_chanel` VARCHAR(100)

)
BEGIN
	-- call channelconnector.getCanceledOrderDetails (27,0,'','');
	SELECT DISTINCTROW 
			od.* , 
            om.payment_method ,
            om.ship_phone_number,
            om.ship_address_1,
            om.recipient_name,
            om.ship_company,
            om.ship_address_1,
            om.ship_address_2,
            om.ship_postal_code,
            om.ship_city,
            om.ship_state_code,
            om.ship_country,
            om.ship_country_code,
            om.buyer_email,
            om.payment_date,
            om.payment_transaction_id,
            CAST(om.total_order AS DECIMAL(10,2))as `total_order`,
            om.purchase_date,
            om.currency_code,
            om.fby_send_status,
            Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`
		FROM 
			order_details od
        INNER JOIN     order_masters om
			on om.fby_user_id = od.fby_user_id
            AND om.order_no = od.order_no
		INNER JOIN _2_channel as ch
		on ch.channelId = od.fby_user_id 
		and ch.channelId = in_fby_id
	WHERE
		(od.payment_status = 'partially_refunded'
			OR od.payment_status = 'refunded'
            OR od.payment_status = '7'
			OR od.payment_status like '%cancel%'
            OR od.order_status like '%cancel%'
            OR od.order_status = 'partially_refunded'
			OR od.order_status = 'refunded'
            OR od.order_status = '7'
		)
		AND (od.is_canceled_fby = 0 OR od.is_canceled_fby IS NULL)
		-- AND od.channel = in_chanel
		-- AND od.channel_code = in_chanel_code
		-- AND od.account_id = in_acount_id
        AND od.purchase_date > ch.orderSyncStartDate
		AND od.fby_user_id = in_fby_id
        -- AND od.order_no = 'order_ZXXqnajj70bqa'
        ORDER BY od.channel,od.order_no,od.order_line_item_id
        ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCancelOrderByFlag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCancelOrderByFlag`(
	IN `in_casename` VARCHAR(60)
)
BEGIN
	/*
    call channelconnector.getCancelOrderByFlag('send_Orders_Fby');
    */
    
	 /*SELECT * FROM order_details;# WHERE fby_error_flag=1 AND count=1 AND lower(cron_name) = lower(casename);*/
     SELECT * FROM order_details as OD 
     WHERE OD.fby_error_flag = 1 
     AND OD.count = 1
     AND lower(OD.cron_name) = lower(in_casename);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCancelReason` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCancelReason`(IN `cancel_reason` VARCHAR(100), IN `in_chanel` VARCHAR(50))
BEGIN
-- Call channelconnector.getCancelReason( 'fraud','Woocommerce-IT');
-- Call channelconnector.getCancelReason( 'customer','shopify');
IF EXISTS (
SELECT * FROM map_channel_cancel_reason AS mcc WHERE mcc.ch_cancel_reason=cancel_reason and mcc.channel=in_chanel
)
then 
	SELECT * FROM map_channel_cancel_reason AS mcc WHERE mcc.ch_cancel_reason=cancel_reason and mcc.channel=in_chanel;
ELSE
		SELECT 
			id, in_chanel as `channel`, ch_cancel_reason, fby_cancel_reason

        FROM map_channel_cancel_reason 
        Where fby_cancel_reason = 'RRIPCLI'
        limit 1;
end if;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getChannelDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getChannelDetails`(
IN `in_groupCode` VARCHAR(128) 
)
BEGIN
	SELECT * from _2_channel where groupCode = in_groupCode;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreatedProductById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreatedProductById`(
`in_fby_id` INT
)
BEGIN
	IF EXISTS (
		SELECT 1 from channelconnector.createdproducts where fby_user_id = in_fby_id
		limit 1
    )
    THEN
	SELECT 
		ccp.*, p.* 
	FROM 
		channelconnector.createproducts AS cp
    INNER JOIN
		channelconnector.createdproducts ccp 
	ON ccp.fby_user_id = cp.fby_user_id
		AND ccp.sku = cp.sku
		AND ccp.fby_user_id = cp.fby_user_id
		AND cp.fby_user_id = in_fby_id
	LEFT JOIN
        product_units p ON cp.fby_user_id = p.fby_user_id 
            AND cp.sku = p.sku;    
   /*INNER JOIN    
   channelconnector.Images img
   ON ccp.fby_user_id = cp.fby_user_id
   AND ccp.sku = img.sku
   	WHERE
        cp.fby_user_id = in_fby_id; */
	END IF; 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreatedProductIDBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreatedProductIDBySku`(`in_sku` VARCHAR(100))
BEGIN
	SELECT 
		* 
	FROM createdproducts as cp
    WHERE 
		in_sku like CONCAT('%',cp.sku,'%');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreatedProductVariantByDomain` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreatedProductVariantByDomain`(
`in_fby_id` INT, `in_dom` VARCHAR(64)
)
BEGIN

	SELECT 
		* 
	FROM 
	channelconnector.createdproductvariants ccp 
	WHERE
        fby_user_id = in_fby_id;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreatedProductVariantBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreatedProductVariantBySku`(
`in_sku` VARCHAR(100)
)
BEGIN
	SELECT 
    cpp.id, cpp.fby_user_id, cpp.channel, cpp.domain, cpp.owner_code, 
    cpp.sku, cpp.barcode, cpp.item_id, cpp.title, cpp.item_product_id, cpp.inventory_item_id, cpp.location_id, cpp.previous_inventory_quantity, cpp.inventory_quantity, 
    cpp.image, cpp.price, cpp.count, cpp.fby_error_flag, cpp.status, 
    cpp.cron_name, cpp.cron_id, cpp.created_at, cpp.updated_at, 
    cpp.isChanged, cpp.description, 
    cpp.specialPrice
	FROM createdproductvariants as cpp
    WHERE 
		cpp.sku = in_sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreatedProductVariantBySkuFamily` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreatedProductVariantBySkuFamily`(
	`in_sku` VARCHAR(100),
	`in_item_product_id` VARCHAR(100)
)
BEGIN
   IF(in_sku is not null)
   THEN
	SELECT cpp.*, cp.sku as skuFamily, im.image as image_url
	FROM channelconnector.createdproductvariants as cpp
    INNER JOIN channelconnector.createdproducts as cp
    on cp.item_id = cpp.item_product_id
    INNER JOIN channelconnector.Images as im
    on im.channel_image_id = cpp.image and
    im.sku = cpp.sku
    WHERE 
		 cp.sku = in_sku;
  ELSE
		SELECT cpp.*, cp.sku as skuFamily, im.image as image_url
		FROM channelconnector.createdproductvariants as cpp
		INNER JOIN channelconnector.createdproducts as cp
		on cp.item_id = cpp.item_product_id
		INNER JOIN channelconnector.Images as im
		on im.channel_image_id = cpp.image and
		im.sku = cpp.sku
		WHERE 
		cp.item_product_id = in_item_product_id;
	END IF;
    END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductByDomain` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductByDomain`(`in_fby_id` INT, `in_dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductByDomain (1006,'');
    
    SELECT 'Req',
		cp.* 
	FROM createproducts  as cp
    UNION
    SELECT 'Res',
		cp1.* 
	FROM createdproducts  as cp1;
    
    */
	SELECT 
		cp.*, p.*
	FROM
		createproducts AS cp
			LEFT JOIN
		createdproducts ccp ON ccp.fby_user_id = cp.fby_user_id
			AND ccp.sku = cp.sku
			AND ccp.fby_user_id = cp.fby_user_id
			AND cp.fby_user_id = in_fby_id
            LEFT JOIN
        product_units p ON cp.fby_user_id = p.fby_user_id 
            AND cp.sku = p.sku
	WHERE
		ccp.sku IS NULL
        AND cp.fby_user_id = in_fby_id
        ; 
		
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductById`(
	`in_fby_user_id` INT
)
BEGIN

	SELECT 
		cp.*, pu.*,
        (
			SELECT SUM(inventory_quantity) 
			FROM createproductvariants p
            INNER JOIN sku_family s
            ON p.fby_user_id = s.fby_user_id
            AND p.sku = s.sku
            WHERE p.fby_user_id = cp.fby_user_id
            AND s.skuFamily = cp.sku
		) AS quantity,
        (
			SELECT COUNT(*) 
			FROM createproductvariants p
            INNER JOIN sku_family s
            ON p.fby_user_id = s.fby_user_id
            AND p.sku = s.sku
            WHERE p.fby_user_id = cp.fby_user_id 
            AND s.skuFamily = cp.sku
		) AS count,
        (
			SELECT p.price as variantPrice
            FROM createproductvariants p
			INNER JOIN sku_family s
            ON p.fby_user_id = s.fby_user_id
            AND p.sku = s.sku
            WHERE p.fby_user_id = cp.fby_user_id 
            AND s.skuFamily = cp.sku
            LIMIT 1
        ) AS variantPrice,
		(
			SELECT 
			CASE 
				WHEN COUNT(*) > 0 THEN 'published'
				ELSE 'unpublished'
			END
		FROM createdproducts
		WHERE fby_user_id = cp.fby_user_id 
		AND sku = cp.sku
    ) as channel_status
	FROM
		createproducts AS cp
            LEFT JOIN
        product_units pu ON cp.fby_user_id = pu.fby_user_id 
            AND cp.sku = pu.sku
	WHERE
			cp.fby_user_id = in_fby_user_id
        ; 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductBySku`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_sku` VARCHAR(128)
)
BEGIN
	SELECT 
		* 
	FROM createproducts as cp
    INNER JOIN channelconnector.product_units as pu
		on cp.sku = pu.sku and
		cp.fby_user_id = pu.fby_user_id
	LEFT JOIN channelconnector.option_table as ot
		on cp.sku = ot.sku and
		cp.fby_user_id = ot.fby_user_id    
    WHERE 
		in_sku like CONCAT('%',cp.sku,'%');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductVariantByDomain` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductVariantByDomain`(`in_fby_id` INT, `in_dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getCreateProductVariantByDomain (1002,'storeden');
    */
	SELECT 
		cp.* 
	FROM 
		createproductvariants AS cp
    LEFT JOIN
		createdproductvariants ccp 
	ON ccp.fby_user_id = cp.fby_user_id
		AND ccp.sku = cp.sku
		AND ccp.fby_user_id = cp.fby_user_id
		AND cp.fby_user_id = in_fby_id
	WHERE
		ccp.sku IS NULL
        AND cp.fby_user_id = in_fby_id;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductVariantById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductVariantById`(
	`in_fby_id` INT
)
BEGIN
	SELECT 
		ccp.*, 
        ot.option_1_name, 
        ot.option_1_value, 
        ot.option_2_name, 
        ot.option_2_value,
        cp.sku as skyFamily, 
        cp.barcode, 
        cp.item_id, 
        cp.title, 
        cp.item_product_id, 
        cp.inventory_item_id, 
        cp.location_id, 
        cp.previous_inventory_quantity, 
        cp.inventory_quantity, 
        cp.image, 
        cp.price, 
        cp.count, cp.fby_error_flag, 
        cp.status, 
        cp.cron_name, 
        cp.cron_id, 
        cp.created_at, 
        cp.updated_at, cp.isChanged, 
        cp.description, 
        cp.specialPrice, 
        pu.product_type,
        pu.brand,
        pu.weight_unit, 
        pu.weight_value, 
        pu.dimensions_unit, 
        pu.dimensions_width, 
        pu.dimensions_height, 
        pu.dimensions_length, 
        pu.tags, pu.category, 
        pu.asin, pu.product_status
	FROM 
		createproductvariants AS ccp
    INNER JOIN
    	sku_family as sf
    ON ccp.sku = sf.sku
	LEFT JOIN
    	option_table as ot
    ON ccp.sku = ot.sku
    INNER JOIN
		createproducts cp 
	ON ccp.fby_user_id = cp.fby_user_id
		AND sf.skuFamily = cp.sku
	INNER JOIN
		product_units pu 
	ON cp.fby_user_id = pu.fby_user_id
		AND pu.sku = cp.sku
	WHERE
        cp.fby_user_id = in_fby_id;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductVariantBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductVariantBySku`(
`in_sku` VARCHAR(100)
)
BEGIN
	SELECT 
		* 
	FROM createproductvariants as cp
    WHERE 
		in_sku like CONCAT('%',cp.sku,'%');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCreateProductVariantBySkuFamily` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCreateProductVariantBySkuFamily`(
`in_sku` VARCHAR(100)
)
BEGIN
	SELECT 
		* 
	FROM createproductvariants as cp
    INNER JOIN sku_family as sf
    ON cp.sku = sf.sku
	LEFT JOIN option_table as ot
    ON cp.sku = ot.sku AND
    cp.fby_user_id = ot.fby_user_id
    WHERE 
		sf.skuFamily = in_sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getCronLogs` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getCronLogs`(
   IN in_fby_user_id INT,
   IN in_cc_operation VARCHAR(256)
)
BEGIN

SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED ;
SELECT DISTINCTROW
    ch.channelName,
    ch.platformName,
    ch.groupCode,
    ch.ownerCode,
    c.fby_user_id,
    c.cc_operation,
    c.cron_schedule
FROM
    channelconnector._cron c
        INNER JOIN
    channelconnector._2_channel ch ON ch.channelId = c.fby_user_id
        AND ch.isActive = 1
WHERE
    1 = IF(in_fby_user_id > 0,
        IF(fby_user_id = in_fby_user_id, 1, 0),
        1)
        AND 1 = IF(in_cc_operation IS NOT NULL
            AND in_cc_operation <> '',
        IF(in_cc_operation = cc_operation, 1, 0),
        1)
ORDER BY fby_user_id , cc_operation;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getEbayOrderDetailTracking` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getEbayOrderDetailTracking`(
	IN `in_fby_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256),
	IN `in_channel` VARCHAR(128)
)
BEGIN
SELECT * FROM order_details 
    WHERE fby_user_id = in_fby_id 
    AND order_no = in_order_no 
    AND is_trackable = 1
    AND channel = in_channel
    AND order_details.status = 0
    LIMIT 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getImageBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getImageBySku`(
`in_fby_user_id` VARCHAR(128), 
`in_sku` VARCHAR(128)
)
BEGIN
/*

CALL getImageBySku(8,'TestSku);

*/
SELECT `i`.`id`,
    `i`.`sku`,
    `i`.`image`,
    `i`.`skuFamily`,
    `i`.`fby_user_id`,
    `i`.`channel_image_id`,
    `i`.`imageOrder`,
    `i`.`createdOn`
FROM
    Images AS i
WHERE
		(i.image IS NOT NULL AND i.image != '')
        AND i.fby_user_id = in_fby_user_id
        AND i.sku = in_sku;      
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getImages` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getImages`(`in_fby_user_id` INT)
BEGIN

/*

CALL getImages(41);

*/

Select DISTINCTROW img.*
FROM
(
SELECT `i`.`id`,
    `i`.`sku`,
    `i`.`image`,
    `i`.`skuFamily`,
    `i`.`fby_user_id`,
    `i`.`channel_image_id`,
    `i`.`imageOrder` as imageOrderInserted,
    `i`.`createdOn`,
    ROW_NUMBER() OVER(PARTITION BY i.sku order by i.imageOrder,i.sku,i.id) AS `imageOrder`
FROM
    Images AS i
        INNER JOIN
    createproductvariants AS v ON v.sku = i.sku
WHERE
		(v.image IS NULL OR v.image = '')
        AND v.fby_user_id = in_fby_user_id
        AND (i.channel_image_id IS NULL      OR i.channel_image_id = ''  OR i.channel_image_id = 0   )
/*        
union

SELECT 
    i.*,
    ROW_NUMBER() OVER(PARTITION BY i.sku order by i.sku,i.id) AS imageOrder
FROM
    Images AS i
        INNER JOIN
    channelconnector.createdproducts AS v ON v.sku = i.sku
WHERE
		(v.image IS NULL OR v.image = '')
        AND v.fby_user_id = in_fby_user_id
        AND (i.channel_image_id IS NULL      OR i.channel_image_id = ''  OR i.channel_image_id = 0   )
*/        
) as img
WHERE
img.fby_user_id = in_fby_user_id
AND (img.channel_image_id IS NULL      OR img.channel_image_id = ''  OR img.channel_image_id = 0   )        
order by img.fby_user_id, img.sku,img.id;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getLastSyncOperationTime` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getLastSyncOperationTime`(
    IN `in_fby_user_id` VARCHAR(256),
    IN `in_CC_OPERATION` VARCHAR(256)
)
BEGIN

	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET in_CC_OPERATION = UPPER(in_CC_OPERATION);
    
    IF EXISTS (
		SELECT 
			last_opearion_sync_time,
			fby_user_id
		FROM
			_3_last_operation_sync_time as t
		WHERE
			fby_user_id = in_fby_user_id
			AND UPPER(t.cc_operation) = in_CC_OPERATION
            
    ) THEN 
    
   
		SELECT 
			CAST(last_opearion_sync_time AS CHAR) AS last_opearion_sync_time,
            page_count,
			fby_user_id,
            cc_operation
		FROM
			`channelconnector`.`_3_last_operation_sync_time` as t 
		WHERE
			fby_user_id = in_fby_user_id
			AND UPPER(t.cc_operation) = in_CC_OPERATION
        ORDER BY  last_opearion_sync_time DESC
        LIMIT 1
            ;
    
    ELSE
		SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
        
        INSERT INTO _3_last_operation_sync_time(
			fby_user_id,last_opearion_sync_time,cc_operation,created_at,updated_at
		) 
		VALUES(
			in_fby_user_id,CAST(DATE_ADD(NOW(), INTERVAL - 2 HOUR) AS CHAR),UPPER(in_CC_OPERATION),now(),now()
			);
            
        SELECT 
		CAST(DATE_ADD(NOW(), INTERVAL - 2 HOUR) AS CHAR) AS last_opearion_sync_time,
        page_count,
		in_fby_user_id AS `fby_user_id`,
        UPPER(in_CC_OPERATION) as cc_operation
        
        ;
		
    END IF    ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getLocationId` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getLocationId`(IN `fby_id` VARCHAR(128), IN `skuid` VARCHAR(256), IN `in_order_item_id` VARCHAR(256))
BEGIN

	/*
		call channelconnector.getLocationId('OSM21A008' ,9,'41090369585309');
    */
	SELECT 
		*
	FROM
		products
	WHERE
		sku = skuid 
		AND fby_user_id = fby_id
		and item_id = in_order_item_id;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOptions` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOptions`(
IN `in_fby_user_id` VARCHAR(128), 
IN `in_sku` VARCHAR(128)
)
BEGIN
	SELECT * FROM option_table ot 
	INNER JOIN sku_family as sf
    ON ot.sku = sf.sku
    where 
    ot.fby_user_id = in_fby_user_id AND sf.skuFamily=in_sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderByAccount` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderByAccount`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_account_id` VARCHAR(256) 
	-- IN `in_channel_code` VARCHAR(20)
)
BEGIN
/*,
call channelconnector.getOrderByAccount(8,17);
*/
SET SQL_SAFE_UPDATES = 0;
	SELECT  
		om.`id`,
    om.`channel`,
    om.`channel_code`,
    om.`owner_code`,
    om.`fby_user_id`,
    om.`account_id`,
    om.`order_no`,
    om.`seller_order_id`,
    om.`purchase_date`,
    om.`payment_date`,
    om.`local_time`,
    om.`SalesChannel`,
    om.`shipping_method`,
    om.`recipient_name`,
    om.`ship_company`,
    om.`ship_address_1`,
    om.`ship_address_2`,
    om.`ship_city`,
    om.`ship_state`,
    om.`ship_state_code`,
    om.`ship_postal_code`,
    om.`ship_country`,
    om.`ship_country_code`,
    om.`ship_phone_number`,
    om.`total_order`,
    om.`total_items`,
    om.`total_items_price`,
    om.`total_shipping_price`,
    om.`total_shipping_tax`,
    om.`total_tax`,
    om.`total_discount`,
    om.`payment_transaction_id`,
	om.`bill_generator_name`,
    om.`bill_company`,
	om.`bill_address_1`,
	om.`bill_address_2`,
	om.`bill_city`,
	om.`bill_state`,
	om.`bill_state_code`,
	om.`bill_postal_code`,
	om.`bill_country`,
	om.`bill_country_code`,
	om.`bill_phone_number`,
    Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
			om.`currency_code`,
			om.`buyer_id`,
			om.`buyer_email`,
			om.`buyer_name`,
			om.`product_details`,
			om.`sales_record_no`,
			om.`payment_status`,
			om.`order_status`,
			om.`is_canceled`,
			om.`fby_send_status`,
			om.`count`,
			om.`fby_error_flag`,
			om.`cron_name`,
			om.`cron_id`,
			om.`created_at`,
			om.`updated_at`
    
    FROM order_masters AS om 
	WHERE 
		om.fby_user_id = in_fby_user_id; 
		-- AND OM.account_id = in_account_id 
		-- AND OM.channel_code = in_channel_code;
		
SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderByFlag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderByFlag`(IN `casename` VARCHAR(60))
BEGIN
	 SELECT * FROM order_masters WHERE fby_error_flag=1 AND count=1 AND cron_name=casename;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderByLineItem` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderByLineItem`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256),
    IN `in_order_line_item_id` VARCHAR(256)
)
BEGIN
SET SQL_SAFE_UPDATES = 0;
	SELECT  
		*
    FROM order_details
	WHERE 
		fby_user_id = in_fby_user_id 
        AND order_no = in_order_no
        AND order_line_item_id = in_order_line_item_id;

SET SQL_SAFE_UPDATES = 1;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderByOrderNumber` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderByOrderNumber`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256) 
)
BEGIN
SET SQL_SAFE_UPDATES = 0;
	SELECT  
		om.`id`,
    om.`channel`,
    om.`channel_code`,
    om.`owner_code`,
    om.`fby_user_id`,
    om.`account_id`,
    om.`order_no`,
    om.`seller_order_id`,
    om.`purchase_date`,
    om.`payment_date`,
    om.`local_time`,
    om.`SalesChannel`,
    om.`shipping_method`,
    om.`recipient_name`,
    om.`ship_company`,
    om.`ship_address_1`,
    om.`ship_address_2`,
    om.`ship_city`,
    om.`ship_state`,
    om.`ship_state_code`,
    om.`ship_postal_code`,
    om.`ship_country`,
    om.`ship_country_code`,
    om.`ship_phone_number`,
    om.`total_order`,
    om.`total_items`,
    om.`total_items_price`,
    om.`total_shipping_price`,
    om.`total_shipping_tax`,
    om.`total_tax`,
    om.`total_discount`,
    om.`payment_transaction_id`,
	om.`bill_generator_name`,
    om.`bill_company`,
	om.`bill_address_1`,
	om.`bill_address_2`,
	om.`bill_city`,
	om.`bill_state`,
	om.`bill_state_code`,
	om.`bill_postal_code`,
	om.`bill_country`,
	om.`bill_country_code`,
	om.`bill_phone_number`,
    Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
			om.`currency_code`,
			om.`buyer_id`,
			om.`buyer_email`,
			om.`buyer_name`,
			om.`product_details`,
			om.`sales_record_no`,
			om.`payment_status`,
			om.`order_status`,
			om.`is_canceled`,
			om.`fby_send_status`,
			om.`count`,
			om.`fby_error_flag`,
			om.`cron_name`,
			om.`cron_id`,
			om.`created_at`,
			om.`updated_at`
    
    FROM order_masters AS om 
	WHERE 
		om.fby_user_id = in_fby_user_id
		AND om.order_no = in_order_no; 

		
SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderByStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderByStatus`(
	IN `in_fby_user_id` VARCHAR(128)
)
BEGIN
	/*
		call channelconnector.getOrderByStatus(27);
    */
	SELECT DISTINCTROW 
    
    om.`id`,
    om.`channel`,
    om.`channel_code`,
    om.`owner_code`,
    om.`fby_user_id`,
    om.`account_id`,
    om.`order_no`,
    om.`seller_order_id`,
    om.`purchase_date`,
    om.`payment_date`,
    om.`local_time`,
    om.`SalesChannel`,
    om.`shipping_method`,
    om.`recipient_name`,
    om.`ship_company`,
    om.`ship_address_1`,
    om.`ship_address_2`,
    om.`ship_city`,
    om.`ship_state`,
    om.`ship_state_code`,
    om.`ship_postal_code`,
    om.`ship_country`,
    om.`ship_country_code`,
    om.`ship_phone_number`,
    om.`total_order`,
    om.`total_items`,
    om.`total_items_price`,
    om.`total_shipping_price`,
    om.`total_shipping_tax`,
    om.`total_tax`,
    om.`total_discount`,
    om.`payment_transaction_id`,
    Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
    om.`currency_code`,
    om.`buyer_id`,
    om.`buyer_email`,
    om.`buyer_name`,
    om.`product_details`,
    om.`sales_record_no`,
    om.`payment_status`,
    om.`order_status`,
    om.`is_canceled`,
    om.`fby_send_status`,
    om.`count`,
    om.`fby_error_flag`,
    om.`cron_name`,
    om.`cron_id`,
    om.`created_at`,
    om.`updated_at`,
	ch.orderSyncStartDate,
    if(om.managedByChannel is null,0,om.managedByChannel) as managedByChannel,
	om.`bill_generator_name`,
    om.`bill_company`,
	om.`bill_address_1`,
	om.`bill_address_2`,
	om.`bill_city`,
	om.`bill_state`,
	om.`bill_state_code`,
	om.`bill_postal_code`,
	om.`bill_country`,
	om.`bill_country_code`,
	om.`bill_phone_number`
    
    FROM order_masters as om
    inner join order_details as od
    on od.fby_user_id = om.fby_user_id
    and od.order_no = om.order_no
    INNER JOIN _2_channel as ch
    on ch.channelId = om.fby_user_id 
    and ch.channelId = in_fby_user_id
    WHERE 
		(recipient_name IS NOT NULL AND  recipient_name <> '')
        /*
        AND payment_date IS NOT NULL
        */
		AND om.fby_user_id = in_fby_user_id 
        AND om.fby_send_status = 0
        AND (od.sku is not null and od.sku <> '')
        AND om.purchase_date >= ch.orderSyncStartDate
	Order by om.fby_user_id, om.order_no;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderDetails`(IN `fby_id` VARCHAR(128), IN `odr_no` VARCHAR(256))
BEGIN
	-- call channelconnector.getOrderDetails (27,'order_XKAoGmXVeZt86');
		 SELECT DISTINCTROW 
			od.* , 
            -- om.payment_method ,
            Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `payment_method`,
            om.ship_phone_number,
            om.ship_address_1,
            om.recipient_name,
            om.ship_company,
            om.ship_address_1,
            om.ship_address_2,
            om.ship_postal_code,
            om.ship_city,
            om.ship_state_code,
            om.ship_country,
            om.ship_country_code,
            om.buyer_email,
            om.payment_date,
            om.payment_transaction_id,
            CAST(om.total_order AS DECIMAL(10,2))as `total_order`,
            om.purchase_date,
            om.currency_code,
            om.fby_send_status,
			om.bill_generator_name,
            om.bill_company,
			om.bill_address_1,
			om.bill_address_2,
			om.bill_city,
			om.bill_state,
			om.bill_state_code,
			om.bill_postal_code,
			om.bill_country,
			om.bill_country_code,
			om.bill_phone_number,
            om.total_items_price,
            om.total_tax,
            Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
            od.quantity_purchased,
            cp.image,
            cp.title
		FROM 
			order_details od
        INNER JOIN  order_masters om
			on om.fby_user_id = od.fby_user_id
            AND om.order_no = od.order_no
		LEFT JOIN  createproductvariants cp
			on cp.fby_user_id = od.fby_user_id
            AND od.sku = cp.sku    
		WHERE 
			od.fby_user_id = fby_id 
            AND od.order_no = odr_no;
            
       
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderDetailsTracking` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderDetailsTracking`(
	IN `in_fby_user_id` VARCHAR(128), 
    IN `in_order_no` VARCHAR(256)
)
BEGIN
	/*  
        call channelconnector.getOrderForTracking(1002);
		CALL `channelconnector`.`getOrderDetailsTracking`(39, '4992090996965');
    */
    DECLARE in_channel_name varchar(256);
    DECLARE var_platform_name varchar(256);
    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    SET in_channel_name = 
					(
						SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
						limit 1
                       -- FOR UPDATE
					);
                    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    SET var_platform_name = 
					(
						SELECT lower(platformName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
						limit 1
                       -- FOR UPDATE
					);                
    /*
    IF(in_channel_name like '%shopify%')
    THEN
    */
	SELECT DISTINCTROW
    od.`channel`,
    od.`channel_code`,
    od.`owner_code`,
    od.`fby_user_id`,
    od.`account_id`,
    od.`order_no`,
    od.`location_id`,
    od.`seller_order_id`,
    LEFT(REPLACE(GROUP_CONCAT(DISTINCT od.`order_line_item_id`, ','
                ORDER BY od.`order_line_item_id`),
            ',,',
            ','),
        LENGTH(REPLACE(GROUP_CONCAT(DISTINCT od.`order_line_item_id`, ','
                        ORDER BY od.`order_line_item_id`),
                    ',,',
                    ',')) - 1) AS order_line_item_id,
    LEFT(REPLACE(GROUP_CONCAT(DISTINCT od.`sku`, ','
                ORDER BY od.`order_line_item_id`),
            ',,',
            ','),
        LENGTH(REPLACE(GROUP_CONCAT(DISTINCT od.`sku`, ','
                        ORDER BY od.`order_line_item_id`),
                    ',,',
                    ',')) - 1) AS sku,
    CASE
        WHEN
            tm.mirakl_carrier_code IS NOT NULL
                && LOWER(ch.platformName) LIKE 'mirakl%'
        THEN
            tm.mirakl_carrier_code
        ELSE CASE
            WHEN
                tm.mirakl_carrier_code IS NULL
                    && LOWER(ch.platformName) LIKE 'mirakl%'
            THEN
                od.`tracking_courier` -- 'Example Carrier'
            ELSE CASE
                WHEN LOWER(ch.platformName) NOT LIKE 'mirakl%' THEN od.`tracking_courier`
                ELSE od.`tracking_courier`
            END
        END
    END AS tracking_courier,
    od.`tracking_id`,
    CASE
        WHEN
            tm.mirakl_carrier_code IS NULL
                && LOWER(ch.platformName) LIKE 'mirakl%'
        THEN
            od.`tracking_url` -- 'https://xxx.com/1234567'
        ELSE od.`tracking_url`
    END AS tracking_url,
    od.`is_trackable`,
    od.`order_status`,
    od.`fby_error_flag`,
    od.`cron_name`,
    od.`cron_id`,
    od.`status`,
    od.`IsNotifiedFBY`,
    od.`original_Channel_OrderId`,
    od.fulfillment_order_id,
    LEFT(REPLACE(GROUP_CONCAT(od.`quantity_purchased`, ','
                ORDER BY od.`order_line_item_id`),
            ',,',
            ','),
        LENGTH(REPLACE(GROUP_CONCAT(od.`quantity_purchased`, ','
                        ORDER BY od.`order_line_item_id`),
                    ',,',
                    ',')) - 1) AS quantities,
    LEFT(REPLACE(GROUP_CONCAT(DISTINCT od.`fulfillment_order_line_item_Id`, ','
                ORDER BY od.`order_line_item_id`),
            ',,',
            ','),
        LENGTH(REPLACE(GROUP_CONCAT(DISTINCT od.`fulfillment_order_line_item_Id`, ','
                        ORDER BY od.`order_line_item_id`),
                    ',,',
                    ',')) - 1) AS fulfillment_order_line_item_Ids,
                    if(tm.mirakl_carrier_code is null, 0 , 1) as isTrackingMapped
	FROM
		`channelconnector`.`order_details` AS od
			INNER JOIN
		_2_channel AS ch ON ch.channelId = od.fby_user_id
			LEFT JOIN
		channelconnector.tracking_carrier_master_mapping AS tm ON tm.fby_user_id = od.fby_user_id
			AND tm.fby_mapped_tracking_carrier_code = od.tracking_courier
	WHERE od.fby_user_id = in_fby_user_id 
		AND 1 = case when od.order_no = in_order_no || in_order_no is NULL then 1 else 0 end
		AND od.is_trackable = 1 
		AND od.status = 0
	GROUP BY od.`channel` , od.`channel_code` , od.`owner_code` , od.`fby_user_id` , od.`account_id` , od.`order_no` , od.`location_id` , od.`seller_order_id` , od.`tracking_courier` , od.`tracking_id` , od.`tracking_url` , od.`is_trackable` , od.`order_status` , od.`fby_error_flag` , od.`cron_name` , od.`cron_id` , od.`status` , od.`IsNotifiedFBY` , od.`original_Channel_OrderId`, od.fulfillment_order_id	
	Order by od.created_at
	;

    /*
    ELSE
    
		SELECT od.* 
		FROM order_details as od
		WHERE od.fby_user_id = in_fby_user_id 
		AND 1 = case when od.order_no = in_order_no || in_order_no is NULL then 1 else 0 end
		AND od.is_trackable = 1 
		AND od.status = 0;
    END IF;
    */
    
    -- Select in_channel_name;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderDetailsV1` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderDetailsV1`(
	IN `in_fby_user_id` VARCHAR(128) 
)
BEGIN

SET SQL_SAFE_UPDATES = 0;
	SELECT  
		*
    FROM order_details
	WHERE 
		fby_user_id = in_fby_user_id; 

SET SQL_SAFE_UPDATES = 1;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderForTracking` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderForTracking`(
	IN `in_fby_user_id` VARCHAR(128)
)
BEGIN
/*,

call channelconnector.getOrderForTracking(27);

*/
DECLARE var_channel_name varchar(500);
SET SQL_SAFE_UPDATES = 0;

SET var_channel_name = (
SELECT LOWER(channelName) FROM channelconnector._2_channel
Where isActive = 1 and isEnabled = 1
AND channelId = in_fby_user_id
LIMIT 1
);

IF (var_channel_name like 'woo%comm%') 
THEN
	SELECT DISTINCT 
		om.`id`,
    om.`channel`,
    om.`channel_code`,
    om.`owner_code`,
    om.`fby_user_id`,
    om.`account_id`,
    om.`order_no`,
    om.`seller_order_id`,
    om.`purchase_date`,
    om.`payment_date`,
    om.`local_time`,
    om.`SalesChannel`,
    om.`shipping_method`,
    om.`recipient_name`,
    om.`ship_company`,
    om.`ship_address_1`,
    om.`ship_address_2`,
    om.`ship_city`,
    om.`ship_state`,
    om.`ship_state_code`,
    om.`ship_postal_code`,
    om.`ship_country`,
    om.`ship_country_code`,
    om.`ship_phone_number`,
    om.`total_order`,
    om.`total_items`,
    om.`total_items_price`,
    om.`total_shipping_price`,
    om.`total_shipping_tax`,
    om.`total_tax`,
    om.`total_discount`,
    om.`payment_transaction_id`,
    Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
    om.`currency_code`,
    om.`buyer_id`,
    om.`buyer_email`,
    om.`buyer_name`,
    om.`product_details`,
    om.`sales_record_no`,
    om.`payment_status`,
    om.`order_status`,
    om.`is_canceled`,
    om.`fby_send_status`,
    om.`count`,
    om.`fby_error_flag`,
    om.`cron_name`,
    om.`cron_id`,
    om.`created_at`,
    om.`updated_at`
        
        , od.status
    FROM order_masters AS om 
    inner join order_details od 
		on od.fby_user_id = om.fby_user_id
		AND od.order_no = om.order_no
    INNER JOIN (
		SELECT DISTINCT 
			om1.fby_user_id,om1.order_no 
		FROM order_masters AS om1 
		inner join order_details od1 
			on od1.fby_user_id = om1.fby_user_id
			AND od1.order_no = om1.order_no
		WHERE 
			om1.fby_user_id = in_fby_user_id
			
			AND om1.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
			AND od1.status = 0  -- SET TO 1 when traking is updated at channel
			AND is_trackable = 1
            Order by om1.seller_order_id
            LIMIT 15

        )    as OM2
        ON OM2.fby_user_id = om.fby_user_id AND OM2.order_no = om.order_no 
        
	WHERE 
		om.fby_user_id = in_fby_user_id
        
        AND om.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
        AND od.status = 0  -- SET TO 1 when traking is updated at channel
        AND is_trackable = 1
         
	Order by om.seller_order_id DESC;

ELSE

	SELECT DISTINCT 
		om.`id`,
    om.`channel`,
    om.`channel_code`,
    om.`owner_code`,
    om.`fby_user_id`,
    om.`account_id`,
    om.`order_no`,
    om.`seller_order_id`,
    om.`purchase_date`,
    om.`payment_date`,
    om.`local_time`,
    om.`SalesChannel`,
    om.`shipping_method`,
    om.`recipient_name`,
    om.`ship_company`,
    om.`ship_address_1`,
    om.`ship_address_2`,
    om.`ship_city`,
    om.`ship_state`,
    om.`ship_state_code`,
    om.`ship_postal_code`,
    om.`ship_country`,
    om.`ship_country_code`,
    om.`ship_phone_number`,
    om.`total_order`,
    om.`total_items`,
    om.`total_items_price`,
    om.`total_shipping_price`,
    om.`total_shipping_tax`,
    om.`total_tax`,
    om.`total_discount`,
    om.`payment_transaction_id`,
    Case when  
            (
			Select fby_payment_method from map_channel_payment_method pm
			Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
			limit 1
			)  IS NOT NULL  
            
            THEN 
				(
				Select fby_payment_method from map_channel_payment_method pm
				Where pm.ch_payment_method = om.payment_method and pm.channel = om.channel
				limit 1
				) 
            ELSE
				om.payment_method 
            END
            as `fby_payment_method`,
    om.`currency_code`,
    om.`buyer_id`,
    om.`buyer_email`,
    om.`buyer_name`,
    om.`product_details`,
    om.`sales_record_no`,
    om.`payment_status`,
    om.`order_status`,
    om.`is_canceled`,
    om.`fby_send_status`,
    om.`count`,
    om.`fby_error_flag`,
    om.`cron_name`,
    om.`cron_id`,
    om.`created_at`,
    om.`updated_at`
        
        , od.status
    FROM order_masters AS om 
    inner join order_details od 
		on od.fby_user_id = om.fby_user_id
		AND od.order_no = om.order_no
	WHERE 
		om.fby_user_id = in_fby_user_id
        
        AND om.fby_send_status = 1 -- SET TO 1 when order is pushed to fby
        AND od.status = 0  -- SET TO 1 when traking is updated at channel
        AND is_trackable = 1
        
	Order by om.order_no;
		-- AND om.account_id = in_account_id 
		-- AND om.channel_code = in_channel_code;
        
END IF;		

SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getOrderMasterDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getOrderMasterDetails`(
IN `in_fby_user_id` VARCHAR(128) 
)
BEGIN
SET SQL_SAFE_UPDATES = 0;
SELECT 
    om.*,
    (
        SELECT SUM(od.quantity_purchased) 
        FROM order_details od
        WHERE od.order_no = om.order_no
    ) AS quantity,
    (
        SELECT COUNT(*) 
        FROM order_details od
        WHERE od.order_no = om.order_no
    ) AS orderCount
FROM 
    order_masters om
WHERE 
    om.fby_user_id = in_fby_user_id;

SET SQL_SAFE_UPDATES = 1;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getPaymentMethod` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getPaymentMethod`(
	IN `in_payment_method` VARCHAR(100), 
    IN `in_chanel` VARCHAR(50)
)
BEGIN
/*
	CALL getPaymentMethod('NaN','Shopify IT');
	CALL getPaymentMethod('credit_card','Shopify IT');

*/
IF EXISTS (
			SELECT * FROM map_channel_payment_method AS mcp 
            WHERE mcp.ch_payment_method = in_payment_method
				and mcp.channel = in_chanel
) 
THEN 
		SELECT 
			mcp.id,
			mcp.channel,
			mcp.ch_payment_method,
			mcp.fby_payment_method
		FROM
			map_channel_payment_method AS mcp
		WHERE
			mcp.ch_payment_method = in_payment_method
				AND mcp.channel = in_chanel
		ORDER BY id DESC
		LIMIT 1;
 ELSE
	SELECT 
		0 as id,
		in_chanel as channel,
		in_payment_method as ch_payment_method,
		in_payment_method as fby_payment_method;
  END IF;          
  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getPriceDetails` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getPriceDetails`(
`in_fby_id` INT)
BEGIN
	SELECT * FROM
		channelconnector.products AS P
			INNER JOIN
	channelconnector.temp_master_price AS TI 
		ON P.sku = TI.skucode
		AND P.fby_user_id = TI.fby_user_id
	WHERE
		P.fby_user_id = in_fby_id
		AND P.sku IS NOT NULL
		AND sku <> '' ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductByDomain` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductByDomain`(
`in_fby_id` INT, 
`in_dom` VARCHAR(64)
)
BEGIN
	/*-- GET PRODUCTS FOR STOCK UPDATE 
    call channelconnector.getProductByDomain (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductByDomain (39,'');
    */
    DECLARE lastNdays DATETIME;
    DECLARE in_channel_name varchar(256);
	DECLARE in_domain varchar(256);
    DECLARE in_owner_code varchar(256);
        
    SET lastNdays = DATE_ADD(NOW(), INTERVAL -2 DAY);
    
   
	SELECT lower(channelName) 	, domain	, ownerCode
		into in_channel_name	, in_domain	, in_owner_code
	FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
	limit 1;
	
    DROP TABLE IF EXISTS temp_getProductByDomain;
    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    CREATE TEMPORARY TABLE temp_getProductByDomain as 
	SELECT DISTINCTROW
		P.`id`,
		P.`fby_user_id`,
		P.`channel`,
		P.`domain`,
		P.`owner_code`,
		P.`sku`,
		P.`barcode`,
        P.`previous_inventory_quantity`,
		TI.quantity AS `inventory_quantity`,
        P.inventory_quantity as 'P_inventory_quantity',
        TI.quantity as `TI_quantity_StockToUpdate`,
		P.`item_id`,
		P.`title`,
		P.`item_product_id`,
		P.`inventory_item_id`,
		P.`location_id`,
		
		P.`image`,
		IF(TP.specialPrice > 0 and TP.specialPrice is not null and TP.specialPrice <> '', TP.specialPrice, P.`price`) as `price`,
        IF(TP.fullPrice is null or TP.fullPrice = '' , 0, TP.fullPrice) as fullPrice,
		P.`count`,
		P.`fby_error_flag`,
		P.`status`,
		P.`cron_name`,
		P.`cron_id`,
		P.`created_at`,
		P.`updated_at`,
        TI.`created_at` as tim_created_at,
		TI.`updated_at` as tim_updated_at
        
	FROM
		channelconnector.products AS P
			INNER JOIN
	channelconnector.temp_master_inventory AS TI 
		ON P.sku = TI.skucode
		AND P.fby_user_id = TI.fby_user_id
	LEFT JOIN
		channelconnector.temp_master_price AS TP
        on P.sku = TP.skucode
        AND P.fby_user_id = TP.fby_user_id
	WHERE
		P.fby_user_id = in_fby_id
        AND P.sku IS NOT NULL
		AND sku <> ''
		-- /*
        AND (P.inventory_quantity <> P.previous_inventory_quantity
		OR P.inventory_quantity <> TI.quantity
        or TI.quantity = 0
		or P.inventory_quantity is null
        or P.previous_inventory_quantity is null
        )
        -- */
        AND (TI.created_at >= lastNdays  OR TI.updated_at >= lastNdays)
	ORDER BY P.fby_user_id, P.sku, P.barcode	
	;
    
    IF(in_channel_name like '%amazon%' or in_domain like '%amazon%')
    THEN
		
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_getProductByDomain1 as 
        SELECT DISTINCTROW
		IF (P.`id` IS NULL,TI.id,P.`id`) as id,
		IF (P.`fby_user_id` IS NULL,TI.fby_user_id,P.`fby_user_id`) as fby_user_id,
        IF (P.`channel` IS NULL,in_channel_name,P.`channel`) as `channel`,
        IF (P.`domain` IS NULL,in_domain,P.`domain`) as `domain`,
        IF (P.`owner_code` IS NULL,in_owner_code,P.`owner_code`) as owner_code,
		IF (P.`sku` IS NULL,TI.skucode,P.`sku`) as sku,
        IF (P.`barcode` IS NULL,'',P.`barcode`) as barcode,
		IF (P.`previous_inventory_quantity` IS NULL,0,P.`previous_inventory_quantity`) as previous_inventory_quantity,
        TI.quantity AS `inventory_quantity`,
        P.inventory_quantity as 'P_inventory_quantity',
        TI.quantity as `TI_quantity_StockToUpdate`,
		IF (P.`item_id` IS NULL,0,P.`item_id`) as item_id,
		P.`title`,
        IF (P.`item_product_id` IS NULL,0,P.`item_product_id`) as item_product_id,
		IF (P.`inventory_item_id` IS NULL,0,P.`inventory_item_id`) as inventory_item_id,
		
		IF (P.`location_id` IS NULL,0,P.`location_id`) as location_id,
		P.`image`,
		IF(TP.specialPrice > 0 and TP.specialPrice is not null and TP.specialPrice <> '', TP.specialPrice, P.`price`) as `price`,
        IF(TP.fullPrice is null or TP.fullPrice = '' , 0, TP.fullPrice) as fullPrice,
		0 `count`,
		0 `fby_error_flag`,
		0 as `status`,
		P.`cron_name`,
		P.`cron_id`,
		
        IF (P.`created_at` IS NULL, TI.`created_at`, P.`created_at`) as created_at,
		P.`updated_at`,
        TI.`created_at` as tim_created_at,
		TI.updated_at as tim_updated_at
        
	FROM channelconnector.temp_master_inventory AS TI 
	LEFT JOIN
		channelconnector.products AS P
		ON P.sku = TI.skucode
		AND P.fby_user_id = TI.fby_user_id
	LEFT JOIN
		temp_getProductByDomain AS TI1 
		ON TI1.sku = TI.skucode
		AND TI1.fby_user_id = TI.fby_user_id
	LEFT JOIN
		channelconnector.temp_master_price AS TP
        on P.sku = TP.skucode
        AND P.fby_user_id = TP.fby_user_id      
	WHERE
		TI.fby_user_id = in_fby_id
        AND TI.skucode IS NOT NULL
		AND TI.skucode <> ''
		AND (TI.created_at >= lastNdays  OR TI.updated_at >= lastNdays)
        AND TI1.sku is null
	-- ORDER BY TI.fby_user_id, TI.skucode, P.barcode	
	;
    
    INSERT INTO temp_getProductByDomain
        (
        id,
		fby_user_id,
		channel,
		domain,
		owner_code,
		sku,
		barcode,
		previous_inventory_quantity,
		inventory_quantity,
		P_inventory_quantity,
		TI_quantity_StockToUpdate,
		item_id,
		title,
		item_product_id,
		inventory_item_id,
		location_id,
		image,
		price, fullPrice,
		count,
		fby_error_flag,
		status,
		cron_name,
		cron_id,
		created_at,
		updated_at,
		tim_created_at,
		tim_updated_at
        )
        Select * from temp_getProductByDomain1;
        
    END IF;
	
    SELECT * FROM temp_getProductByDomain;
    
    DROP TABLE IF EXISTS temp_getProductByDomain;
    DROP TABLE IF EXISTS temp_getProductByDomain1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductByDomainForLocationSync` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductByDomainForLocationSync`(IN `fby_id` INT, IN `dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductByDomainForLocationSync (8,'shopping170.myshopify.com');
    */
	SELECT 
		* 
	FROM products 
    WHERE 
		fby_user_id = fby_id 
		AND domain = dom 
        AND location_id = 0;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductByFlag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductByFlag`(IN `casename` VARCHAR(60))
BEGIN
	SELECT * FROM products WHERE fby_error_flag=1 AND count=1 AND cron_name=casename;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductBySku` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductBySku`(IN `fby_user_id` VARCHAR(128), IN `sku` VARCHAR(128))
BEGIN
	SELECT * FROM products where fby_user_id=fby_user_id AND products.sku=sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductByStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductByStatus`(`in_fby_user_id` VARCHAR(128))
BEGIN
	/*
    call channelconnector.`getProductByStatus`(1000);
    */
	SELECT * FROM products 
    WHERE 
		lower(fby_user_id) = lower(in_fby_user_id) 
        AND products.`status` = 0;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductForLocation` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductForLocation`(
	IN `fby_id` INT, 
	IN `dom` VARCHAR(64)
)
BEGIN
-- call channelconnector.getProductForLocation(39,'');
	SELECT 
		*
	FROM
		products
	WHERE
		fby_user_id = fby_id 
        -- AND domain = dom
        AND (location_id = 0 OR location_id IS NULL)
        order by sku
        limit 50
        ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductForUpdate` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductForUpdate`(`in_fby_id` INT, `in_dom` VARCHAR(64))
BEGIN
	/*
    call channelconnector.getProductForUpdate (8,'shopping170.myshopify.com');
    
    call channelconnector.getProductForUpdate (1002,'');
    
    SELECT 'Req',
		cp.* 
	FROM createproducts  as cp
    UNION
    SELECT 'Res',
		cp1.* 
	FROM createdproducts  as cp1;
    
    */
	SELECT 
			cp.`id`,
			cp.`fby_user_id`,
			cp.`channel`,
			cp.`domain`,
			cp.`owner_code`,
			cp.`sku`,
			cp.`barcode`,
			ccp.`item_id`,
			cp.`title`,
			ccp.`item_product_id`,
			ccp.`inventory_item_id`,
			cp.`location_id`,
			cp.`previous_inventory_quantity`,
			cp.`inventory_quantity`,
			cp.`image`,
			cp.`price`,
			cp.`count`,
			cp.`fby_error_flag`,
			cp.`status`,
			cp.`cron_name`,
			cp.`cron_id`,
			cp.`created_at`,
			cp.`updated_at`,
			cp.`isChanged`
	FROM
		createproducts AS cp
	INNER JOIN
		createdproducts ccp ON ccp.fby_user_id = cp.fby_user_id
	AND ccp.sku = cp.sku
	AND ccp.fby_user_id = cp.fby_user_id
	AND cp.fby_user_id = in_fby_id
	WHERE
		cp.isChanged = 1
        and cp.fby_user_id = in_fby_id
        
    UNION
    SELECT 
			cp.`id`,
			cp.`fby_user_id`,
			cp.`channel`,
			cp.`domain`,
			cp.`owner_code`,
			cp.`sku`,
			cp.`barcode`,
			ccp.`item_id`,
			cp.`title`,
			ccp.`item_product_id`,
			ccp.`inventory_item_id`,
			cp.`location_id`,
			cp.`previous_inventory_quantity`,
			cp.`inventory_quantity`,
			cp.`image`,
			cp.`price`,
			cp.`count`,
			cp.`fby_error_flag`,
			cp.`status`,
			cp.`cron_name`,
			cp.`cron_id`,
			cp.`created_at`,
			cp.`updated_at`,
			cp.`isChanged`
	FROM
		createproductvariants AS cp
	INNER JOIN
		createdproductvariants ccp 
	ON ccp.fby_user_id = cp.fby_user_id
		AND ccp.sku = cp.sku
		AND ccp.fby_user_id = cp.fby_user_id
		AND cp.fby_user_id = in_fby_id
	WHERE
		cp.isChanged = 1
        and cp.fby_user_id = in_fby_id        ; 
		
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductType` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductType`(
IN `in_fby_user_id` VARCHAR(128), 
IN `in_sku` VARCHAR(128)
)
BEGIN
	SELECT product_type FROM productType 
    where 
    fby_user_id = in_fby_user_id AND sku=in_sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getProductUnits` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getProductUnits`(
IN `in_fby_user_id` VARCHAR(128), 
IN `in_sku` VARCHAR(128)
)
BEGIN
	SELECT * FROM product_units 
    where 
    fby_user_id = in_fby_user_id AND sku=in_sku;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getReports` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getReports`(`in_fby_user_id` INT)
BEGIN

SELECT 
    *
FROM
    Amazon_Reports amr
WHERE
		amr.fby_user_id = in_fby_user_id;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getShopifyUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getShopifyUser`(
	IN `in_channelId` VARCHAR(1024)
)
BEGIN
	/*
    
		call channelconnector.`getShopifyUser`(1111);
        
		call channelconnector.`getShopifyUser`(25);	
        
        call channelconnector.`getShopifyUser`(1010);
    */
    DECLARE var_orderSyncStartDate datetime;
    DECLARE var_last_orderSyncDateTime datetime;
    DECLARE var_new_orderSyncDateTime datetime;
    DECLARE var_last_ProductSyncDateTime datetime;
    DECLARE var_channel_name varchar(200);

	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_channelId
		AND ch.isActive = 1 
		limit 1
	);

    
    SET var_orderSyncStartDate = (
			SELECT 
				T1.`orderSyncStartDate`
			FROM
				`channelconnector`.`_2_channel` AS T1
			WHERE
				T1.`channelId` = in_channelId
					AND T1.`isActive` = 1
					AND T1.`isEnabled` = 1
			LIMIT 1
	);
    
    SET var_last_orderSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                MAX(T1.`created_at`) 
                #END 
			FROM
				`channelconnector`.`order_details` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    set var_last_ProductSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                date_add(MAX(T1.`created_at`) , INTERVAL -2 day) 
                #END 
			FROM
				`channelconnector`.`products` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    IF(var_last_orderSyncDateTime IS NOT NULL)
    THEN
		/*
		IF(var_channel_name like '%woocom%') THEN
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        ELSE
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -1 day) ;
        END IF;
        */
        IF(date_add(var_last_orderSyncDateTime, INTERVAL -2 day) > var_orderSyncStartDate)
        THEN
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -2 day) ;
        ELSE
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        END IF;
        
    ELSE
		SET var_new_orderSyncDateTime = var_orderSyncStartDate;
    END IF;
        
	SELECT 
			T1.`id` ,
			T1.`channelId` as `fby_user_id`,
			T1.`groupCode` as group_code,
			T1.`currencyCode` as currency_code,
			T1.`ownerCode` as owner_code,
			T1.`channelCode`  as channel_code,
			T1.`channelName` ,
			T1.`domain`  ,
			T1.`username` ,
			T1.`password`  as `api_password`,
            T1.`siteId`  ,
			T1.`compatibilityLevel`  , 
			T1.`ebay_devid`  , 
			T1.`ebay_appid`  , 
			T1.`ebay_certid`  , 
			T1.`ebay_quantity_update_by`  , 
			T1.`apiKey`  as `api_key`,
			T1.`secret` ,
			T1.`token`  ,
			T1.`isActive`  ,
			T1.`isEnabled` ,
            T1.stockUpdate,
			T1.priceUpdate,
			T1.orderSync,
			T1.productPublish,
            T1.`amazon_Role`,
            T1.`amazon_MarketPlaceID`,
            T1.`amazon_SellerID`,
            T1.amazon_region,
			CAST(T1.`createdOn` as CHAR) as created_at,
			CAST(T1.`modifiedOn` as CHAR) as updated_at,
            CAST(var_new_orderSyncDateTime as CHAR) as orderSyncStartDate,
            CAST(var_last_ProductSyncDateTime as CHAR) as productSyncDateTime,
            T1.platformName
    	
	FROM `channelconnector`.`_2_channel` as T1
	WHERE 
		T1.`channelId` = in_channelId
        AND T1.`isActive` = 1
        AND T1.`isEnabled` = 1
        #AND 1 = case when T1.`orderSyncStartDate` is null || (T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()) then 1 else 0 end
        # AND T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()
        LIMIT 1 ;
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `getUntrackOrders` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `getUntrackOrders`(
	`in_fby_id` VARCHAR(128),
    `in_channel` VARCHAR(128)
)
BEGIN
	/*
    
	call channelconnector.getUntrackOrders (23,'shopify');
    
    */
    DECLARE in_channel_name varchar(256);
    SET in_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	SELECT DISTINCTROW
		CASE
			WHEN in_channel_name LIKE '%presta%'
					
			THEN
				TRIM(seller_order_id)
			ELSE TRIM(order_no) END AS 
		order_no,
        owner_code,
        fby_user_id
    FROM order_details 
     
	WHERE 
		fby_user_id = in_fby_id 
		#AND channel = in_channel 
		AND is_trackable = 0  
   ORDER BY     
        fby_user_id,order_no;
    -- GROUP BY 
		-- order_no,
        -- owner_code;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `get_user` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `get_user`(
	IN `in_user_id` INT
)
BEGIN
	/*
		#Retun fby credentials for given user_id by using default credentials 
        call channelconnector.get_user(39);
        
        SELECT * FROM channelconnector._2_channel;
    */
    DECLARE in_channelName VARCHAR(128);
    
    SET in_channelName = (
		SELECT channelName from channelconnector._2_channel
		Where channelId = in_user_id
		order by id desc
		limit 1
    );
    
 	SELECT DISTINCT
		t1.`id`,
		t1.`name`,
		t1.`email`,
		in_user_id as `fby_user_id`,
		t1.`auth_username`,
		t1.`auth_password`,
		t1.`created_at`,
		t1.`updated_at`,
        in_channelName as channelName,
        t2.domain,
        t2.stockUpdate,
		t2.priceUpdate,
		t2.orderSync,
		t2.productPublish,
        t2.ownerCode as owner_code
        
	FROM 
		`channelconnector`.`users` AS t1
        INNER JOIN `channelconnector`.`_2_channel` AS t2
        ON t2.channelId = in_user_id
	ORDER BY t1.id
	LIMIT 1;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `insertCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `insertCron`(IN `fby_id` VARCHAR(128), IN `crn_name` VARCHAR(60), IN `crnid` VARCHAR(100), IN `stats` TINYINT(4))
BEGIN
	INSERT INTO cron_process_table(fby_user_id,cron_name,cron_id,status) VALUES(fby_id,crn_name,crnid,stats);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `isOrderNotified` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `isOrderNotified`(
	IN `in_fby_user_id` INT,
    IN `in_order_no` VARCHAR(256),
    IN `in_sku` VARCHAR(256),
    IN `in_original_Channel_OrderId` VARCHAR(256)
   
)
BEGIN
	
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	/*
    call channelconnector.isOrderNotified
    (
    39, '4997659885797', 'JM412-L', 'R-4997659885797'
    )
    */
	IF(in_original_Channel_OrderId is not null and in_original_Channel_OrderId <> '')
    THEN
		IF EXISTS (
			Select 1
			from channelconnector.order_details as od
			where od.fby_user_id = in_fby_user_id and od.order_no = in_order_no and od.sku = in_sku 
            and (original_Channel_OrderId IS NULL OR original_Channel_OrderId = '')
        )
        THEN
			
            SET SQL_SAFE_UPDATES = 0;
			UPDATE channelconnector.order_details
			SET original_Channel_OrderId = in_original_Channel_OrderId
			WHERE fby_user_id = in_fby_user_id and order_no = in_order_no and sku = in_sku;
            SET SQL_SAFE_UPDATES = 1;
        END IF;
    END IF;
    
	Select od.*
	from channelconnector.order_details as od
	where od.fby_user_id = in_fby_user_id and od.order_no = in_order_no and od.sku = in_sku 
    -- and  od.order_line_item_id = in_order_line_item_id
    -- and od.IsNotifiedFBY = 0
    and od.status = 1
    order by od.fby_user_id,od.order_no ,od.sku, id
	limit 1;
    
    
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `mark_order_complete` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `mark_order_complete`(
	IN p_fby_user_id VARCHAR(128), 
    IN p_order_no VARCHAR(128),
    IN p_status VARCHAR(128)
)
BEGIN
    INSERT INTO binStatus (fby_user_id, order_no, status)
    VALUES (p_fby_user_id, p_order_no, p_status);
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateCancelOrderCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateCancelOrderCron`(`in_ordr_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)
BEGIN
	 SET SQL_SAFE_UPDATES = 0;
     
		UPDATE order_details AS od 
		SET 
			od.cron_name = in_crn_name,
			od.cron_id = in_crnid,
			od.updated_at = NOW()
		WHERE
			od.order_no = in_ordr_no;


	 SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateChannelStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateChannelStatus`(
    `in_channelId` INT, 
    `in_groupCode` VARCHAR(256), 
    `in_Active` INT
)
BEGIN
    DECLARE isChannelExists INT;

    SELECT COUNT(*) INTO isChannelExists
    FROM channelconnector._2_channel
    WHERE channelId = in_channelId
    LIMIT 1;

    IF isChannelExists THEN
        SET SQL_SAFE_UPDATES = 0;

        UPDATE `channelconnector`.`_2_channel`
        SET isActive = in_Active
        WHERE `channelId` = in_channelId AND groupCode = in_groupCode;
        
        SET SQL_SAFE_UPDATES = 1;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateCron`(`in_time` DATETIME, `in_crnid` VARCHAR(100))
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE cron_process_table 
	SET 
		updated_at = NOW(),
		status = 0
	WHERE
		cron_id = in_crnid 
        AND status = 1;
        
    SET SQL_SAFE_UPDATES = 1;    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateCronLogs` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateCronLogs`(
   IN in_fby_user_id INT,
   IN in_cc_operation VARCHAR(256),
   IN in_cron_schedule VARCHAR(256),
   In in_url VARCHAR(256)
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE _cron 
	SET 
        cc_operation = in_cc_operation,
        cron_schedule = in_cron_schedule,
        url = in_url,
		updatedOn = NOW()
	WHERE
		fby_user_id = in_fby_user_id; 
        
    SET SQL_SAFE_UPDATES = 1;  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateEbayTrackSent` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateEbayTrackSent`(
	`in_fby_id` VARCHAR(128), 
	`in_order_no` VARCHAR(256), 
	`in_crn_name` VARCHAR(60), 
	`in_crnid` VARCHAR(100), 
	`in_time` DATETIME
)
BEGIN
	   SET SQL_SAFE_UPDATES = 0;
       
		UPDATE order_details 
		SET 
			`status` = 1,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = NOW(),
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no;
    
		SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateImages` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateImages`(`in_image_id` VARCHAR(200), `in_sku` VARCHAR(100), `in_fby_user_id` INT)
BEGIN
/*

	
CALL updateImages('37688739692787','BN_BRL_WFS14421_S398_F9','1002');
select * from Images;

*/

DECLARE  var_id INT;

SET var_id = (

	SELECT 
		-- i.sku, i.skuFamily, i.fby_user_id, 
        MIN(i.id) AS id
	FROM
		Images AS i
	WHERE
		channel_image_id IS NULL
			AND sku = in_sku
			AND fby_user_id = in_fby_user_id
			GROUP BY i.sku , i.skuFamily , i.fby_user_id
			ORDER BY i.sku , i.skuFamily , i.fby_user_id
			LIMIT 1
);

SET SQL_SAFE_UPDATES = 0;

UPDATE createproductvariants 
SET 
    image = in_image_id
WHERE
    sku = in_sku
        AND fby_user_id = in_fby_user_id
;

UPDATE Images 
SET 
    channel_image_id = in_image_id
WHERE
    channel_image_id IS NULL
        AND sku = in_sku
        AND fby_user_id = in_fby_user_id
        AND id = var_id;

UPDATE createdproducts 
SET 
    image = in_image_id
WHERE
    (image IS NULL || image = "")
        AND sku = in_sku
        AND fby_user_id = in_fby_user_id;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateLastSyncOperationTime` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateLastSyncOperationTime`(
    IN `in_fby_user_id` VARCHAR(256),
    IN `in_page_count` INT,
    IN `in_cc_operation` VARCHAR(256)
)
BEGIN
DECLARE nowTime datetime;  
set nowTime = CAST(DATE_ADD(NOW(), INTERVAL - 1 HOUR) as char(16));
IF(in_fby_user_id = 39 or in_fby_user_id = 40)
THEN
	set nowTime = CAST(DATE_ADD(NOW(), INTERVAL - 1 DAY) as char(10));
ELSE
	set nowTime = DATE_ADD(NOW(), INTERVAL - 1 HOUR);
END IF;

IF(in_fby_user_id = 37 or in_fby_user_id = 50)
THEN
	set nowTime = CAST('2023-06-01' as DATETIME);
END IF;

/*
IF(in_fby_user_id = 50 || in_fby_user_id = 37)
THEN
	set nowTime = CAST(DATE_ADD('2023-06-21 00:00:00', INTERVAL - 0 DAY) as char(10));
END IF;
*/
SET in_cc_operation = UPPER(in_cc_operation);
SET transaction isolation level READ uncommitted;

SET SQL_SAFE_UPDATES = 0;

		IF EXISTS (
        SELECT 1 FROM _3_last_operation_sync_time
		WHERE 
			fby_user_id = in_fby_user_id  
            AND UPPER(cc_operation) = in_CC_OPERATION
            AND in_page_count >1
		LIMIT 1 
        FOR UPDATE
    ) = 1 
    THEN
		Update _3_last_operation_sync_time AS C 
        
		set page_count =  in_page_count
		where fby_user_id = in_fby_user_id 
        AND UPPER(cc_operation) = in_CC_OPERATION;

	ELSEIF EXISTS (
        SELECT 1 FROM _3_last_operation_sync_time
		WHERE 
			fby_user_id = in_fby_user_id  
            AND UPPER(cc_operation) = in_CC_OPERATION
            AND in_page_count =1
		LIMIT 1 
        FOR UPDATE
    ) = 1 
    THEN
		Update _3_last_operation_sync_time AS C 
        
		set last_opearion_sync_time =  nowTime
        , updated_at = NOW()
		where fby_user_id = in_fby_user_id 
        AND UPPER(cc_operation) = in_CC_OPERATION;
          
        
    ELSE
		-- Select 'Inserted' as isExists;
    
		INSERT INTO _3_last_operation_sync_time(
			fby_user_id,last_opearion_sync_time,cc_operation,created_at,updated_at,page_count
		) 
		VALUES(
			in_fby_user_id,nowTime,in_cc_operation,now(),now(),1
			);
		
	END IF;
    
      SET SQL_SAFE_UPDATES = 1;
      Call channelconnector.getLastSyncOperationTime(in_fby_user_id,in_cc_operation);  
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrder`(
	IN `in_crn_name` VARCHAR(60),
	IN `in_crnid` VARCHAR(100),
	IN `in_time` DATETIME,
	IN `in_tracking_id` VARCHAR(256),
	IN `in_carrier` VARCHAR(256),
	IN `in_url` TEXT,
	IN `in_channel_order_id` VARCHAR(256),
	IN `in_channel_code` VARCHAR(20),
	IN `in_barcode` VARCHAR(256),
    IN `in_fby_user_id` VARCHAR(256)
)
BEGIN
	DECLARE var_channel_name varchar(256);
    SET var_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	/*
    
		call channelconnector.`updateOrder`(
		'get_track_number',
	  '9662f8d6-c031-4f15-bb6e-d0e00c74feab',
	  '2022-02-09 21:22:10',
	  'B12345678904231',
	  'SDA',
	  'https://www.swiship.it/t/D6zCc37CL',
	  'FSHREXMYR',
	  'SHIT',
	  '',
	  23
		)
    
    
    Select * from order_details od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
	 
        
   Select * from order_masters om
    WHERE
		om.order_no = in_channel_order_id;
      #*/ 
    IF(in_barcode = '' OR in_barcode IS NULL)    
    THEN
		SET in_barcode = null;
	ELSE
		SET in_barcode = trim(in_barcode);
    END IF;
    
    SET SQL_SAFE_UPDATES = 0;    
    /*
    SELECT in_channel_code,in_channel_order_id,in_barcode;
    
    SELECT * FROM order_details AS od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
    #*/    
    #SELECT in_tracking_id,in_carrier,in_url,in_barcode,in_channel_order_id as order_no,in_channel_code;
    
	UPDATE order_details AS od 
	SET 
		od.tracking_id = in_tracking_id,
		od.tracking_courier = in_carrier,
		od.tracking_url = in_url,
		od.is_trackable = 1,
		od.cron_name = in_crn_name,
		od.cron_id = in_crnid,
		od.updated_at = NOW(),
		od.barcode = CASE
        WHEN
            in_barcode IS NOT NULL
                AND in_barcode <> ''
        THEN
            TRIM(in_barcode)
        ELSE TRIM(od.barcode)
    END
	WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (
							TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
							OR (TRIM(od.order_no) = TRIM(in_channel_order_id))
						)
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL AND in_barcode<>'' THEN trim(in_barcode) ELSE trim(od.barcode) end;
		
    update order_masters om
    SET om.order_status = "fulfiled",
		om.cron_name = in_crn_name,
		om.cron_id = in_crnid,
		om.updated_at = NOW()
	WHERE
		om.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     
	SELECT * FROM order_details AS od
    WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
					OR (TRIM(od.order_no) = TRIM(in_channel_order_id)))
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		-- AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
   /*     
	SELECT * FROM order_masters om
    WHERE
		od.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
     */
	SET SQL_SAFE_UPDATES = 1;	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderCancel` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderCancel`(`in_fby_user_id` VARCHAR(128), `in_ordr_number` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE order_details AS od 
	SET 
		od.is_canceled_fby = 1,
		od.count = 0,
		od.fby_error_flag = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_user_id
		AND od.order_no = in_ordr_number;
    
	UPDATE order_masters AS om 
	SET 
		om.is_canceled = 1,
		om.fby_send_status = 1,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_user_id
		AND om.order_no = in_ordr_number;
    
    SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderCancelStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderCancelStatus`(
	`in_fby_id` VARCHAR(128),
	`in_ordr_number` VARCHAR(256),
	`in_financial_status` VARCHAR(20),
	`in_cancl_reson` VARCHAR(128),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	/*
	CALL updateOrderCancelStatus(8,'4666394706178',"refunded","customer","get_Shopify_Orders","2d404606-c467-4325-98e2-748eca2761f5","2022-03-03 05:12:00");
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details AS od 
	SET 
		od.payment_status = in_financial_status,
		od.order_status = 'canceled',
		od.cancel_reason = in_cancl_reson,
		od.is_canceled_fby = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_canceled_fby IS NULL;
    
	UPDATE order_masters AS om 
	SET 
		om.payment_status = in_financial_status,
		om.order_status = 'canceled',
		om.is_canceled = 0,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_id
			AND om.order_no = in_ordr_number
			AND om.is_canceled <> 1;
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderCron`(`in_ordr_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_masters 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		order_no = in_ordr_no;
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderDetailStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderDetailStatus`(`in_fby_id` VARCHAR(128), `in_order_no` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)
BEGIN
	   SET SQL_SAFE_UPDATES = 0;
       
		UPDATE order_details 
		SET 
			`status` = 1,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = NOW(),
			cron_id = in_crnid,
            IsNotifiedFBY = 1
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND is_trackable = 1;
    
		SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderShippedStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderShippedStatus`(`in_fby_id` VARCHAR(128), `in_order_no` VARCHAR(256), `in_time` DATETIME)
BEGIN
	   SET SQL_SAFE_UPDATES = 0;
       
		UPDATE order_details 
		SET 
			`IsShipped` = 1,
			updated_at = NOW()
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND is_trackable = 1
            AND `status` = 1;
    
		SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderStatus`(
	`in_fby_id` VARCHAR(128),
	`in_order_no` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME,
	`in_checktime` DATETIME,
	`in_isCheckafter48Hr` tinyint
)
BEGIN
	/*
    SELECT * FROM 		order_details as o;
    call channelconnector.updateOrderStatus(8,'4667064942850','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW(),NOW(),0);
    
    call channelconnector.updateOrderStatus(8,'4666393428226','send_orders_fby','b4600751-6a4a-4bd4-9cbe-f351cffa2c65',NOW());
    
    */
	DECLARE is_updated TINYINT;
	DECLARE order_item_count_total INT;
	DECLARE order_item_count_sucess INT;
	DECLARE order_item_count_pending INT;
	
    SET is_updated = 0;
    SET order_item_count_total = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		
	) ;
     SET order_item_count_sucess = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 1
	) ;
     SET order_item_count_pending = ( 

	SELECT count(DISTINCT id)
	FROM 
		order_details as o
	WHERE 
		o.fby_user_id = in_fby_id
		AND o.order_no = in_order_no
		AND o.`status` = 0
	) ;
    
     
   #IF (order_item_count_pending IS NULL  OR order_item_count_pending = 0 )     
   #THEN  
		SET SQL_SAFE_UPDATES = 0;
		UPDATE order_masters 
		SET 
			fby_send_status = CASE WHEN in_isCheckafter48Hr = 1 THEN 0 ELSE 1 END,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = NOW(),
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND fby_send_status = 0;
		 SET is_updated = 1;
		SET SQL_SAFE_UPDATES = 1;
 #END IF;
 
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		checked_at = in_checktime,
		IsCheckAfter48Hours = in_isCheckafter48Hr,
        updated_at = NOW()
	WHERE
		fby_user_id = in_fby_id
		AND order_no = in_order_no;
 
 SELECT 
    is_updated,
    order_item_count_total as order_items_total,
    order_item_count_sucess AS synced,
    order_item_count_pending AS pending;

 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderTrackingCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderTrackingCron`(IN `in_ordr_no` VARCHAR(256), IN `in_crn_name` VARCHAR(60), IN `in_crnid` VARCHAR(100), IN `time` DATETIME)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_details 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = now()
	WHERE
		order_no = in_ordr_no AND is_trackable = 1;
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateOrderV1` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateOrderV1`(
	IN `in_crn_name` VARCHAR(60), 
    IN `in_crnid` VARCHAR(100), 
    IN `in_time` DATETIME, 
    IN `in_tracking_id` VARCHAR(256), 
    IN `in_carrier` VARCHAR(256), 
    IN `in_url` TEXT, 
    IN `in_channel_order_id` VARCHAR(256), 
    IN `in_channel_code` VARCHAR(20), 
    IN `in_barcode` VARCHAR(256), 
    IN `in_sku` VARCHAR(256), 
    IN `in_fby_user_id` VARCHAR(256)
)
BEGIN
	DECLARE var_channel_name varchar(256);
    
    DECLARE errno INT;
    /*
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
		GET CURRENT DIAGNOSTICS CONDITION 1 errno = MYSQL_ERRNO;
		SELECT errno AS MYSQL_ERROR;
		ROLLBACK;
    END;
    
	START TRANSACTION;*/
    SET var_channel_name = 
						(
							SELECT lower(channelName) FROM _2_channel WHERE channelId = in_fby_user_id and isActive = 1 and isEnabled = 1
                            limit 1
						);
                        
	/*
    
		CALL updateOrderV1(
			"get_track_number",    					-- 1)	in_crn_name
			"f8b3d22d-680a-45c9-a3e1-780fa1c7c355", -- 2)	in_crnid
			"2022-07-14 19:50:13",                  -- 3)	in_time
			"1234567890",                           -- 4)	in_tracking_id
			"SDA",                                  -- 5)	in_carrier
			"",                                     -- 6)	in_url
			"order_jcs7oE0FsFpwr",                  -- 7)	in_channel_order_id
			"WCIT",                                 -- 8)	in_channel_code
			"1478523695493",                        -- 9)	in_barcode
			"BALINIMO_JUOSTELES_JUODOS",            -- 10) 	in_sku
			28										-- 11) 	in_fby_user_id
        );
    
    
    Select * from order_details od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
	 
        
   Select * from order_masters om
    WHERE
		om.order_no = in_channel_order_id;
      #*/ 
    IF(in_barcode = '' OR in_barcode IS NULL)    
    THEN
		SET in_barcode = null;
	ELSE
		SET in_barcode = trim(in_barcode);
    END IF;
    
    IF(in_sku = '' OR in_sku IS NULL)    
    THEN
		SET in_sku = null;
	ELSE
		SET in_sku = trim(in_sku);
    END IF;
    
    SET SQL_SAFE_UPDATES = 0;    
    /*
    SELECT in_channel_code,in_channel_order_id,in_barcode;
    
    SELECT * FROM order_details AS od
    WHERE
		trim(od.order_no) = trim(in_channel_order_id)
		AND trim(od.channel_code) = trim(in_channel_code)
		AND trim(od.barcode)= case when in_barcode IS NOT NULL THEN trim(in_barcode) ELSE trim(od.barcode) END;
    #*/    
    #SELECT in_tracking_id,in_carrier,in_url,in_barcode,in_channel_order_id as order_no,in_channel_code;
    IF EXISTS (
		Select * from order_details od
		WHERE
				od.fby_user_id = in_fby_user_id AND
				1 = CASE
					WHEN
						var_channel_name LIKE '%presta%'
							AND (
									TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
									OR (TRIM(od.order_no) = TRIM(in_channel_order_id))
								)
					THEN
						1
					ELSE CASE
						WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
						ELSE 0
					END
				END
				-- AND trim(od.channel_code) = trim(in_channel_code)
				AND TRIM(LOWER(od.sku)) = TRIM(LOWER(in_sku))
		FOR UPDATE
    )
    THEN
		UPDATE order_details AS od 
		SET 
			od.tracking_id = in_tracking_id,
			od.tracking_courier = in_carrier,
			od.tracking_url = in_url,
			od.is_trackable = 1,
			od.cron_name = in_crn_name,
			od.cron_id = in_crnid,
			od.updated_at = NOW(),
			od.barcode = CASE
			WHEN
				in_barcode IS NOT NULL AND in_barcode <> ''
				THEN
					TRIM(in_barcode)
				ELSE 
					TRIM(od.barcode)
			END
		WHERE
			od.fby_user_id = in_fby_user_id AND
			1 = CASE
				WHEN
					var_channel_name LIKE '%presta%'
						AND (
								TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
								OR (TRIM(od.order_no) = TRIM(in_channel_order_id))
							)
				THEN
					1
				ELSE CASE
					WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
					ELSE 0
				END
			END
			-- AND trim(od.channel_code) = trim(in_channel_code)
			AND TRIM(LOWER(od.sku)) = TRIM(LOWER(in_sku))
			/*and trim(od.barcode)= case when 
										in_barcode IS NOT NULL AND in_barcode<>'' 
										AND trim(od.barcode) IS NOT NULL AND trim(od.barcode)<>'' 
									THEN trim(in_barcode) ELSE trim(od.barcode) end*/
									;
			
		update order_masters om
		SET om.order_status = "fulfiled",
			om.cron_name = in_crn_name,
			om.cron_id = in_crnid,
			om.updated_at = NOW()
		WHERE
			om.fby_user_id = in_fby_user_id AND
			trim(om.order_no) = trim(in_channel_order_id);
            
    END IF; 
    
    SET transaction isolation level read uncommitted;
    
	SELECT od.* 
    FROM order_details AS od
    WHERE
		od.fby_user_id = in_fby_user_id AND
		1 = CASE
			WHEN
				var_channel_name LIKE '%presta%'
					AND (TRIM(od.seller_order_id) = TRIM(in_channel_order_id)
					OR (TRIM(od.order_no) = TRIM(in_channel_order_id)))
			THEN
				1
			ELSE CASE
				WHEN TRIM(od.order_no) = TRIM(in_channel_order_id) THEN 1
				ELSE 0
			END
		END
		AND TRIM(LOWER(od.sku)) = TRIM(LOWER(in_sku))
		/*and trim(od.barcode)= case when 
									in_barcode IS NOT NULL AND in_barcode<>'' 
                                    AND trim(od.barcode) IS NOT NULL AND trim(od.barcode)<>'' 
								THEN trim(in_barcode) ELSE trim(od.barcode) end*/
                                
                                ;
   /*     
	SELECT * FROM order_masters om
    WHERE
		om.fby_user_id = in_fby_user_id AND
		trim(om.order_no) = trim(in_channel_order_id);
   */
	SET SQL_SAFE_UPDATES = 1;	
    -- COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updatePrices` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updatePrices`(

	`in_fby_user_id` int,

	`in_price` VARCHAR(200), 
    `in_special_price` VARCHAR(200),

    `in_sku` VARCHAR(100)

)
BEGIN

		DECLARE isExistsVariant tinyint;
    DECLARE isExistsProduct tinyint;
    
	SET SQL_SAFE_UPDATES = 0;
    
    SET isExistsVariant = (
			SELECT 
				1 
			FROM
				channelconnector.createproductvariants as cp
			WHERE
				 sku = in_sku
                AND fby_user_id = in_fby_user_id
                AND (cp.price <> in_price
                OR cp.specialPrice <> in_special_price
                )
                
			LIMIT 1
    );
    
    SET isExistsProduct = (
			SELECT 
				1 
			FROM
				channelconnector.createproductvariants as cp
			WHERE
				 sku = in_sku
                AND fby_user_id = in_fby_user_id
                AND (cp.price <> in_price
                OR cp.specialPrice <> in_special_price
                )
                
			LIMIT 1
    );
    
    IF (in_price is not null and in_price > 0)
    THEN
		UPDATE createproductvariants
		SET price = in_price, specialPrice = in_special_price,
		isChanged = case when isExistsVariant = 1 then 1 else 0 end
		WHERE 
			sku = in_sku AND fby_user_id = in_fby_user_id;
        
		UPDATE createdproductvariants
		SET price = in_price, specialPrice = in_special_price
		WHERE 
			sku = in_sku AND fby_user_id = in_fby_user_id;    
        
		UPDATE createproducts
		SET price = in_price, specialPrice = in_special_price,
		isChanged = case when isExistsProduct = 1 then 1 else 0 end
		WHERE 
			sku = in_sku AND fby_user_id = in_fby_user_id;  
            
       UPDATE createdproducts
		SET price = in_price, specialPrice = in_special_price
		
		WHERE 
			sku = in_sku AND fby_user_id = in_fby_user_id;       
        
     END IF;   
        
    Select * from createproducts    
    WHERE  sku = in_sku AND fby_user_id = in_fby_user_id;
    
    Select * from createproductvariants    
    WHERE  sku = in_sku AND fby_user_id = in_fby_user_id;

    

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateProdLocation` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateProdLocation`(`in_fby_id` VARCHAR(128), `in_inventry_id` VARCHAR(127), `in_loc_id` VARCHAR(256), `in_crn_name` VARCHAR(60), `in_crn_id` VARCHAR(100), `in_time` DATETIME)
BEGIN
	/*
		CALL channelconnector.updateProdLocation(8,'44437908324610',66372763906,'Get_Shopify_Location','feceac14-421c-411c-9dac-188cb43fb4ba','2022-02-28 14:26:57');
    */
    SET SQL_SAFE_UPDATES = 0;
	UPDATE
		products AS p
	SET
		p.location_id = case when in_loc_id > 0 then in_loc_id else p.location_id  end,
		p.cron_name = in_crn_name,
		p.count = 0,
		p.fby_error_flag = 0,
		p.cron_id = in_crn_id,
		p.updated_at = in_time
	WHERE
		p.inventory_item_id = in_inventry_id
		AND p.fby_user_id = in_fby_id
		AND p.location_id = 0
        AND in_loc_id > 0;
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateProduct`(
	`in_crn_name` VARCHAR(60) ,
    `in_crnid` VARCHAR(100) ,
    `in_time` DATETIME,
	`in_fby_user_id` VARCHAR(60) 
)
BEGIN
	/*
		call channelconnector.`updateProduct`(
        'send_Products_Fby',
        '22dd4051-1ff6-44d5-b281-fe1ba55e10e7',
        now(),
        1002
        );
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE products AS P,temp_master_inventory AS TI 
	SET 
	
		P.previous_inventory_quantity = P.inventory_quantity,
		P.inventory_quantity = TI.quantity,
		#P.barcode = TI.barcode,
		P.cron_name = in_crn_name,
		P.cron_id = in_crnid,
		P.updated_at = in_time 
	WHERE 
		P.sku = TI.skucode 
        AND 1 = (
        case when (P.barcode <> '0' OR P.barcode is not null OR P.barcode <> '') AND (TI.barcode <>'' OR TI.barcode is not null OR TI.barcode <> '')
			then case when TRIM(P.barcode) = TRIM(TI.barcode) 
				then 1 
                else 0 
			end 
		else 1 end
        
        )
        AND P.fby_user_id = in_fby_user_id
        AND TI.fby_user_id = in_fby_user_id
        AND P.fby_user_id = TI.fby_user_id
		AND (CASE WHEN P.inventory_quantity IS  NULL THEN 0 ELSE P.inventory_quantity  END) <> TI.quantity; 
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateProductAftrSndChanl` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateProductAftrSndChanl`(
`in_fby_id` VARCHAR(128), 
`in_sku` VARCHAR(128), 
`in_crn_name` VARCHAR(60), 
`in_crnid` VARCHAR(100), 
`in_time` DATETIME
)
BEGIN
	DECLARE quantity INT;
	SET SQL_SAFE_UPDATES = 0;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    
    SET quantity = (
    Select p.quantity from temp_master_inventory as p
    WHERE
		fby_user_id = in_fby_id 
        AND skucode = in_sku
    );
    
    IF EXISTS( 
    Select p.* from products as p
    WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku
   FOR UPDATE
   )
   THEN
        
	UPDATE products 
	SET 
		previous_inventory_quantity = inventory_quantity,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = NOW(),
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku;
    
    UPDATE products 
	SET 
		inventory_quantity = quantity,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = NOW(),
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku;
    END IF;
    
    Select p.* from products as p
    WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku;
        
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateProductCron` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateProductCron`(`in_sku` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE products 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		products.sku = in_sku;
            
	SET SQL_SAFE_UPDATES = 1;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `updateProductStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `updateProductStatus`(`in_fby_id` VARCHAR(128), `in_sku` VARCHAR(128), `in_crn_name` VARCHAR(60), `in_crnid` VARCHAR(100), `in_time` DATETIME, `in_barcode` VARCHAR(128), `in_item_id` VARCHAR(128), `in_item_product_id` VARCHAR(128), `in_inventory_item_id` VARCHAR(128))
BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE products 
	SET 
		status = 1,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = in_time,
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
		AND sku = in_sku
		AND 1 = case when `in_barcode` <> '' AND `in_barcode` <> '0' then case when barcode = `in_barcode` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_id` <> ''  AND `in_item_id` <> '0' then case when item_id = `in_item_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_product_id` <> ''  AND `in_item_product_id` <> '0' then case when item_product_id = `in_item_product_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_inventory_item_id` <> ''  AND `in_inventory_item_id` <> '0' then case when inventory_item_id = `in_inventory_item_id` then 1 else 0 end else 1 end 
		AND status = 0;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_1_client_Delete` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_1_client_Delete`(`in_clientId` VARCHAR(1024))
BEGIN
	/*
		call channelconnector.`_1_client_Delete`(
			'Id123'
        );
    */
    DECLARE isExists TINYINT; 
    DECLARE in_ownerCode varchar(1024);
    
	SET in_clientId = LOWER(`in_clientId`);
    
    SET in_ownerCode = (
        SELECT LOWER(ownerCode) FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
        LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
         /*   
        UPDATE `channelconnector`.`_2_channel`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(ownerCode) = in_ownerCode 
			AND isActive = 1;    
           */ 
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT   
			clientId,
            `name`,
            ownerCode,
            CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn,
            1 as `isDeleted`
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId 
            ORDER BY `id` DESC
            LIMIT 1;

    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_1_client_Get` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_1_client_Get`(`in_clientId` VARCHAR(1024))
BEGIN
	/*
    
		call channelconnector.`_1_client_Get`(
			''
        );
        
		call channelconnector.`_1_client_Get`(
			'Id123'
        );
        
    */
   SET in_clientId = LOWER(`in_clientId`);
   
   IF(in_clientId IS NULL OR in_clientId ='')
   THEN
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			isActive = 1;
   
   ELSE
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId
			AND isActive = 1;
	END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_1_client_Post` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_1_client_Post`(`in_clientId` VARCHAR(1024), `in_name` VARCHAR(1024), `in_ownerCode` VARCHAR(1024))
BEGIN
	/*
		call channelconnector.`_1_client_Post`(			'12345',			'Test Name',            'Test Owner Code'        );
        
         call channelconnector.`_1_client_Get`(
			''
        );
        
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
		WHERE 
			LOWER(T.`clientId`) = LOWER(`in_clientId`) 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
        INSERT INTO `channelconnector`.`_1_client`
		(
			`clientId`,
			`name`,
			`ownerCode`,
			`isActive`
		)
		VALUES
		(
			in_clientId,
			in_name,
			in_ownerCode,
			1
		);
        
		call channelconnector.`_1_client_Get`(`in_clientId`);
 
    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_1_client_Put` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_1_client_Put`(`in_clientId` VARCHAR(1024), `in_name` VARCHAR(1024), `in_ownerCode` VARCHAR(1024))
BEGIN
	/*
		call channelconnector.`_1_client_Put`(
			'1234',
			'Test Name1',
            'Test Owner Code2'
        );
        
        call channelconnector.`_1_client_Get`(
			''
        );
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		call channelconnector.`_1_client_Post`(			`in_clientId`,			`in_name`,            `in_ownerCode`        );
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`name` = in_name,
			`ownerCode` = in_ownerCode,
            `modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		call channelconnector.`_1_client_Get`(`in_clientId`);

    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_channel_Delete` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_channel_Delete`(`in_channelId` INT(11))
BEGIN
	/*
		call channelconnector._2_channel_Get(1002);
        
        call channelconnector.`getShopifyUser`(1002);
    
		call channelconnector.`_2_channel_Delete`(1002);
        
        SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 1,
                `isEnabled` = 1,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = 1002 ;
		SET SQL_SAFE_UPDATES = 1;
        
		
    */
    DECLARE isExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
   
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel  
        WHERE 
			`channelId` = `in_channelId` 
            AND `isActive` = 1 
			LIMIT 1
    );
    
    /*
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    */
    
    IF(isDeletedExists = 1)
    THEN
		DELETE FROM channelconnector._2_channel 
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0;
    END IF;
    
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
		UPDATE `channelconnector`.`_2_channel` 
			SET 
				`isActive` = 0,
                `isEnabled` = 0,
				`modifiedOn` = NOW()
			WHERE
				`channelId` = `in_channelId` 
				AND `isActive` = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT 
				T1.`channelId` ,
				T1.`groupCode` ,
				T1.`currencyCode` ,
				T1.`ownerCode` ,
				T1.`channelCode`  ,
				T1.`channelName` ,
				T1.`domain`  ,
				T1.`username` ,
				T1.`password`  ,
				T1.`apiKey`  ,
				T1.`secret` ,
				T1.`token`  ,
				T1.`isActive`  ,
				T1.`isEnabled` ,
				CAST(T1.`createdOn` as CHAR) as createdOn,
				CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                1 as Deleted
		FROM
			`channelconnector`.`_2_channel` as T1
		WHERE
			T1.`channelId` = `in_channelId` 
		ORDER BY T1.`id` DESC
		LIMIT 1;
            
    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_channel_Get` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_channel_Get`(
	`in_channelId` VARCHAR(20)
)
BEGIN
	
	/*
		# 1st owner must be present in _1_Client table for the channel
        
        #Get by channed id
		call channelconnector._2_channel_Get(1010);
        
        call channelconnector.`getShopifyUser`('1010');
        
        #Get all
		call channelconnector._2_channel_Get('');
        
        call channelconnector._2_channel_Get('dummy-owner-code-001');
        
    */
   
	IF (in_channelId IS NOT NULL AND in_channelId <> '')
	THEN
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId,
                    T1.stockUpdate,
					T1.priceUpdate,
					T1.orderSync,
					T1.productPublish,
                    T1.`amazon_Role`,
					T1.`amazon_MarketPlaceID`,
					T1.`amazon_SellerID`,
					T1.amazon_region,
					T1.`platformCode`,
					T1.`platformName` ,
                    T1.`warehouseLocationId`
                    
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				T1.`channelId` = `in_channelId` 
				AND T1.`isActive` = 1
			ORDER BY T1.`channelId`;
		ELSE
			SELECT  DISTINCTROW
					T1.`channelId` ,
					T1.`groupCode` ,
					T1.`currencyCode` ,
					T1.`ownerCode` ,
					T1.`channelCode`  ,
					T1.`channelName` ,
					T1.`domain`  ,
					T1.`username` ,
					T1.`password`  ,
					T1.`apiKey`  ,
					T1.`secret` ,
					T1.`token`  ,
					#T1.`isActive`  ,
					T1.`isEnabled`,
					CAST(T1.`createdOn` as CHAR) as createdOn,
					CAST(T1.`modifiedOn` as CHAR) as modifiedOn,
                    CAST(T1.`orderSyncStartDate` as CHAR) as orderSyncStartDate,
                    T1.`compatibilityLevel`,
					T1.ebay_devid,
					T1.ebay_appid,
					T1.ebay_certid,
					T1.siteId,
					T1.stockUpdate,
					T1.priceUpdate,
					T1.orderSync,
					T1.productPublish,
                    T1.`amazon_Role`,
					T1.`amazon_MarketPlaceID`,
					T1.`amazon_SellerID`,
					T1.amazon_region,
					T1.`platformCode`,
					T1.`platformName` ,
                    T1.`warehouseLocationId`
                    
			FROM 
				`channelconnector`.`_2_channel` as T1
				INNER JOIN `channelconnector`.`_1_client` as T2 
					ON LOWER(T2.`ownerCode`) = LOWER(T1.`ownerCode`)
					AND T2.`isActive` = 1
			WHERE 
				 T1.`isActive` = 1
			ORDER BY T1.`channelId`;
               
		END IF;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_channel_Post` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_channel_Post`(
			`in_channelId` INT, 
			`in_groupCode` VARCHAR(256), 
			`in_currencyCode` VARCHAR(56), 
            `in_ownerCode` VARCHAR(256), 
            `in_channelCode` VARCHAR(256), 
            `in_channelName` VARCHAR(2048), 
            `in_domain` VARCHAR(2048), 
            `in_username` VARCHAR(2048), 
            `in_password` VARCHAR(2048), 
            `in_apiKey` VARCHAR(2048), 
            `in_secret` VARCHAR(2048),
            `in_token` VARCHAR(2048), 
            `in_isEnabled` INT, 
            `in_orderSyncStartDate` DATETIME,
			`in_ebaycompatibilityLevel` VARCHAR(128), 
			`in_ebaydevId` VARCHAR(256),
            `in_ebayappId` VARCHAR(256),
            `in_ebaycertId` VARCHAR(256),
            `in_ebaysiteId` VARCHAR(256),
            `in_stockUpdate` TINYINT,
            `in_orderSync` TINYINT,
            `in_productPublish` TINYINT,
            `in_priceUpdate` TINYINT,

			`in_amazon_Role` 			VARCHAR(128),
			`in_amazon_MarketPlaceID` 	VARCHAR(128),
			`in_amazon_SellerID` 		VARCHAR(128),
			`in_amazon_region`			VARCHAR(128),
			`in_platformCode` VARCHAR(256), 
            `in_platformName` VARCHAR(1048),
            `in_warehouseLocationId` VARCHAR(2048)
            
            
	)
BEGIN
	/*
		call channelconnector.`_2_channel_Post`(
						1111 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4',
            true,
            true,
            false,
            false
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
    */
    
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    #SET `in_orderSyncStartDate` = NULL;
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel AS T1 
        WHERE 
			T1.`channelId` = `in_channelId` 
			AND T1.isActive = 1 
		LIMIT 1
    );
    
    SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`ownerCode`) = LOWER(`in_ownerCode`) 
            AND T.isActive = 1 
		LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		  
        CALL channelconnector.`_2_channel_put`(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`,
			`in_stockUpdate` ,
            `in_orderSync` ,
            `in_productPublish` ,
            `in_priceUpdate`			,
            `in_amazon_Role` 			,
			`in_amazon_MarketPlaceID` 	,
			`in_amazon_SellerID` 		,
			`in_amazon_region`		    ,
			`in_platformCode`           , 
            `in_platformName`			,
            `in_warehouseLocationId`
            
        );
       
        
    ELSE
    IF (isClientExists = 0 OR isClientExists IS NULL)
    THEN
		SELECT 1 AS isErrorClientNotFound;
        
    ELSE IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
			   INSERT INTO `channelconnector`.`_2_channel`
				(
					`channelId` ,
					`groupCode` ,
					`currencyCode` ,
					`ownerCode` ,
					`channelCode`  ,
					`channelName` ,
					`domain`  ,
					`username` ,
					`password`  ,
					`apiKey`  ,
					`secret` ,
					`token`  ,
					`isEnabled` ,
                    `isActive`,
					`orderSyncStartDate` ,
                    `compatibilityLevel`,
					`ebay_devid`,
					`ebay_appid`,
					`ebay_certid`,
					`siteId`,
                    `stockUpdate`,
					`priceUpdate`,
					`orderSync`,
					`productPublish`,
					
                    `amazon_Role`,
					`amazon_MarketPlaceID`,
					`amazon_SellerID`,
					`amazon_region`	,
					`platformCode`  , 
					`platformName`  ,
                    `warehouseLocationId`
				)
				VALUES
				(
					`in_channelId` ,
					`in_groupCode` ,
					`in_currencyCode` ,
					`in_ownerCode` ,
					`in_channelCode`  ,
					`in_channelName` ,
					`in_domain`  ,
					`in_username` ,
					`in_password`  ,
					`in_apiKey`  ,
					`in_secret` ,
					`in_token`  ,
					`in_isEnabled` ,
                     1 ,
                    `in_orderSyncStartDate`,
                    `in_ebaycompatibilityLevel`,
					`in_ebaydevId`,
					`in_ebayappId`,
					`in_ebaycertId`,
					`in_ebaysiteId`,
					`in_stockUpdate` ,
					`in_orderSync` ,
					`in_productPublish` ,
					`in_priceUpdate`	,
					
                    `in_amazon_Role` 			,
					`in_amazon_MarketPlaceID` 	,
					`in_amazon_SellerID` 		,
					`in_amazon_region`		    ,
					`in_platformCode`           , 
					`in_platformName`           ,
                    `in_warehouseLocationId`
				);
				
				call channelconnector._2_channel_Get(`in_channelId`);
                
			END IF;
		END IF;
    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_channel_Put` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_channel_Put`(
			`in_channelId` INT, 
            `in_groupCode` VARCHAR(256), 
            `in_currencyCode` VARCHAR(56), 
            `in_ownerCode` VARCHAR(256), 
            `in_channelCode` VARCHAR(256), 
            `in_channelName` VARCHAR(2048), 
            `in_domain` VARCHAR(2048), 
            `in_username` VARCHAR(2048), 
            `in_password` VARCHAR(2048), 
            `in_apiKey` VARCHAR(2048), 
            `in_secret` VARCHAR(2048), 
            `in_token` VARCHAR(2048), 
            `in_isEnabled` INT, 
            `in_orderSyncStartDate` DATETIME,
			`in_ebaycompatibilityLevel` VARCHAR(128), 
			`in_ebaydevId` VARCHAR(256),
            `in_ebayappId` VARCHAR(256),
            `in_ebaycertId` VARCHAR(256), 
            `in_ebaysiteId` VARCHAR(256),
            `in_stockUpdate` TINYINT,
            `in_orderSync` TINYINT,
            `in_productPublish` TINYINT,
            `in_priceUpdate` TINYINT,

			`in_amazon_Role` 			VARCHAR(128),
			`in_amazon_MarketPlaceID` 	VARCHAR(128),
			`in_amazon_SellerID` 		VARCHAR(128),
			`in_amazon_region`			VARCHAR(128),
			`in_platformCode` VARCHAR(256), 
            `in_platformName` VARCHAR(1048),
            `in_warehouseLocationId` VARCHAR(2048)
)
BEGIN
	/*
		call channelconnector.`_2_channel_Put`(
						1030 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4',
             true,
            true,
            false,
            false
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
        
    */
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
		SET 
			`isActive` = 1,
			`isEnabled` = 1,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId`
			AND `isActive` = 0;
		SET SQL_SAFE_UPDATES = 1;
    END IF;
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel
        WHERE 
			`channelId` = `in_channelId`
			AND isActive = 1 
       LIMIT 1     
    );
    
	SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client
        WHERE 
			LOWER(`ownerCode`) = LOWER(`in_ownerCode`) 
            AND isActive = 1 
		LIMIT 1
    );
   
    
	IF (isClientExists = 0 OR isClientExists IS NULL)
	THEN
		SELECT 1 AS isErrorClientNotFound;
        
	ELSE IF (isExists = 0 OR isExists IS NULL)
	THEN
		CALL channelconnector.`_2_channel_post`
		(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`,
            `in_stockUpdate` ,
            `in_orderSync` ,
            `in_productPublish` ,
            `in_priceUpdate`,
            `in_amazon_Role` 			,
			`in_amazon_MarketPlaceID` 	,
			`in_amazon_SellerID` 		,
			`in_amazon_region`			,
			`in_platformCode`           , 
            `in_platformName`           ,
            `in_warehouseLocationId`
		);
        
	ELSE
		SET SQL_SAFE_UPDATES = 0;

		UPDATE `channelconnector`.`_2_channel`
		SET
			groupCode = `in_groupCode` ,
			currencyCode = `in_currencyCode` ,
			ownerCode = `in_ownerCode` ,
			channelCode = `in_channelCode`  ,
			channelName = `in_channelName` ,
			domain = `in_domain`  ,
			username = `in_username` ,
			`password` = `in_password`  ,
			apiKey = `in_apiKey`  ,
			secret = `in_secret` ,
			token = `in_token`  ,
			isEnabled = `in_isEnabled` ,
            orderSyncStartDate = `in_orderSyncStartDate`,
            compatibilityLevel = `in_ebaycompatibilityLevel`,
            ebay_devid = `in_ebaydevId`,
            ebay_appid = `in_ebayappId`,
            ebay_certid = `in_ebaycertId`,
            siteId = `in_ebaysiteId`,
			`stockUpdate` = `in_stockUpdate`,
			`priceUpdate` = `in_priceUpdate`,
			`orderSync`= `in_orderSync`,
			`productPublish` = `in_productPublish`,
            `amazon_Role` = `in_amazon_Role` ,
			`amazon_MarketPlaceID` = `in_amazon_MarketPlaceID` 	,
			`amazon_SellerID` = `in_amazon_SellerID` 		,
			`amazon_region`	 = `in_amazon_region`	,
			`platformCode`   =	`in_platformCode` , 
            `platformName` =  `in_platformName` ,
            `warehouseLocationId` =  `in_warehouseLocationId` ,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId` 
			AND isActive = 1;
		
		SET SQL_SAFE_UPDATES = 1;

		call channelconnector._2_channel_Get(`in_channelId`);
             
	END IF;
    END IF;
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_getIDforChannelByChannelName` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_getIDforChannelByChannelName`(`in_channelname` VARCHAR(200))
BEGIN
	
	
	/*
		call channelconnector.`_2_getIDforChannelByChannelName`('');
        
        call channelconnector.`_2_getIDforChannelByChannelName`('all');
    
		call channelconnector.`_2_getIDforChannelByChannelName`('shopify');
        
		call channelconnector.`_2_getIDforChannelByChannelName`('storeden');
        
    */
    IF(in_channelname = 'all' || in_channelname='')
    THEN
		SELECT 
			T1.*
		FROM
			channelconnector._2_channel T1
		WHERE
			isActive = 1
			AND isEnabled = 1;
    ELSE
		SET in_channelname = lower(concat('%',in_channelname,'%')); 
		SELECT 
			T1.*
		FROM
			channelconnector._2_channel T1
		WHERE
			(
			LOWER(domain) LIKE in_channelname
			OR LOWER(channelName) LIKE in_channelname
			)
			AND isActive = 1
			AND isEnabled = 1;
	END IF; 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_getUserIdForJob` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_2_getUserIdForJob`()
BEGIN
	/*
    
		call channelconnector.`_2_getUserIdForJob`();
        
		call channelconnector.`_2_getUserIdForJob`();
        
    */
	call channelconnector.`_2_getIDforChannelByChannelName`('shopify');
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_3_winston_logs_clean` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_3_winston_logs_clean`(`in_partition_key` DATE)
BEGIN
	/*
		Select * from `channelconnector`.`winston_logs` order 
        
		Call channelconnector._3_winston_logs_clean('2022-02-28');
                
    */
    SET SQL_SAFE_UPDATES = 0;
	DELETE
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		 CAST(`timestamp` as date)< CAST(`in_partition_key`as date)  ;
    SET SQL_SAFE_UPDATES = 1;    
    
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_3_winston_logs_get` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_3_winston_logs_get`(`in_partition_key` DATE, `in_operation_id` VARCHAR(255))
BEGIN
	/*
		Call channelconnector._3_winston_logs_get('2022-01-27','');
        
        Call channelconnector._3_winston_logs_get('2022-01-27','');
        
    */

	SELECT  
		`partition_key`,
		`operation_id`,
		`level`,
		`message`,
		`meta`,
        CAST(`timestamp` as char) as createdOn
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		`partition_key` = `in_partition_key`
        AND 1 = (case when `in_operation_id` = '' then 1 else case when `operation_id` = `in_operation_id` then 1 else 0 end end);
		


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_3_winston_logs_Post` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_3_winston_logs_Post`(`in_level` VARCHAR(255), `in_message` TEXT, `in_meta` TEXT, `in_operation_id` VARCHAR(255))
BEGIN

	INSERT INTO `channelconnector`.`winston_logs`
	( 
		`level`,
		`message`,
		`meta`,
		`operation_id`
	)
	VALUES
	(
		`in_level` ,
		`in_message`,
		`in_meta`  ,
		`in_operation_id`
	);
    
	SET SQL_SAFE_UPDATES = 0;

	DELETE FROM `channelconnector`.`winston_logs` 
	WHERE
		`timestamp` < DATE_ADD(NOW(), INTERVAL -7 DAY);



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_4_get_channel_from_group_code` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_4_get_channel_from_group_code`(
	`in_groupCode` VARCHAR(128)
)
BEGIN
	SELECT * 
    FROM channelconnector._2_channel
    WHERE groupCode = in_groupCode
    ;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_4_get_group_code` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`digispinuser`@`%` PROCEDURE `_4_get_group_code`()
BEGIN
SELECT distinct groupCode FROM channelconnector._2_channel;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 22:34:57
