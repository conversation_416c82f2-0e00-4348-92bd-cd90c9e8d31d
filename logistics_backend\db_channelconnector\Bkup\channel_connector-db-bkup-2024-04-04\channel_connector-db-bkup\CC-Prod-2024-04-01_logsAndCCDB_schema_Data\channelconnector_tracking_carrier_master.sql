-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `tracking_carrier_master`
--

DROP TABLE IF EXISTS `tracking_carrier_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracking_carrier_master` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` int DEFAULT NULL,
  `mirakl_carrier_code` varchar(256) DEFAULT NULL,
  `label` varchar(256) DEFAULT NULL,
  `tracking_url` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fby_user_id` (`fby_user_id`,`mirakl_carrier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tracking_carrier_master`
--

LOCK TABLES `tracking_carrier_master` WRITE;
/*!40000 ALTER TABLE `tracking_carrier_master` DISABLE KEYS */;
INSERT INTO `tracking_carrier_master` VALUES (1,37,'UPS','UPS','https://www.ups.com/fr/fr/services/tracking/information.page={trackingId}','2023-06-20 14:41:57','2023-12-22 15:50:07'),(2,37,'DHL','DHL','https://www.dhl.fr/exp-fr/dhl_express/suivi_expedition.html={trackingId}','2023-06-20 14:41:57','2023-12-22 15:50:02'),(3,37,'TNT IT','TNT Italy','http://www.tnt.it/tracking/Tracking.do={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:06'),(4,37,'GLS','GLS','https://gls-group.com/IT/it/servizi-online/ricerca-spedizioni={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:04'),(5,37,'FEDEX','FEDEX','https://www.fedex.com/it-it/tracking.html={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:03'),(6,37,'SDA','SDA','https://www.sda.it/wps/portal/Servizi_online/ricerca_spedizioni?locale=it={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:06'),(7,37,'BRT','BRT','https://www.brt.it/it/tracking={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:01'),(8,37,'RHENUS','RHENUS','https://www.rhenus.com/it/it/servizi/contract-logistics/track-trace/={trackingId}','2023-06-20 14:41:58','2023-12-22 15:50:04'),(9,37,'CMO','Colissimo','http://www.coliposte.fr/particulier/suivi_particulier.jsp?colispart={trackingId}','2023-06-20 14:41:59','2023-12-22 15:50:02'),(10,37,'CRO','Chronopost','http://www.chronopost.com?id={trackingId}','2023-06-20 14:41:59','2023-12-22 15:50:01'),(11,37,'DPD','DPD','https://www.dpd.com/tracking/(lang)/fr_BE={trackingId}','2023-06-20 14:41:59','2023-12-22 15:50:03'),(12,50,'swisspost','SwissPost','http://www.post.ch/swisspost-tracking?formattedParcelCodes={trackingId}','2023-06-20 14:52:14','2024-01-27 09:00:06'),(13,50,'dpd','DPD','https://tracking.dpd.de/status/en_US/parcel/{trackingId}','2023-06-20 14:52:14','2024-01-27 09:00:03'),(14,50,'dhl','DHL','https://www.dhl.com/ch-en/home/<USER>','2023-06-20 14:52:14','2024-01-27 09:00:02'),(15,50,'ups','UPS','https://www.ups.com/track?loc=fr_CH&requester=QUIC&tracknum={trackingId}/','2023-06-20 14:52:14','2024-01-27 09:00:07'),(16,50,'tnt','TNT','https://www.tnt.com/express/de_ch/site/shipping-tools/tracking.html?searchType=con&cons={trackingId}','2023-06-20 14:52:14','2024-01-27 09:00:06'),(17,50,'fiege','Fiege','https://www.fiege.com/services/freight-forwarding/','2023-06-20 14:52:14','2024-01-27 09:00:04'),(18,50,'LaPost_Colissimo','LaPoste Colissimo','https://www.laposte.fr/outils/suivre-vos-envois?code={trackingId}','2023-06-20 14:52:15','2024-01-27 09:00:04'),(19,50,'asendia','ASENDIA','https://tracking.asendia.com/tracking/{trackingId}','2023-06-20 14:52:15','2024-01-27 09:00:02'),(20,50,'chronopost','Chronopost','https://www.chronopost.fr/tracking-no-cms/suivi-page?listeNumerosLT={trackingId}','2023-06-20 14:52:15','2024-01-27 09:00:02'),(21,50,'Planzer','Planzer Pakete','https://planzer-paket.ch/en/receiving-parcels/','2023-06-20 14:52:15','2024-01-27 09:00:04'),(22,50,'quickpac','Quickpac','https://www.quickpac.ch/en/tracking/{trackingId}','2023-06-20 14:52:15','2024-01-27 09:00:06'),(23,50,'fedex','Fedex','https://www.fedex.com/fedextrack/?action=track&trackingnumber={trackingId}&cntry_code=ch&locale=de_ch','2023-06-20 14:52:15','2024-01-27 09:00:03'),(24,50,'postnord','Postnord','https://www.postnord.se/en/our-tools/track-and-trace?shipmentId={trackingId}','2023-06-20 14:52:16','2024-01-27 09:00:05'),(25,50,'gls','GLS','https://www.gls-pakete.de/en/parcel-tracking?trackingNumber={trackingId}','2023-09-14 12:02:29','2024-01-27 09:00:04'),(26,50,'dpd_fr','DPD FR','https://trace.dpd.fr/fr/trace/{trackingId}','2023-10-16 14:50:09','2024-01-27 09:00:03'),(27,50,'dpd_de','DPD DE','https://tracking.dpd.de/status/en_US/parcel/{trackingId}','2023-10-16 15:00:12','2024-01-27 09:00:03'),(28,50,'dpd_ch','DPD CH','https://www.dpdgroup.com/ch/mydpd/my-parcels/track?lang=en&parcelNumber={trackingId}','2023-10-16 15:00:14','2024-01-27 09:00:03'),(29,50,'dhl_express','DHL Express','https://www.dhl.com/ch-en/home/<USER>/tracking-express.html?submit=1&tracking-id={trackingId}','2023-10-16 16:50:10','2024-01-27 09:00:03'),(30,50,'post_nl','Post NL','https://tracking.postnl.nl/track-and-trace/{trackingId}-CH','2023-10-16 17:50:10','2024-01-27 09:00:04');
/*!40000 ALTER TABLE `tracking_carrier_master` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:26:06
